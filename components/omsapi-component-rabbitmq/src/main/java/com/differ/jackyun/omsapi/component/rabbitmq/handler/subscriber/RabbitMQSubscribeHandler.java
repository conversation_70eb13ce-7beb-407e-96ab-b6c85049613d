package com.differ.jackyun.omsapi.component.rabbitmq.handler.subscriber;

import com.differ.jackyun.omsapi.component.rabbitmq.core.GroupContext;
import com.differ.jackyun.omsapi.component.rabbitmq.RabbitMQParameter;
import com.differ.jackyun.omsapi.component.rabbitmq.handler.RabbitHandler;
import com.differ.jackyun.omsapi.component.rabbitmq.handler.SiteSubscribe;
import com.rabbitmq.client.Channel;
import org.springframework.amqp.core.Message;

import java.io.IOException;

/**
 * @Description MQ处理策略接口
 * <AUTHOR>
 * @Date 2021/3/5 16:30
 */
public interface RabbitMQSubscribeHandler extends RabbitHandler {

    /**
     * 接收消息
     * @param message
     * @param channel
     * @throws IOException
     */
    void onMessage(Message message, Channel channel) throws IOException;
}
