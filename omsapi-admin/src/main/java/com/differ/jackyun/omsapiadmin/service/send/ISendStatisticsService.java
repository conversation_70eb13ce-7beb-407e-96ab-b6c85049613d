package com.differ.jackyun.omsapiadmin.service.send;

import com.differ.jackyun.framework.component.basic.dto.JackYunResponse;
import com.differ.jackyun.omsapibase.data.core.PagingQueryResult;
import com.differ.jackyun.omsapibase.data.statistics.send.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 发货统计服务接口
 *
 * <AUTHOR>
 * @since 2023-01-04 4:58
 */
public interface ISendStatisticsService {

    /**
     * 查询发货失败全局统计信息
     *
     * @param date      日期
     * @param plat      平台值
     * @param pageIndex 页码
     * @param pageSize  页容量
     * @param excludeIgnore  是否排除被忽略的错误
     * @return 统计信息
     */
    JackYunResponse<Object> querySendFailKeyWordStatisticData(LocalDateTime date, Integer plat, Integer level, Integer pageIndex, Integer pageSize, Boolean excludeIgnore);

    /**
     * 查询指定会员、指定错误的会员级明细数据
     *
     * @param date      日期
     * @param errorId   错误id
     * @param pageIndex 页码
     * @param pageSize  页容量
     * @return 会员级明细数据
     */
    JackYunResponse<Object> querySendFailKeyWordStatisticByError(LocalDateTime date, Integer errorId, Integer level, Integer pageIndex, Integer pageSize);

    /**
     * 查询指定会员、指定错误的店铺级明细数据
     *
     * @param date      日期
     * @param errorId   错误id
     * @param user      吉客号
     * @param pageIndex 页码
     * @param pageSize  页容量
     * @return 店铺级明细数据
     */
    JackYunResponse<Object> querySendFailKeyWordStatisticUserByUser(LocalDateTime date, Integer errorId, String user, Integer pageIndex, Integer pageSize);

    /**
     * 查询指定会员、指定错误、指定店铺的明细数据
     *
     * @param date    日期
     * @param errorId 错误id
     * @param user    吉客号
     * @param shopId  店铺id
     * @return 网店订单id集合
     */
    List<String> querySendFailKeyWordStatisticDetail(LocalDateTime date, Integer errorId, String user, Long shopId);

    /**
     * 忽略指定错误
     *
     * @param errorId 错误id
     * @param user    操作人
     * @param reason  忽略原因
     * @return 是否操作成功
     */
    Boolean ignoreSendFailKeyWord(Long errorId, String user, String reason);

    /**
     * 查询被忽略的错误信息
     *
     * @param pageIndex 页码
     * @param pageSize  页容量
     * @return 被忽略的错误信息集合
     */
    JackYunResponse<Object> queryIgnoreSendFailKeyWord(Integer pageIndex, Integer pageSize);

    /**
     * 查询指定日期及其前一天的平台级发货错误量统计
     *
     * @param date 日期
     * @return
     */
    JackYunResponse<Object> queryTwoDayPlatSendFailData(LocalDateTime date);

    /**
     * 查询指定日期及其前一天的用户级发货错误量统计
     *
     * @param date 日期
     * @return
     */
    JackYunResponse<Object> queryTwoDayUserSendFailData(LocalDateTime date);

    /**
     * 重试统计
     *
     * @param date 日期
     * @return
     */
    JackYunResponse<Object> querySendFailRetryStatisticData(LocalDateTime date);

    /**
     * 平台不支持某物流统计
     *
     * @param date 日期
     * @return
     */
    JackYunResponse<Object> querySendLogisticsNotSupportStatisticData(LocalDateTime date);

}
