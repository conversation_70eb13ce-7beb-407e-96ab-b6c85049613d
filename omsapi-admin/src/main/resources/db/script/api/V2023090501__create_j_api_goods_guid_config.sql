CREATE TABLE IF NOT EXISTS `j_api_goods_guid_config` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `type` INT DEFAULT NULL COMMENT '类型',
  `plat_value` INT NOT NULL COMMENT '平台值',
  `class_name` VARCHAR(50) NOT NULL COMMENT '类名',
  `field_name` VARCHAR(50) NOT NULL COMMENT '字段名',
  `sort` INT NOT NULL COMMENT '排序',
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `INDEX_TYPE_PLAT_VALUE` (`type`,`plat_value`)
) ENGINE=INNODB DEFAULT CHARSET=utf8;