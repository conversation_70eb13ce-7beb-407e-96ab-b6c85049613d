package com.differ.jackyun.omsapibase.dao.jackyunmultidb.omsonline;

import com.differ.jackyun.framework.component.multidb.annotation.DFDatasourceSwitcher;
import com.differ.jackyun.omsapibase.data.open.oms.BusinessDataDeletedInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 业务回收站信息操作仓储
 *
 * <AUTHOR>
 * @date 2025-03-07 10:04
 */
@Repository
@DFDatasourceSwitcher
public class BusinessDeletedInfoDBSwitchMapper {

    @Autowired
    private BusinessDeletedInfoMapper businessDeletedInfoMapper;

    /**
     * 批量新增已删除业务信息
     *
     * @param memberName 会员名
     * @param entities   待新增实体
     * @return 结果
     */
    public boolean batchInsert(String memberName, List<BusinessDataDeletedInfo> entities) {
        return businessDeletedInfoMapper.batchInsert(entities) > 0;
    }

    /**
     * 新增已删除业务信息
     *
     * @param memberName 会员名
     * @param entity     待新增实体
     * @return 结果
     */
    public boolean singleInsert(String memberName, BusinessDataDeletedInfo entity) {
        return businessDeletedInfoMapper.singleInsert(entity) > 0;
    }

}
