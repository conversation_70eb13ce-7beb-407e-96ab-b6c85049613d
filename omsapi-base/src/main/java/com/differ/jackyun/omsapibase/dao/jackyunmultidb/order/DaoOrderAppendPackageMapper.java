package com.differ.jackyun.omsapibase.dao.jackyunmultidb.order;

import com.differ.jackyun.omsapibase.data.tradeorder.TradeOrderAppendPackageEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 订单多包裹接口(在基础dao层上封装，用于切库)
 *
 * <AUTHOR>
 * @since 2021-07-14 10:22
 */
@Mapper
@Repository
public interface DaoOrderAppendPackageMapper {

    /**
     * 查询该订单相关的待发货的追加包裹信息
     *
     * @param sysTradeIds 销售单id
     * @return 追加包裹信息
     */
    List<TradeOrderAppendPackageEntity> queryTradeWaitingAppendPackage(@Param("sysTradeIds") List<Long> sysTradeIds, @Param("status") int status);

    /**
     * 修改追加包裹发货状态
     *
     * @param sysTradeIds    销售单id
     * @param logisticNoList 单号
     * @param status         状态
     */
    void setAppendPackageSyncStatus(@Param("sysTradeIds") List<Long> sysTradeIds, @Param("logisticNoList") List<String> logisticNoList, @Param("status") int status);

    /**
     * 修改追加包裹发货状态
     *
     * @param sysTradeIds    销售单id
     * @param logisticNoList 单号
     */
    void setAppendPackageCancel(@Param("sysTradeIds") List<Long> sysTradeIds, @Param("logisticNoList") List<String> logisticNoList);

    /**
     * 批量插入
     *
     * @param dataList 数据列表
     */
    void batchInsert(@Param("dataList") List<TradeOrderAppendPackageEntity> dataList);

    /**
     * 根据网店订单id查询该订单相关的待发货的追加包裹信息
     *
     * @param tradeIdList 网店订单id
     * @return 追加包裹信息
     */
    List<TradeOrderAppendPackageEntity> queryTradeWaitingAppendPackageBySourceTradeId(@Param("tradeIds") List<Long> tradeIdList, @Param("statusList") List<Integer> statusList);

    /**
     * 根据网店订单id查询该订单相关的待发货的追加包裹信息
     *
     * @param tradeIdList 网店订单id
     * @return 追加包裹信息
     */
    List<TradeOrderAppendPackageEntity> queryCompensateWaitingAppendPackageBySourceTradeId(@Param("tradeIds") List<Long> tradeIdList);

    /**
     * 不存在则插入
     *
     * @param dataList 数据列表
     */
    Integer batchInsertIfNotExist(@Param("dataList") List<TradeOrderAppendPackageEntity> dataList);


    /**
     * 修改追加包裹发货状态：当前状态值为特定值时不修改
     *
     * @param dataList    数据列表
     * @param status      状态
     * @param checkStatus 不用更新的状态
     */
    void updateSyncStatusIfNotEquals(@Param("dataList") List<TradeOrderAppendPackageEntity> dataList, @Param("status") int status, @Param("checkStatus") int checkStatus);

}
