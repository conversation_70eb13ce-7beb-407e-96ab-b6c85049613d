package com.differ.jackyun.omsapibase.dao.jackyunmultidb.platwebsite;

import com.differ.jackyun.framework.component.multidb.annotation.DFDatasourceSwitcher;
import com.differ.jackyun.omsapibase.data.platwebsite.PlatWebSiteEntity;
import com.differ.jackyun.omsapibase.data.platwebsite.WebsiteCommand;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description 平台网点信息接口（用于切库）
 * <AUTHOR>
 * @Date 2023/8/8 14:25
 */
@Repository
@DFDatasourceSwitcher
public class ApiWebSiteDBMapper {
    /**
     * dao层接口对象
     */
    @Autowired
    private WebSiteMapper webSiteMapper;

    /**
     * 根据编号删除平台网点信息
     *
     * @param memberName 会员名
     * @param businessNo 平台网点信息编号
     */
    public void deleteWebSiteByBusinessNo(String memberName, Long businessNo) {
        this.webSiteMapper.deleteAllByBusinessNo(businessNo);
    }

    /**
     * 加载网点信息
     *
     * @param memberName 会员名
     * @param commands   请求插入参数列表
     */
    public void add(String memberName, List<PlatWebSiteEntity> commands) {
        this.webSiteMapper.add(commands);
    }

    /**
     * 根据平台网点编号和平台店铺id删除平台网点信息
     *
     * @param memberName    吉客号
     * @param businessNo 平台网点信息实体
     * @param platShopId 平台店铺Id
     */
    public void deleteWebSiteByBusinessNoAndPlatShopId(String memberName, Long businessNo, String platShopId) {
        this.webSiteMapper.deleteByPlatShopIdAndBusinessNo(businessNo, platShopId);
    }

    /**
     * 新增
     *
     * @param memberName    吉客号
     * @param webSiteEntity 平台网点信息实体
     */
    public void addPlatWebSite(String memberName, PlatWebSiteEntity webSiteEntity) {
        this.webSiteMapper.addSingle(webSiteEntity);
    }

    /**
     * 更新
     *
     * @param memberName    吉客号
     * @param webSiteEntity 平台网点信息实体
     */
    public void updatePlatWebSite(String memberName, PlatWebSiteEntity webSiteEntity) {
        this.webSiteMapper.updateByPlatShopId(webSiteEntity);
    }

    /**
     * 根据网点编号信息查询
     *
     * @param memberName 吉客号
     * @param command    网点查询条件
     * @return List
     */
    public List<PlatWebSiteEntity> select(String memberName, WebsiteCommand command) {
        return this.webSiteMapper.select(command);
    }

    /**
     * 查询总条数
     *
     * @param command command 业务网点信息查询参数
     * @return 返回条数
     */
    public int getNumRecord(String memberName, WebsiteCommand command) {
        return this.webSiteMapper.getNumRecord(command);
    }
}
