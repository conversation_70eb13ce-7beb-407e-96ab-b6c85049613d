package com.differ.jackyun.omsapibase.dao.jackyunmultidb.shop;

import com.differ.jackyun.omsapibase.data.shopconf.entity.ConfShopEntity;
import com.differ.jackyun.omsapibase.data.shopconf.entity.ConfShopLogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 店铺dao接口(j_conf_shop)。
 *
 * <AUTHOR>
 * @since 2020-05-12 14:14:50
 */
@Mapper
@Repository
public interface ConfShopMapper {

    /**
     * 清空店铺授权信息。
     *
     * @param confShop 店铺实体
     * @return 影响的记录数
     */
    int cleanAuthorizationInfoByChannelId(ConfShopEntity confShop);


    /**
     * 更新店铺授权信息
     *
     * @param shopId               店铺Id
     * @param sessionKeyExpireTime 授权到期时间
     * @return 影响行数
     */
    int updateConfigShopAuthInfo(@Param("shopId") Long shopId, @Param("sessionKeyExpireTime") LocalDateTime sessionKeyExpireTime);

    /**
     * 更新店铺授权信息
     *
     * @param shopId     店铺Id
     * @param authStatus 授权状态
     * @return 影响行数
     */
    int updateShopStatus(@Param("shopId") Long shopId, @Param("authStatus") int authStatus);

    /**
     * 查询，根据主键
     *
     * @param shopId 店铺Id
     * @return 影响行数
     */
    ConfShopEntity getByShopId(@Param("shopId") Long shopId);

    /**
     * 新增店铺配置
     *
     * @param confShop 店铺配置
     * @return 影响行数
     */
    int insertShopConfig(ConfShopEntity confShop);

    /**
     * 根据主键更新店铺配置
     *
     * @param confShop 店铺配置
     * @return 影响行数
     */
    int updateByPrimaryKey(ConfShopEntity confShop);

    /**
     * 保存店铺token
     * @param shopId 店铺ID
     * @param shopToken 店铺token
     * @return
     */
    int saveShopToken(@Param("shopId") Long shopId,@Param("shopToken") String shopToken);
}
