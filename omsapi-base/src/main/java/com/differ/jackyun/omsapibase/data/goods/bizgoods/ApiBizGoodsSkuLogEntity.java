package com.differ.jackyun.omsapibase.data.goods.bizgoods;

import com.differ.jackyun.omsapibase.data.core.BaseEntity;

import java.util.Date;

/**
 * 平台商品日志
 *
 * <AUTHOR>
 * @since 2020-06-16 13:54
 */
public class ApiBizGoodsSkuLogEntity extends BaseEntity {
    // region 属性

    /**
     * 平台商品id
     */
    private String platGoodsId;
    /**
     * 平台规格id
     */
    private String platSkuId;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 日志内容
     */
    private String logDetail;
    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 平台商品唯一主键
     */
    private String platGoodsGuid;

    // endregion

    // region 属性方法

    public String getPlatGoodsId() {
        return platGoodsId;
    }

    public void setPlatGoodsId(String platGoodsId) {
        this.platGoodsId = platGoodsId;
    }

    public String getPlatSkuId() {
        return platSkuId;
    }

    public void setPlatSkuId(String platSkuId) {
        this.platSkuId = platSkuId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getLogDetail() {
        return logDetail;
    }

    public void setLogDetail(String logDetail) {
        this.logDetail = logDetail;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public String getPlatGoodsGuid() {
        return platGoodsGuid;
    }

    public void setPlatGoodsGuid(String platGoodsGuid) {
        this.platGoodsGuid = platGoodsGuid;
    }

// endregion

    // region 其他方法

    /**
     * 快速构建
     *
     * @param platGoodsId 平台商品id
     * @param platSkuId   平台规格id
     * @param userId      用户id
     * @param userName    用户名称
     * @param logDetail   日志
     * @return
     */
    public static ApiBizGoodsSkuLogEntity make(String platGoodsId, String platSkuId, String userId, String userName,
                                               String logDetail) {
        ApiBizGoodsSkuLogEntity apiBizGoodsSkuLogEntity = new ApiBizGoodsSkuLogEntity();
        apiBizGoodsSkuLogEntity.setPlatGoodsId(platGoodsId);
        apiBizGoodsSkuLogEntity.setPlatSkuId(platSkuId);
        apiBizGoodsSkuLogEntity.setUserId(userId);
        apiBizGoodsSkuLogEntity.setUserName(userName);
        apiBizGoodsSkuLogEntity.setLogDetail(logDetail);
        return apiBizGoodsSkuLogEntity;
    }

    /**
     * 快速构建
     *
     * @param platGoodsId 平台商品id
     * @param platSkuId   平台规格id
     * @param userId      用户id
     * @param userName    用户名称
     * @param logDetail   日志
     * @return
     */
    public static ApiBizGoodsSkuLogEntity make(String platGoodsId, String platSkuId, String userId, String userName,
                                               String logDetail, String platGoodsGuid) {
        ApiBizGoodsSkuLogEntity apiBizGoodsSkuLogEntity = new ApiBizGoodsSkuLogEntity();
        apiBizGoodsSkuLogEntity.setPlatGoodsId(platGoodsId);
        apiBizGoodsSkuLogEntity.setPlatSkuId(platSkuId);
        apiBizGoodsSkuLogEntity.setUserId(userId);
        apiBizGoodsSkuLogEntity.setUserName(userName);
        apiBizGoodsSkuLogEntity.setLogDetail(logDetail);
        apiBizGoodsSkuLogEntity.setPlatGoodsGuid(platGoodsGuid);
        return apiBizGoodsSkuLogEntity;
    }

    /**
     * 快速构建
     *
     * @param operateCaption 操作标题
     * @param platGoodsGuid  平台商品唯一主键
     * @param platGoodsId    平台商品id
     * @param platSkuId      平台规格id
     * @param userId         用户id
     * @param userName       用户名称
     * @param logDetail      日志
     * @return 返回结果对象
     */
    public static ApiBizGoodsSkuLogEntity make(String operateCaption, String platGoodsGuid, String platGoodsId, String platSkuId, String userId, String userName, String logDetail) {
        ApiBizGoodsSkuLogEntity apiBizGoodsSkuLogEntity = new ApiBizGoodsSkuLogEntity();
        apiBizGoodsSkuLogEntity.setPlatGoodsId(platGoodsId);
        apiBizGoodsSkuLogEntity.setPlatSkuId(platSkuId);
        apiBizGoodsSkuLogEntity.setUserId(userId);
        apiBizGoodsSkuLogEntity.setUserName(userName);
        apiBizGoodsSkuLogEntity.setLogDetail(String.format("【%s】%s", operateCaption, logDetail));
        apiBizGoodsSkuLogEntity.setPlatGoodsGuid(platGoodsGuid);
        return apiBizGoodsSkuLogEntity;
    }

    // endregion
}