package com.differ.jackyun.omsapibase.data.order.send;

import com.differ.jackyun.omsapibase.infrastructure.enums.goods.OrderGoodsSendStatusEnum;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> hjl
 * @version V1.0
 * @Project: oms-api
 * @Package com.differ.jackyun.omsapibase.data.order.batchsend
 * @Description: 发货所需的发货表信息
 * @date Date : 2019年06月10日 11:46
 */
public class GoodsDeliveryDtoForSend implements Serializable {
    // region 属性

    /**
     * 发货表id
     */
    private Integer id;
    /**
     * 系统订单号
     */
    private Long sysTradeId;
    /**
     * 网店订单货品id
     */
    private Long subTradeId;
    /**
     * 平台子订单号
     */
    private String subOrderNo;
    /**
     * 网店订单货品数量
     */
    private BigDecimal sellCount;
    /**
     * 物流公司id
     */
    private Long logisticId;
    /**
     * 物流单号
     */
    private String logisticPostId;
    /**
     * 本次出库数量
     */
    private BigDecimal stockCount;
    /**
     * 已发货数量
     */
    private BigDecimal deliveryCount;
    /**
     * 发货状态枚举
     */
    private OrderGoodsSendStatusEnum synStatus;
    /**
     * 追加物流多包裹状态枚举
     */
    private OrderGoodsSendStatusEnum additionSendStatus;
    /**
     * 触发方式
     */
    private Byte triggerWay;
    /**
     * 仓库id
     */
    private String warehouseCode;

    // endregion

    // region 属性方法  订单货品表
    /**
     * 平台商家编码
     */
    private String platGoodsId;
    /**
     * 平台子商家编码
     */
    private String platSkuId;
    /**
     * 商家编码
     */
    private String outerId;
    /**
     * 子商家编码
     */
    private String outerSkuId;
    /**
     * 交易编号
     */
    private String tradeGoodsNo;
    /**
     * 网店订单号
     */
    private Long tradeId;
    /**
     * 网店订单货品退款状态
     */
    private String refundStatus;
    /**
     * 网单规格
     */
    private String goodsSpec;
    /**
     * 是否是赠品
     */
    private byte isGift;
    /**
     * 序列号
     */
    private String serialNo;

    // endregion

    // region 属性方法 发货表

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getSysTradeId() {
        return sysTradeId;
    }

    public void setSysTradeId(Long sysTradeId) {
        this.sysTradeId = sysTradeId;
    }

    public Long getSubTradeId() {
        return subTradeId;
    }

    public void setSubTradeId(Long subTradeId) {
        this.subTradeId = subTradeId;
    }

    public String getSubOrderNo() {
        return subOrderNo;
    }

    public void setSubOrderNo(String subOrderNo) {
        this.subOrderNo = subOrderNo;
    }

    public BigDecimal getSellCount() {
        return sellCount;
    }

    public void setSellCount(BigDecimal sellCount) {
        this.sellCount = sellCount;
    }

    public Long getLogisticId() {
        return logisticId;
    }

    public void setLogisticId(Long logisticId) {
        this.logisticId = logisticId;
    }

    public String getLogisticPostId() {
        return logisticPostId;
    }

    public void setLogisticPostId(String logisticPostId) {
        this.logisticPostId = logisticPostId;
    }

    public BigDecimal getStockCount() {
        return stockCount;
    }

    public void setStockCount(BigDecimal stockCount) {
        this.stockCount = stockCount;
    }

    public BigDecimal getDeliveryCount() {
        return deliveryCount;
    }

    public void setDeliveryCount(BigDecimal deliveryCount) {
        this.deliveryCount = deliveryCount;
    }

    public OrderGoodsSendStatusEnum getSynStatus() {
        return synStatus;
    }

    public void setSynStatus(OrderGoodsSendStatusEnum synStatus) {
        this.synStatus = synStatus;
    }

    public String getPlatGoodsId() {
        return platGoodsId;
    }

    public void setPlatGoodsId(String platGoodsId) {
        this.platGoodsId = platGoodsId;
    }

    public String getPlatSkuId() {
        return platSkuId;
    }

    public void setPlatSkuId(String platSkuId) {
        this.platSkuId = platSkuId;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public String getOuterSkuId() {
        return outerSkuId;
    }

    public void setOuterSkuId(String outerSkuId) {
        this.outerSkuId = outerSkuId;
    }

    public Long getTradeId() {
        return tradeId;
    }

    public void setTradeId(Long tradeId) {
        this.tradeId = tradeId;
    }

    public String getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(String refundStatus) {
        this.refundStatus = refundStatus;
    }

    public String getGoodsSpec() {
        return goodsSpec;
    }

    public void setGoodsSpec(String goodsSpec) {
        this.goodsSpec = goodsSpec;
    }

    public byte getIsGift() {
        return isGift;
    }

    public void setIsGift(byte isGift) {
        this.isGift = isGift;
    }

    public Byte getTriggerWay() {
        return triggerWay;
    }

    public void setTriggerWay(Byte triggerWay) {
        this.triggerWay = triggerWay;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public OrderGoodsSendStatusEnum getAdditionSendStatus() {
        return additionSendStatus;
    }

    public void setAdditionSendStatus(OrderGoodsSendStatusEnum additionSendStatus) {
        this.additionSendStatus = additionSendStatus;
    }

    public String getTradeGoodsNo() {
        return tradeGoodsNo;
    }

    public void setTradeGoodsNo(String tradeGoodsNo) {
        this.tradeGoodsNo = tradeGoodsNo;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    // endregion
}
