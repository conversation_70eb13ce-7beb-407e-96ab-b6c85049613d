package com.differ.jackyun.omsapibase.domain.dataproxy.worker;

import com.differ.jackyun.omsapibase.data.businessworker.BusinessWorkerEntity;
import com.differ.jackyun.omsapibase.domain.dataproxy.core.BaseDataProxy;
import com.differ.jackyun.omsapibase.domain.dataproxy.core.DataCacheKeyEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.BusinessWorkerTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.utils.LocalConfig;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 业务服务缓存代理类
 *
 * <AUTHOR>
 * @since 2020-07-07 13:26
 */
@Component
public class BusinessWorkerDataProxy extends BaseDataProxy<BusinessWorkerEntity> {
    // region 构造器

    public BusinessWorkerDataProxy() {
        this.cacheKey = DataCacheKeyEnum.BUSINESS_WORKER_INFO.getCaption();
        this.isSplitCacheKey = true;
        this.hashItemType = BusinessWorkerEntity.class;
    }

    // endregion

    // region 获取任务信息

    /**
     * 获取任务信息
     *
     * @param type     工作任务类型
     * @param hashKeys hash键
     * @return
     */
    public List<BusinessWorkerEntity> getBusinessWorkerInfo(BusinessWorkerTypeEnum type, List<String> hashKeys) {
        return this.cacher.hashGet(this.formatCacheKey(LocalConfig.get().getGroupId(), type.getCode()), hashKeys, type.getBusinessWorkerEntityClass());
    }

    // endregion

    // region 删除任务信息

    /**
     * 删除任务信息
     *
     * @param type     工作任务类型
     * @param hashKeys hash键
     * @return
     */
    public void removeBusinessWorkerInfo(BusinessWorkerTypeEnum type, List<String> hashKeys) {
        this.cacher.hashRemove(this.formatCacheKey(LocalConfig.get().getGroupId(), type.getCode()), hashKeys.toArray(new String[hashKeys.size()]));
    }

    // endregion

    // region 添加任务信息

    /**
     * 添加任务信息
     *
     * @param type                     工作任务类型
     * @param businessWorkerEntityList 工作任务实体
     */
    public void insertBusinessWorkerInfo(BusinessWorkerTypeEnum type, List<BusinessWorkerEntity> businessWorkerEntityList) {
        this.cacher.hashSync(this.formatCacheKey(LocalConfig.get().getGroupId(), type.getCode()), false, businessWorkerEntityList);
    }

    // endregion
}