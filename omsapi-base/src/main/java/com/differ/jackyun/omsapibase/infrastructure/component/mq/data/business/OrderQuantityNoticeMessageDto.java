package com.differ.jackyun.omsapibase.infrastructure.component.mq.data.business;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 订购量消息实体
 * online拷贝过来
 * <AUTHOR>
 * @since 2021-05-19 13:41
 */
public class OrderQuantityNoticeMessageDto implements Serializable {
    // region 属性

    /**
     * 会员名
     */
    private String memberName;
    /**
     * 版本号（后期扩展用）
     */
    private String version = "v1";
    /**
     * 订购量通知时间
     */
    private Date noticeTime;
    /**
     * 订购量通知
     */
    private List<OrderQuantityNoticeDto> openOrderQuantityRequestDtoList;

    // endregion

    // region 属性方法

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getVersion() {
        return version;
    }

    public Date getNoticeTime() {
        return noticeTime;
    }

    public void setNoticeTime(Date noticeTime) {
        this.noticeTime = noticeTime;
    }

    public List<OrderQuantityNoticeDto> getOpenOrderQuantityRequestDtoList() {
        return openOrderQuantityRequestDtoList;
    }

    public void setOpenOrderQuantityRequestDtoList(List<OrderQuantityNoticeDto> openOrderQuantityRequestDtoList) {
        this.openOrderQuantityRequestDtoList = openOrderQuantityRequestDtoList;
    }

    // endregion
}