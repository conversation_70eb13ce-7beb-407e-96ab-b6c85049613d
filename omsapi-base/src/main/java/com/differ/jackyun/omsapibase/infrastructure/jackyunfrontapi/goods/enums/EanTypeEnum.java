package com.differ.jackyun.omsapibase.infrastructure.jackyunfrontapi.goods.enums;

import com.differ.jackyun.omsapibase.infrastructure.enums.core.EnumItemCacheManager;
import com.differ.jackyun.omsapibase.infrastructure.enums.core.INameValueEnum;
import com.differ.jackyun.omsapibase.infrastructure.jackyunfrontapi.enums.JackyunFrontAPICategoryEnum;

/**
 * @description 编码类型
 * <AUTHOR>
 * @date 2022-01-11 18:24
 */
public enum EanTypeEnum implements INameValueEnum {

    //region 枚举
    SPU_EAN(0,"商品编码"),
    SKU_EAN(1,"商品子编码即规格编码")
    ;
    //endregion

    // region 构造器

    EanTypeEnum(Integer eanValue, String eanCaption) {
        this.eanValue = eanValue;
        this.eanCaption = eanCaption;
    }

    // endregion

    // region 变量
    /**
     * 编码类型字符标识
     */
    private Integer eanValue;

    /**
     * 编码类型字符说明
     */
    private String eanCaption;
    // endregion

    // region 方法


    public Integer getEanValue() {
        return eanValue;
    }

    public void setEanValue(Integer eanValue) {
        this.eanValue = eanValue;
    }

    public String getEanCaption() {
        return eanCaption;
    }

    public void setEanCaption(String eanCaption) {
        this.eanCaption = eanCaption;
    }

    @Override
    public Object getValue() {
        return this.eanValue;
    }

    @Override
    public String getCaption() {
        return this.eanCaption;
    }

    /**
     * 重写toString方法。
     *
     * @return
     */
    @Override
    public String toString() {
        return this.getValue().toString();
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static EanTypeEnum create(String value) {
        return EnumItemCacheManager.create(value,EanTypeEnum.class, EanTypeEnum::values);
    }

    // endregion
}
