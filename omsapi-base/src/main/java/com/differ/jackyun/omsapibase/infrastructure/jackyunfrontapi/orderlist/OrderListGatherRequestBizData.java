package com.differ.jackyun.omsapibase.infrastructure.jackyunfrontapi.orderlist;

import com.differ.jackyun.omsapibase.infrastructure.openapi.core.BaseOpenAPIRequestBizData;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 开放接口请求业务数据(网店订单汇总)
 *
 * <AUTHOR>
 * @since 2019/6/19 10:23
 */
public class OrderListGatherRequestBizData extends BaseOpenAPIRequestBizData {
    //region 变量

    /**
     * 时间类型(0、下单时间 1、付款时间)
     */
    private Byte timeType;
    /**
     * 开始时间
     */
    private LocalDateTime timeBegin;
    /**
     * 结束时间
     */
    private LocalDateTime timeEnd;

    /**
     * 下单时间开始
     */
    private LocalDateTime createTimeBegin;
    /**
     * 下单时间结束
     */
    private LocalDateTime createTimeEnd;
    /**
     * 付款开始时间
     */
    private LocalDateTime payTimeBegin;
    /**
     * 付款结束时间
     */
    private LocalDateTime payTimeEnd;
    /**
     * 销售渠道
     */
    private List<Long> shopIdList;
    /**
     * 销售渠道
     */
    private Long shopId;
    /**
     * 网店订单交易状态
     */
    private List<String> tradeStatusList;
    /**
     * 发货状态
     */
    private List<Integer> synStatusList;
    /**
     * 处理状态
     */
    private List<Integer> curStatusList;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 规格名称
     */
    private String goodsSpec;
    /**
     * 货品编号
     */
    private String goodsNo;
    /**
     * 总页数
     */
    private Integer pageSize;
    /**
     * 当前页
     */
    private Integer pageIndex;
    /**
     * 分组类型
     */
    private Byte groupType;
    /**
     * offset
     */
    private Integer offset;

    //endregion

    //region 方法

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public LocalDateTime getCreateTimeBegin() {
        return createTimeBegin;
    }

    public void setCreateTimeBegin(LocalDateTime createTimeBegin) {
        this.createTimeBegin = createTimeBegin;
    }

    public LocalDateTime getCreateTimeEnd() {
        return createTimeEnd;
    }

    public void setCreateTimeEnd(LocalDateTime createTimeEnd) {
        this.createTimeEnd = createTimeEnd;
    }

    public List<String> getTradeStatusList() {
        return tradeStatusList;
    }

    public void setTradeStatusList(List<String> tradeStatusList) {
        this.tradeStatusList = tradeStatusList;
    }

    public List<Integer> getSynStatusList() {
        return synStatusList;
    }

    public void setSynStatusList(List<Integer> synStatusList) {
        this.synStatusList = synStatusList;
    }

    public List<Integer> getCurStatusList() {
        return curStatusList;
    }

    public void setCurStatusList(List<Integer> curStatusList) {
        this.curStatusList = curStatusList;
    }

    public List<Long> getShopIdList() {
        return shopIdList;
    }

    public void setShopIdList(List<Long> shopIdList) {
        this.shopIdList = shopIdList;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsSpec() {
        return goodsSpec;
    }

    public void setGoodsSpec(String goodsSpec) {
        this.goodsSpec = goodsSpec;
    }

    public Byte getGroupType() {
        return groupType;
    }

    public void setGroupType(Byte groupType) {
        this.groupType = groupType;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Byte getTimeType() {
        return timeType;
    }

    public void setTimeType(Byte timeType) {
        this.timeType = timeType;
    }

    public LocalDateTime getTimeBegin() {
        return timeBegin;
    }

    public void setTimeBegin(LocalDateTime timeBegin) {
        this.timeBegin = timeBegin;
    }

    public LocalDateTime getTimeEnd() {
        return timeEnd;
    }

    public void setTimeEnd(LocalDateTime timeEnd) {
        this.timeEnd = timeEnd;
    }

    public LocalDateTime getPayTimeBegin() {
        return payTimeBegin;
    }

    public void setPayTimeBegin(LocalDateTime payTimeBegin) {
        this.payTimeBegin = payTimeBegin;
    }

    public LocalDateTime getPayTimeEnd() {
        return payTimeEnd;
    }

    public void setPayTimeEnd(LocalDateTime payTimeEnd) {
        this.payTimeEnd = payTimeEnd;
    }

    //endregion
}
