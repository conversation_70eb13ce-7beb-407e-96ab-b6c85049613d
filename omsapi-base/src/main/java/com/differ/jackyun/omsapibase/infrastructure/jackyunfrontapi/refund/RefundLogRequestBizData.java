package com.differ.jackyun.omsapibase.infrastructure.jackyunfrontapi.refund;

import com.differ.jackyun.omsapibase.infrastructure.openapi.core.BaseOpenAPIRequestBizData;

import java.io.Serializable;

/**
 * 开放接口请求业务数据(退款单日志查询)
 *
 * <AUTHOR>
 * @since 2019/6/19 10:23
 */
public class RefundLogRequestBizData extends BaseOpenAPIRequestBizData {
    //region 变量
    /**
     * 订单tradeId
     */
    private Long tradeId;
    //endregion

    //region 方法

    public Long getTradeId() {
        return tradeId;
    }

    public void setTradeId(Long tradeId) {
        this.tradeId = tradeId;
    }

    //endregion
}
