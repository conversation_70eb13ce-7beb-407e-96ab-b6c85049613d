package com.differ.jackyun.omsapibase.infrastructure.jackyunfrontapi.send;

import com.differ.jackyun.omsapibase.infrastructure.openapi.core.BaseOpenAPIResponseBizData;
import com.differ.jackyun.omsapibase.infrastructure.utils.ValidateResultList;

/**
 * 重新订单发货返回实体类
 *
 * <AUTHOR> hjl
 * @date Date : 2019年11月18日 14:49
 */
public class OrderReSendResponseBizData extends BaseOpenAPIResponseBizData {

    // region 属性

    /**
     * 订单发货失败结果
     */
    private ValidateResultList validateResultList;

    // endregion

    // region 属性方法

    public ValidateResultList getValidateResultList() {
        return validateResultList;
    }

    public void setValidateResultList(ValidateResultList validateResultList) {
        this.validateResultList = validateResultList;
    }


    // endregion

}
