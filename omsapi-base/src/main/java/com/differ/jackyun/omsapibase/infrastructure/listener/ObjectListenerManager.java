/**
 * Copyright(C) 2017 Hangzhou Differsoft Co., Ltd. All rights reserved.
 */
package com.differ.jackyun.omsapibase.infrastructure.listener;

import java.util.HashSet;
import java.util.Set;

/**
 * 通用事件管理器
 *
 * @since 2017年12月12日 下午5:17:13
 * <AUTHOR>
 */
public class ObjectListenerManager {
    /**
     * 注册的事件集合
     */
    private final Set<ObjectListenerImpl> listeners = new HashSet<>();

    /**
     * 注册事件
     *
     * @param listener
     */
    public void addListener(ObjectListenerImpl listener) {
        this.listeners.add(listener);
    }

    /**
     * 移除事件
     *
     * @param listener
     */
    public void removeListener(ObjectListenerImpl listener) {
        this.listeners.remove(listener);
    }

    /**
     * 触发事件
     *
     * @param eventTag
     *            事件标记(自已定义)
     * @param arg
     *            事件参数
     */
    public void fire(String eventTag, Object arg) {
        if (this.listeners.size() == 0) {
            return;
        }

        for (ObjectListenerImpl listener : this.listeners) {
            listener.doEvent(eventTag, arg);
        }
    }
}
