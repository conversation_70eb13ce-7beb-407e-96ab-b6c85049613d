package com.differ.jackyun.omsapibase.infrastructure.openapi.ass;

import com.differ.jackyun.omsapibase.infrastructure.openapi.core.BaseOpenAPIResponseBizData;

import java.util.List;

/**
 * 售后单商品匹配响应数据
 * <AUTHOR>
 * @since 2021/9/6 10:31
 */
public class AfterOrderMatchEventResponseBizData extends BaseOpenAPIResponseBizData {

    private List<AfterOrderGoodsMatchResults> afterOrderGoodsMatchResultsList;

    public List<AfterOrderGoodsMatchResults> getAfterOrderGoodsMatchResultsList() {
        return afterOrderGoodsMatchResultsList;
    }

    public void setAfterOrderGoodsMatchResultsList(List<AfterOrderGoodsMatchResults> afterOrderGoodsMatchResultsList) {
        this.afterOrderGoodsMatchResultsList = afterOrderGoodsMatchResultsList;
    }

    public static class AfterOrderGoodsMatchResults{
        private Long businessId;
        private Long sysGoodsId;
        private Long sysSpecId;
        private Boolean fit;

        public Long getBusinessId() {
            return businessId;
        }

        public void setBusinessId(Long businessId) {
            this.businessId = businessId;
        }

        public Long getSysGoodsId() {
            return sysGoodsId;
        }

        public void setSysGoodsId(Long sysGoodsId) {
            this.sysGoodsId = sysGoodsId;
        }

        public Long getSysSpecId() {
            return sysSpecId;
        }

        public void setSysSpecId(Long sysSpecId) {
            this.sysSpecId = sysSpecId;
        }

        public Boolean getFit() {
            return fit;
        }

        public void setFit(Boolean fit) {
            this.fit = fit;
        }
    }
}
