package com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.getdeductorder;

import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.core.BasePolyAPIResponseBizData;

import java.util.List;

/**
 * 获取拣货单中的订单（返回参数）
 *
 * <AUTHOR>
 * @since 2019-11-27
 */
public class GetDeductOrderResponseBizData extends BasePolyAPIResponseBizData {
    //region  变量

    /**
     * 拣货单号
     */
    private String pickNo;
    /**
     * 是否有下一页
     */
    private int isHasNextPage;
    /**
     * 订单号集合
     */
    private List<String> orders;

    //endregion

    //region 方法

    public String getPickNo() {
        return pickNo;
    }

    public void setPickNo(String pickNo) {
        this.pickNo = pickNo;
    }

    public int getIsHasNextPage() {
        return isHasNextPage;
    }

    public void setIsHasNextPage(int isHasNextPage) {
        this.isHasNextPage = isHasNextPage;
    }

    public List<String> getOrders() {
        return orders;
    }

    public void setOrders(List<String> orders) {
        this.orders = orders;
    }

    //endregion

}
