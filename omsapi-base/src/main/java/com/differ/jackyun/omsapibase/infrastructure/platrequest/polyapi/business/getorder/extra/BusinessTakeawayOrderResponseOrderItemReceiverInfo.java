package com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.getorder.extra;

import com.alibaba.fastjson.annotation.JSONField;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.core.OpenAPIMeta;

import java.math.BigDecimal;

/**
 * 外卖收件人信息。
 *
 * <AUTHOR>
 * @since 2019-12-03 15:16:58
 */
public class BusinessTakeawayOrderResponseOrderItemReceiverInfo {
    //region 字段

    /**
     * 收件人姓名。
     */
    @OpenAPIMeta(remark = "收件人姓名", isRequired = 1, demo = "张三", limit = "32")
    @JSONField(ordinal = 1)
    private String name;

    /**
     * 收件人电话。
     */
    @OpenAPIMeta(remark = "收件人电话", isRequired = 1, demo = "***********", limit = "32")
    @JSONField(ordinal = 2)
    private String phone;

    /**
     * 收件人地址。
     */
    @OpenAPIMeta(remark = "收件人地址", isRequired = 1, demo = "尚坤生态园A211", limit = "32")
    @JSONField(ordinal = 3)
    private String address;

    /**
     * 收件人区/县。
     */
    @OpenAPIMeta(remark = "收件人区/县", isRequired = 1, demo = "西湖区", limit = "32")
    @JSONField(ordinal = 4)
    private String area;

    /**
     * 收件人城市。
     */
    @OpenAPIMeta(remark = "收件人城市", isRequired = 1, demo = "杭州市", limit = "32")
    @JSONField(ordinal = 5)
    private String city;

    /**
     * 收件人省份。
     */
    @OpenAPIMeta(remark = "收件人省份", isRequired = 1, demo = "浙江省", limit = "32")
    @JSONField(ordinal = 6)
    private String province;

    /**
     * 经度。
     */
    @OpenAPIMeta(remark = "经度", isRequired = 1, demo = "38.693203", limit = "32")
    @JSONField(ordinal = 7)
    private BigDecimal longitude;

    /**
     * 纬度。
     */
    @OpenAPIMeta(remark = "纬度", isRequired = 1, demo = "115.281708", limit = "32")
    @JSONField(ordinal = 8)
    private BigDecimal latitude;

    //endregion

    //region 属性方法

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    //endregion
}
