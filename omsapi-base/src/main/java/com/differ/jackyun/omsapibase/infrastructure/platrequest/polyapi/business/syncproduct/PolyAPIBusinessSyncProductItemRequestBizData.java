package com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.syncproduct;

import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.core.BasePolyAPIRequestBizData;

import java.util.List;

/**
 * 同步货品商品绑定-请求参数。
 *
 * <AUTHOR>
 * @since 2020-02-11 10:47:34
 */
public class PolyAPIBusinessSyncProductItemRequestBizData extends BasePolyAPIRequestBizData {
    //region 变量

    /**
     * 商品信息集合。
     */
    private List<GoodsInfo> goodInfos;

    //endregion

    //region 属性方法

    public List<GoodsInfo> getGoodInfos() {
        return goodInfos;
    }

    public void setGoodInfos(List<GoodsInfo> goodInfos) {
        this.goodInfos = goodInfos;
    }

    //endregion

    /**
     * 商品信息。
     */
    public static class GoodsInfo {
        //region 变量

        /**
         * 商品ID。
         */
        private String platProductId;

        /**
         * 商品SKU ID。
         */
        private String skuId;

        /**
         * 货品集合。
         */
        private List<SKU> skus;

        //endregion

        //region 属性方法

        public String getPlatProductId() {
            return platProductId;
        }

        public void setPlatProductId(String platProductId) {
            this.platProductId = platProductId;
        }

        public String getSkuId() {
            return skuId;
        }

        public void setSkuId(String skuId) {
            this.skuId = skuId;
        }

        public List<SKU> getSkus() {
            return skus;
        }

        public void setSkus(List<SKU> skus) {
            this.skus = skus;
        }

        //endregion
    }

    /**
     * 货品信息。
     */
    public static class SKU {
        //region 变量

        /**
         * 货品编码。
         */
        private String outItemId;

        /**
         * 货品数量。
         */
        private int num;

        //endregion

        //region 属性方法

        public String getOutItemId() {
            return outItemId;
        }

        public void setOutItemId(String outItemId) {
            this.outItemId = outItemId;
        }

        public int getNum() {
            return num;
        }

        public void setNum(int num) {
            this.num = num;
        }

        //endregion
    }
}


