/*
 * Copyright(C) 2017 Hangzhou Differsoft Co., Ltd. All rights reserved.
 */
package com.differ.jackyun.omsapibase.infrastructure.utils;

import com.differ.jackyun.omsapibase.infrastructure.component.Loger;
import com.differ.jackyun.omsapibase.infrastructure.enums.ApplicationTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.LogTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 日志辅助类，已废弃，请使用 LogAdapter
 *
 * <AUTHOR>
 * @since 2017年12月11日 下午3:29:27
 */
@Component
@Deprecated
public class LogUtils {
    /**
     * 构造器。
     *
     * @param loger 日志记录接口
     */
    @Autowired
    public LogUtils(Loger loger) {
        LogUtils.loger = loger;
    }

    /**
     * 日志记录对象。
     */
    private static Loger loger;

    /**
     * 写入日志。
     *
     * @param type    日志类别
     * @param content 日志内容
     */
    public static void write(LogTypeEnum type, String content) {
        loger.write(type, ApplicationTypeEnum.COMMON, null, content, null, null, null);
    }

    /**
     * 写入日志。
     *
     * @param type    日志类别
     * @param app     应用程序类别
     * @param content 日志内容
     */
    public static void write(LogTypeEnum type, ApplicationTypeEnum app, String content) {
        loger.write(type, app, null, content, null, null, null);
    }

    /**
     * 写入日志。
     *
     * @param type      日志类别
     * @param app       应用程序类别
     * @param errorCode 错误码
     * @param content   日志内容
     */
    public static void write(LogTypeEnum type, ApplicationTypeEnum app, String errorCode, String content) {
        loger.write(type, app, errorCode, content, null, null, null);
    }

    /**
     * 写入日志。
     *
     * @param type      日志类别
     * @param app       应用程序类别
     * @param errorCode 错误码
     * @param content   日志内容
     * @param contextID 上下文编号
     */
    public static void write(LogTypeEnum type, ApplicationTypeEnum app, String errorCode, String content, Long contextID) {
        loger.write(type, app, errorCode, content, null, contextID, null);
    }

    /**
     * 写入日志。
     *
     * @param type      日志类别
     * @param app       应用程序类别
     * @param errorCode 错误码
     * @param content   日志内容
     * @param username  用户名
     * @param contextID 上下文编号
     * @param logTime   日志生成时间
     */
    public static void write(LogTypeEnum type, ApplicationTypeEnum app, String errorCode, String content, String username, Long contextID,
                             LocalDateTime logTime) {
        loger.write(type, app, errorCode, content, username, contextID, logTime);

    }

    //region 封装便捷方法

    /**
     * 写入调试日志。
     *
     * @param content 日志内容
     */
    public static void writeTrace(String content) {
        loger.write(LogTypeEnum.TRACE, ApplicationTypeEnum.COMMON, null, content, null, null, null);
    }

    /**
     * 写入调试日志。
     *
     * @param app     应用程序类别
     * @param content 日志内容
     */
    public static void writeTrace(ApplicationTypeEnum app, String content) {
        loger.write(LogTypeEnum.TRACE, app, null, content, null, null, null);
    }

    /**
     * 写入错误日志。
     *
     * @param content 日志内容
     */
    public static void writeError(String content) {
        loger.write(LogTypeEnum.ERROR, ApplicationTypeEnum.COMMON, null, content, null, null, null);
    }

    /**
     * 写入错误日志。
     *
     * @param app     应用程序类别
     * @param content 日志内容
     */
    public static void writeError(ApplicationTypeEnum app, String content) {
        loger.write(LogTypeEnum.ERROR, app, null, content, null, null, null);
    }

    /**
     * 写入超级警告。
     *
     * @param content 日志内容
     */
    public static void writeSuperWarning(String content) {
        loger.write(LogTypeEnum.SUPERWARNING, ApplicationTypeEnum.COMMON, null, content, null, null, null);
    }

    /**
     * 写入超级警告。
     *
     * @param app     应用程序类别
     * @param content 日志内容
     */
    public static void writeSuperWarning(ApplicationTypeEnum app, String content) {
        loger.write(LogTypeEnum.SUPERWARNING, app, null, content, null, null, null);
    }

    /**
     * 写入风控。
     *
     * @param content 日志内容
     */
    public static void writeRiskControl(String content) {
        loger.write(LogTypeEnum.RISKCONTROL, ApplicationTypeEnum.COMMON, null, content, null, null, null);
    }

    /**
     * 写入风控。
     *
     * @param app     应用程序类别
     * @param content 日志内容
     */
    public static void writeRiskControl(ApplicationTypeEnum app, String content) {
        loger.write(LogTypeEnum.RISKCONTROL, app, null, content, null, null, null);
    }

    //endregion
}
