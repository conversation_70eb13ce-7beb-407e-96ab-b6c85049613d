package com.differ.jackyun.omsapibase.infrastructure.utils;

import com.differ.jackyun.framework.component.utils.encrypt.JackYunMd5Utils;
import com.differ.jackyun.omsapibase.data.shopconf.entity.ShopConfigEntity;
import com.differ.jackyun.omsapibase.domain.dataproxy.shopconf.ShopConfDataProxy;
import com.differ.jackyun.omsapibase.infrastructure.component.thread.JTask;
import com.differ.jackyun.omsapibase.infrastructure.enums.ApplicationTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.ConfigKeyEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.LogTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.SecurityAuditOperationEnum;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.core.PolyAPIPlatEnum;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.jni.Local;

import java.time.LocalDateTime;
import java.util.List;
import java.util.SortedMap;
import java.util.TreeMap;

/**
 * 安全审计中心工具类
 *
 * <AUTHOR>
 * @since 2021/1/4  下午4:11:35
 */
public class SecurityAuditCenterUtils {
    //region 对象
    /**
     * 锁对象
     */
    private final static Object LOCK = new Object();

    /**
     * 线程池对象
     */
    private static volatile JTask task;

    /**
     * 请求地址
     */
    private final static String ORDER_URL = "/jsac/sac-logsync/logsync/order";

    //endregion

    //region 公共静态方法

    /**
     * @param orderIdsList   订单编号集合
     * @param accountIdsList 平台或吉客号店铺id集合
     * @param plat           菠萝派平台枚举
     */
    public static void orderSearchLogList(PolyAPIPlatEnum plat, List<String> orderIdsList, List<String> accountIdsList, UserClientInfo clientInfo) {
        String platCodeByPolyPlat = getPlatCodeByPolyPlat(plat);
        String systemType = YunConfigUtils.getDBConfig(ConfigKeyEnum.ACCOUNT_SECURITYAUDITSENTER_SYSTYPE_VOP);
        // orderIds分组处理，架构那里最多orderIds为50个，所以分组处理
        List<List<String>> partition = Lists.partition(orderIdsList, 50);
        partition.forEach(orderIds -> {
            orderSearchLog(systemType, platCodeByPolyPlat,
                    CoreUtils.toJoinString(orderIds, ","), CoreUtils.toJoinString(accountIdsList, ","),
                    SecurityAuditOperationEnum.BATCH_QUERY_ORDERLIST.getOperationValue(), clientInfo);
        });

    }

    /**
     * 安全审计中心上传订单详情日志 查询orderId为单个不进行特殊处理
     *
     * @param orderIds   订单编号集合(英文逗号分隔)
     * @param accountIds 平台或吉客号店铺id集合(英文逗号分隔)
     * @param plat       菠萝派平台枚举
     */
    public static void orderSearchLogDetail(PolyAPIPlatEnum plat, String orderIds, String accountIds, UserClientInfo clientInfo) {
        String platCodeByPolyPlat = getPlatCodeByPolyPlat(plat);
        String systemType = YunConfigUtils.getDBConfig(ConfigKeyEnum.ACCOUNT_SECURITYAUDITSENTER_SYSTYPE_VOP);

        orderSearchLog(systemType, platCodeByPolyPlat, orderIds, accountIds, SecurityAuditOperationEnum.SINGLE_QUERY_ORDER.getOperationValue(), clientInfo);
    }

    //endregion

    //region 私有方法

    /**
     * 初始化线程池
     */
    private static void initTask() {
        if (null == task) {
            synchronized (LOCK) {
                if (null == task) {
                    task = JTask.createInstance(10);
                }
            }
        }
    }

    /**
     * 安全审计中心上传
     *
     * @param systemType   系统类别
     * @param platFormCode 平台编码
     * @param orderIds     订单编号集合(英文逗号分隔)
     * @param accountIds   平台或吉客号店铺id集合(英文逗号分隔)
     * @param operation    订单操作类型(1列表，5单个)
     */
    private static void orderSearchLog(String systemType, String platFormCode, String orderIds, String accountIds, String operation, UserClientInfo clientInfo) {
        initTask();
        try {
            task.runTask(null, (o) -> {
                try {
                    LocalDateTime now = LocalDateTime.now();
                    String result = syncLog(systemType, platFormCode, orderIds, accountIds, operation, clientInfo);
                    if (LocalConfig.get().getIsTest() || YunConfigUtils.getSecuritySuccessLogSwitch()) {
                        BusinessLogUtils.write(clientInfo.getContextId(),
                                String.format("[omsapi安全审计中心]日志上传完成，请求: %s;%s;%s;%s;%s,返回: %s,耗时%s", systemType,
                                        platFormCode, orderIds, operation, JsonUtils.toJson(clientInfo), result,
                                        CoreUtils.diffTime(now)));
                        LogUtils.write(LogTypeEnum.TRACE, ApplicationTypeEnum.BUSINESS, String.format("[omsapi安全审计中心]上传完成，请求: %s;%s;%s;%s;%s,返回: %s,耗时%s", systemType, platFormCode, orderIds, operation, JsonUtils.toJson(clientInfo), result,
                                CoreUtils.diffTime(now)));
                    }

                    if (Strings.isNullOrEmpty(result) || !result.contains("\"code\":200")) {
                        BusinessLogUtils.write(clientInfo.getContextId(),
                                String.format("[omsapi安全审计中心]日志上传失败，请求: %s;%s;%s;%s;%s,返回: %s,耗时%s", systemType, platFormCode,
                                        orderIds, operation, JsonUtils.toJson(clientInfo), result,
                                        CoreUtils.diffTime(now)));
                    }
                } catch (Exception e) {
                    LogUtils.writeError(ApplicationTypeEnum.COMMON, "[omsapi安全审计中心]上传异常：" + CoreUtils.exceptionToString(e));
                }
                return null;
            });
        } catch (Exception e) {
            LogUtils.writeError(ApplicationTypeEnum.COMMON, "[omsapi安全审计中心]异步上传异常：" + CoreUtils.exceptionToString(e));
        }
    }

    /**
     * 根据菠萝派平台枚举获取两个平台日志标识
     *
     * @param plat 平台标识《systemType，platFormCode》
     * @return 返回
     *//*
    private static Pair<String, String> getPairPlatCodeByPolyPlat(PolyAPIPlatEnum plat) {
        String systemType;
        String platFormCode;
        systemType = YunConfigUtils.getDBConfig(ConfigKeyEnum.ACCOUNT_SECURITYAUDITSENTER_SYSTYPE_VOP);
        switch (plat) {
            case BUSINESS_WPHMP:
                 platFormCode = PolyAPIPlatEnum.BUSINESS_WPHMP.getPlatEName();
                 break;
            case BUSINESS_KSXD:
                 platFormCode = PolyAPIPlatEnum.BUSINESS_KSXD.getPlatEName();
                 break;
            case BUSINESS_FXG:
                 platFormCode = PolyAPIPlatEnum.BUSINESS_FXG.getPlatEName();
                 break;
            case BUSINESS_LUBAN:
                 platFormCode = PolyAPIPlatEnum.BUSINESS_LUBAN.getPlatEName();
                 break;
            default:
                return null;
        }

        return Pair.of(systemType, platFormCode);
    }*/

    /**
     * 获取对应的平台值
     *
     * @param plat
     * @return
     */
    private static String getPlatCodeByPolyPlat(PolyAPIPlatEnum plat) {
        String platFormCode;

        switch (plat) {
            case BUSINESS_WPHMP:
                platFormCode = PolyAPIPlatEnum.BUSINESS_WPHMP.getPlatEName();
                break;
            case BUSINESS_KSXD:
                platFormCode = PolyAPIPlatEnum.BUSINESS_KSXD.getPlatEName();
                break;
            case BUSINESS_FXG:
                platFormCode = PolyAPIPlatEnum.BUSINESS_FXG.getPlatEName();
                break;
            case BUSINESS_LUBAN:
                platFormCode = PolyAPIPlatEnum.BUSINESS_LUBAN.getPlatEName();
                break;
            default:
                return null;
        }

        return platFormCode;
    }

    /**
     * 安全审计中心签名类
     *
     * @param secret    密钥
     * @param sortedMap 参数集合
     * @return 签名
     */
    private static String makeSign(String secret, SortedMap<String, String> sortedMap) throws Exception {
        // 构建待签名的字符串。
        StringBuilder beSignText = new StringBuilder(secret);
        sortedMap.forEach((k, v) -> {
            if (!Strings.isNullOrEmpty(v)) {
                beSignText.append(k).append(v);
            }
        });
        beSignText.append(secret);

        return JackYunMd5Utils.toMd5(beSignText.toString());
    }

    /**
     * 订单查询上传日志
     *
     * @param systemType   系统类别
     * @param platFormCode 平台编码
     * @param orderIds     订单编号集合(英文逗号分隔)
     * @param accountIds   平台或吉客号店铺id集合(英文逗号分隔)
     * @param operation    订单操作类型(1列表，5单个)
     * @return 上传日志结果返回
     */
    private static String syncLog(String systemType, String platFormCode, String orderIds, String accountIds, String operation, UserClientInfo clientInfo) throws Exception {
        SortedMap<String, String> map = new TreeMap<>();
        map.put("appkey", YunConfigUtils.getDBConfig(ConfigKeyEnum.ACCOUNT_SECURITYAUDITSENTER_APPKEY));
        map.put("systemType", systemType);
        map.put("timestamp", String.valueOf(System.currentTimeMillis()));
        map.put("operationTime", String.valueOf(System.currentTimeMillis()));
        map.put("sync", "0");

        //平台编码值
        map.put("platformCode", platFormCode);
        //isv账号
        map.put("userId", clientInfo.getMemberId() + "-" + clientInfo.getUserId());
        //客户端ip
        map.put("userIp", clientInfo.getClientIp());
        //客户端mac地址
        map.put("deviceId", clientInfo.getMacAddress());
        //平台或吉客号店铺id集合
        map.put("accountIds", accountIds);
        // 1 查询订单列表  5.查订单单个
        map.put("operation", operation);
        //导出必填，暂时为空
        map.put("fileMd5", "");
        //网店订单号集合，逗号分隔
        map.put("orderIds", orderIds);
        //当前url
        map.put("url", clientInfo.getUrl());

        if (StringUtils.isNotBlank(clientInfo.getUserAgent()) && (platFormCode.equals(PolyAPIPlatEnum.BUSINESS_LUBAN.getPlatEName()) || platFormCode.equals(PolyAPIPlatEnum.BUSINESS_FXG.getPlatEName()))) {

            ExtraInfo extraInfo = new ExtraInfo();
            extraInfo.setDeviceType(clientInfo.getDeviceType());
            extraInfo.setRefer(clientInfo.getRefer());
            extraInfo.setUserAgent(clientInfo.getUserAgent());
            String sendInfos = "[{'name': 'order_id', 'encrypted' : false}]";
            extraInfo.setSendInfos(sendInfos);
            String extraStr = JsonUtils.toJson(extraInfo);

            //封装额外信息
            map.put("extra", extraStr);
        }


        //平台授权authSessionKey
        if (StringUtils.isNoneBlank(clientInfo.getAccessToken())) {
            map.put("accessToken", clientInfo.getAccessToken());
        }

        //签名
        map.put("sign", SecurityAuditCenterUtils.makeSign(YunConfigUtils.getDBConfig(ConfigKeyEnum.ACCOUNT_SECURITYAUDITSENTER_APPSECRET), map));

        //请求
        return HttpUtils.postData(YunConfigUtils.getDBConfig(ConfigKeyEnum.ACCOUNT_SECURITYAUDITSENTER_URL) + ORDER_URL, map, 10000);
    }
    //endregion

    //region 用户客户端信息内部类

    /**
     * 用户客户端信息内部类
     */
    public static class UserClientInfo {

        /**
         * 用户请求URL
         */
        private String url;
        /**
         * 用户mac地址
         */
        private String macAddress;
        /**
         * 客户端ip
         */
        private String clientIp;

        /**
         * 吉客号Id
         */
        private String memberId;

        /**
         * 吉客号用户id
         */
        private String userId;
        /**
         * 日志id
         */
        private Long contextId;

        /**
         * 设备类型（iOS / Android / Windows）
         */
        private String deviceType;

        /**
         * 用户代理
         */
        private String userAgent;

        /**
         * 引用
         */
        private String refer;

        /**
         * 平台授权token
         *
         * @return
         */
        private String accessToken;

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public String getClientIp() {
            return clientIp;
        }

        public void setClientIp(String clientIp) {
            this.clientIp = clientIp;
        }

        public String getMemberId() {
            return memberId;
        }

        public void setMemberId(String memberId) {
            this.memberId = memberId;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public Long getContextId() {
            return contextId;
        }

        public void setContextId(Long contextId) {
            this.contextId = contextId;
        }

        public String getDeviceType() {
            return deviceType;
        }

        public void setDeviceType(String deviceType) {
            this.deviceType = deviceType;
        }

        public String getUserAgent() {
            return userAgent;
        }

        public void setUserAgent(String userAgent) {
            this.userAgent = userAgent;
        }

        public String getRefer() {
            return refer;
        }

        public void setRefer(String refer) {
            this.refer = refer;
        }

        public String getAccessToken() {
            return accessToken;
        }

        public void setAccessToken(String accessToken) {
            this.accessToken = accessToken;
        }
    }

    /**
     * 请求审核日志上的额外信息内部类
     */
    public static class ExtraInfo {

        /**
         * 设备类型（iOS / Android / Windows）
         */
        private String deviceType;

        /**
         * 用户代理
         */
        private String userAgent;

        /**
         * 引用
         */
        private String refer;

        /**
         * 订单信息明细格式
         *
         * @return
         */
        private String sendInfos;


        public String getDeviceType() {
            return deviceType;
        }

        public void setDeviceType(String deviceType) {
            this.deviceType = deviceType;
        }

        public String getUserAgent() {
            return userAgent;
        }

        public void setUserAgent(String userAgent) {
            this.userAgent = userAgent;
        }

        public String getRefer() {
            return refer;
        }

        public void setRefer(String refer) {
            this.refer = refer;
        }

        public String getSendInfos() {
            return sendInfos;
        }

        public void setSendInfos(String sendInfos) {
            this.sendInfos = sendInfos;
        }
    }
    //endregion
}
