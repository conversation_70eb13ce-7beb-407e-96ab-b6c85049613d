package com.differ.jackyun.omsapibase.repository.errorcode;

import com.differ.jackyun.omsapibase.data.errorcode.ApiErrorCodeMsgEntity;
import com.differ.jackyun.omsapibase.data.errorcode.ApiErrorCodeQueryCommand;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 错误码提示参数
 *
 * <AUTHOR>
 * @date 2023/6/26 18:03
 */
@Mapper
@Repository
public interface ApiErrorCodeMsgRepository {

    /**
     * 根据错误码查询
     *
     * @param bizType    业务类型
     * @param errorCodes 错误码集合
     * @return 结果
     */
    List<ApiErrorCodeMsgEntity> getDataByErrorCode(@Param("bizType") String bizType, @Param("errorCodes") List<String> errorCodes);

    /**
     * 分页查询系统错误码列表
     * @param queryCommand 错误码查询参数
     * @return 错误码列表
     */
    List<ApiErrorCodeMsgEntity> listApiErrorCodeMsgEntity(@Param(value = "queryCommand") ApiErrorCodeQueryCommand queryCommand);

    /**
     * 分页查询系统错误码数量
     * @param queryCommand 错误码查询参数
     * @return 错误码列表
     */
    int countApiErrorCodeMsgEntity(@Param(value = "queryCommand") ApiErrorCodeQueryCommand queryCommand);

    /**
     * 删除系统错误码
     *
     * @param ids 主键集合
     * @return 影响行数
     */
    int delete(@Param("ids") List<Long> ids);

    /**
     * 新增系统错误码
     * @param apiErrorCodeMsgEntity
     * @return 影响行数
     */
    int insert(ApiErrorCodeMsgEntity apiErrorCodeMsgEntity);

    /**
     * 更新系统错误码
     * @param apiErrorCodeMsgEntity 错误码实体
     * @return 影响行数
     */
    int update(ApiErrorCodeMsgEntity apiErrorCodeMsgEntity);

    /**
     * 根据主键查询系统错误吗
     * @param ids 主键列表
     * @return
     */
    List<ApiErrorCodeMsgEntity> selectByPrimaryKeys(@Param(value = "ids") List<Long> ids);

    /**
     * 根据业务类型查询系统错误吗
     * @param bizType 业务类型
     * @return
     */
    List<ApiErrorCodeMsgEntity> getDataByBizType(@Param("bizType") String bizType);

}
