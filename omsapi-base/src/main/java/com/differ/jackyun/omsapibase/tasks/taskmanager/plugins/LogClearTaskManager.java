/*
 * Copyright(C) 2017 Hangzhou Differsoft Co., Ltd. All rights reserved.
 */

package com.differ.jackyun.omsapibase.tasks.taskmanager.plugins;

import com.differ.jackyun.omsapibase.data.operation.dblog.DeleteByConditionCommand;
import com.differ.jackyun.omsapibase.data.operation.servicerunstatus.DeleteProfileByDateBeforeCommand;
import com.differ.jackyun.omsapibase.infrastructure.enums.LogTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.utils.CoreUtils;
import com.differ.jackyun.omsapibase.service.operation.IDbLogService;
import com.differ.jackyun.omsapibase.service.operation.IOperatorLogService;
import com.differ.jackyun.omsapibase.service.operation.IServiceRunStatusService;
import com.differ.jackyun.omsapibase.tasks.taskmanager.BaseTaskManagerFactory;
import com.differ.jackyun.omsapibase.tasks.taskmanager.core.BaseTaskManager;
import com.differ.jackyun.omsapibase.tasks.taskmanager.enums.ServiceCategoriesEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 定期清理日志、记录型数据管理器
 * 本服务仅在Global服务中开启。
 *
 * <AUTHOR>
 * @since 2018-05-09  15:42:11
 */
@Component
public class LogClearTaskManager extends BaseTaskManager {

    // region 构造器

    public LogClearTaskManager() {
        // 设置运行频率。
        this.runFrequency = 60;
        // 设置服务类目。
        this.serviceCategory = ServiceCategoriesEnum.LOGCLEAR;
        //设置当前任务管理器名称。
        this.taskManagerName = "定期清理日志";
    }

    // endregion

    // region 变量

    @Autowired
    private IServiceRunStatusService serviceRunStatusService;

    @Autowired
    private IDbLogService serviceDbLog;

    @Autowired
    private IOperatorLogService operatorLogService;

    /**
     * 删除此天数前的调试日志
     */
    private static final int TRACELOG_DAYBEFORE_NUM = 3;

    /**
     * 删除此天数前的警告日志
     */
    private static final int WARNINGLOG_DAYBEFORE_NUM = 3;

    /**
     * 删除此天数前的错误日志
     */
    private static final int ERRORLOG_DAYBEFORE_NUM = 10;

    /**
     * 删除此天数前的超级警告日志
     */
    private static final int SUPERWARNINGLOG_DAYBEFORE_NUM = 15;

    /**
     * 删除此天数前的服务运行记录
     */
    private static final int SERVICERUNSTATUSPROFILE_DAYBEFORE_NUM = 7;

    /**
     * 删除此天数前的操作查询日志
     */
    private static final long OPTLOG_DAYBEFORE_NUM = 30;

    /**
     * 上次执行时间
     */
    private LocalDateTime lastTime = null;

    // endregion

    // region ITaskManager

    /**
     * 开始工作
     */
    @Override
    public void work() {
        this.doWork("定期清理日志和记录型数据管理器", () -> {
            LocalDateTime dtStart = LocalDateTime.now();
            this.lastTime = LocalDateTime.now();
            StringBuilder resultSb = new StringBuilder();
            int numSuccess = 0;

            BaseTaskManagerFactory.get().getServiceRunStatusTaskManager().notice(this.serviceCategory, dtStart);

            //定义变量
            LocalDate runStatusDateBefore = LocalDate.now().minusDays(SERVICERUNSTATUSPROFILE_DAYBEFORE_NUM);
            LocalDate traceLogDateBefore = LocalDate.now().minusDays(TRACELOG_DAYBEFORE_NUM);
            LocalDate warningLogDateBefore = LocalDate.now().minusDays(WARNINGLOG_DAYBEFORE_NUM);
            LocalDate errorLogDateBefore = LocalDate.now().minusDays(ERRORLOG_DAYBEFORE_NUM);
            LocalDate superWarningLogDateBefore = LocalDate.now().minusDays(SUPERWARNINGLOG_DAYBEFORE_NUM);
            LocalDate optLogDateBefore = LocalDate.now().minusDays(OPTLOG_DAYBEFORE_NUM);

            try {
                //删除N天前的服务运行记录
                int runStatusNum = serviceRunStatusService.deleteByDateBefore(new DeleteProfileByDateBeforeCommand(runStatusDateBefore));
                resultSb.append("服务运行记录删除成功：").append(runStatusNum).append("行；");
                numSuccess++;
            } catch (Exception e) {
                resultSb.append("服务运行记录删除失败: ").append(e.getMessage()).append("；");
                this.writeLog(LogTypeEnum.SUPERWARNING, "服务运行记录删除失败:" + CoreUtils.exceptionToString(e));
            }

            try {
                //删除N天前的调试日志
                int dbDeleteNum = serviceDbLog.deleteByCondition(new DeleteByConditionCommand(LogTypeEnum.TRACE, traceLogDateBefore));
                resultSb.append("调试日志删除成功：").append(dbDeleteNum).append("行；");
                numSuccess++;
            } catch (Exception e) {
                resultSb.append("调试日志删除失败: ").append(e.getMessage()).append("；");
                this.writeLog(LogTypeEnum.SUPERWARNING, "调试日志删除失败:" + CoreUtils.exceptionToString(e));
            }

            try {
                //删除N天前的警告日志
                int dbDeleteNum = serviceDbLog.deleteByCondition(new DeleteByConditionCommand(LogTypeEnum.WARNING, warningLogDateBefore));
                resultSb.append("警告日志删除成功：").append(dbDeleteNum).append("行；");
                numSuccess++;
            } catch (Exception e) {
                resultSb.append("警告日志删除失败: ").append(e.getMessage()).append("；");
                this.writeLog(LogTypeEnum.SUPERWARNING, "警告日志删除失败:" + CoreUtils.exceptionToString(e));
            }

            try {
                //删除N天前的错误日志
                int dbDeleteNum = serviceDbLog.deleteByCondition(new DeleteByConditionCommand(LogTypeEnum.ERROR, errorLogDateBefore));
                resultSb.append("错误日志删除成功：").append(dbDeleteNum).append("行；");
                numSuccess++;
            } catch (Exception e) {
                resultSb.append("错误日志删除失败: ").append(e.getMessage()).append("；");
                this.writeLog(LogTypeEnum.SUPERWARNING, "错误日志删除失败:" + CoreUtils.exceptionToString(e));
            }

            try {
                //删除N天前的超级警告日志
                int dbDeleteNum = serviceDbLog.deleteByCondition(new DeleteByConditionCommand(LogTypeEnum.SUPERWARNING, superWarningLogDateBefore));
                resultSb.append("超级警告日志删除成功：").append(dbDeleteNum).append("行；");
                numSuccess++;
            } catch (Exception e) {
                resultSb.append("超级警告日志删除失败: ").append(e.getMessage()).append("；");
                this.writeLog(LogTypeEnum.SUPERWARNING, "定期清理日志和记录型数据管理器删除日志和记录失败:" + CoreUtils.exceptionToString(e));
            }

            try {
                //删除N天前的操作查询日志
                int dbDeleteNum = operatorLogService.deleteByCondition(LocalDateTime.now().minusDays(OPTLOG_DAYBEFORE_NUM));
                resultSb.append("操作查询日志删除成功：").append(dbDeleteNum).append("行；");
                numSuccess++;
            } catch (Exception e) {
                resultSb.append("操作查询日志删除失败: ").append(e.getMessage()).append("；");
                this.writeLog(LogTypeEnum.SUPERWARNING, "定期清理操作查询日志失败:" + CoreUtils.exceptionToString(e));
            }

            //通知服务运行状态管理器执行结果
            if (numSuccess > 0) {
                long totalMS = Duration.between(dtStart, LocalDateTime.now()).toMillis();
                BaseTaskManagerFactory.get().getServiceRunStatusTaskManager().notice(this.serviceCategory, LocalDateTime.now(), "已完成，成功：" + numSuccess + "(" + resultSb + "),耗时" + totalMS + "毫秒", true, numSuccess);
            }

            //清空resultSb
            return true;
        });
    }

    /**
     * 判断 是否需要进行操作
     *
     * @param timerPeriod 定时器时间间隔(单位：秒)
     * @return 是否需要进行操作
     */
    @Override
    public Boolean isNeedWork(int timerPeriod) {
        //定时器总运行次数加1。
        this.numRun += 1;
        this.numTotalRun += 1;

        //如果当前正在操作。
        if (this.isInProgress) {
            return false;
        }

        //如果未达到执行频率。
        if (this.numRun * timerPeriod < runFrequency) {
            return false;
        }

        synchronized (__LOCK_WORK_TIME__) {
            this.lastDoWorkTime = LocalDateTime.now();
        }

        //定时执行时间(凌晨04:00～05:00时)。
        LocalDateTime dtStart = LocalDate.now().atTime(4, 0);
        LocalDateTime dtEnd = LocalDate.now().atTime(5, 0);

        //获取最后执行时间，如果没有执行过则执行。
        LocalDateTime dt = this.lastTime;

        //判断是否需要执行。
        if (LocalDateTime.now().isAfter(dtStart) && LocalDateTime.now().isBefore(dtEnd) && (null == dt || dt.isBefore(dtStart))) {
            //计数器清零。
            this.numRun = 0;
            this.numRealRun +=1;
            return true;
        }

        return false;
    }

    // endregion
}
