<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD

 Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.differ.jackyun.omsapibase.dao.jackyunmultidb.shopv2.ApiShopAuthMapper">

    <!-- 表字段与模型属性映射 -->
    <resultMap id="BaseResultMap" type="com.differ.jackyun.omsapibase.data.shopv2.entity.ApiShopAuthEntity">
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="poly_token" jdbcType="VARCHAR" property="polyToken"/>
        <result column="app_key" jdbcType="VARCHAR" property="appKey"/>
        <result column="auth_status" jdbcType="TINYINT" property="authStatus"/>
        <result column="auth_session_key" jdbcType="VARCHAR" property="authSessionKey"/>
        <result column="auth_remain_time" jdbcType="DATE" property="authRemainTime"/>
        <result column="self_use_auth" jdbcType="VARCHAR" property="selfUseAuth"/>
        <result column="auth_sell_nick" jdbcType="VARCHAR" property="authSellNick"/>
        <result column="plat_shop_id" jdbcType="VARCHAR" property="platShopId"/>
        <result column="plat_shop_name" jdbcType="VARCHAR" property="platShopName"/>
        <result column="purchase_expire_time" jdbcType="DATE" property="purchaseExpireTime"/>
        <result column="extra_json" jdbcType="VARCHAR" property="extraJson"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
    </resultMap>

    <!-- 表字段与模型属性映射 -->
    <resultMap id="BaseResultWithPlatMap"
               type="com.differ.jackyun.omsapibase.data.shopv2.entity.ApiShopAuthWithPlatEntity">
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="shop_type" jdbcType="INTEGER" property="shopType"/>
        <result column="poly_token" jdbcType="VARCHAR" property="polyToken"/>
        <result column="app_key" jdbcType="VARCHAR" property="appKey"/>
        <result column="auth_status" jdbcType="TINYINT" property="authStatus"/>
        <result column="auth_session_key" jdbcType="VARCHAR" property="authSessionKey"/>
        <result column="auth_remain_time" jdbcType="DATE" property="authRemainTime"/>
        <result column="self_use_auth" jdbcType="VARCHAR" property="selfUseAuth"/>
        <result column="auth_sell_nick" jdbcType="VARCHAR" property="authSellNick"/>
        <result column="plat_shop_id" jdbcType="VARCHAR" property="platShopId"/>
        <result column="plat_shop_name" jdbcType="VARCHAR" property="platShopName"/>
        <result column="purchase_expire_time" jdbcType="DATE" property="purchaseExpireTime"/>
        <result column="extra_json" jdbcType="VARCHAR" property="extraJson"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
    </resultMap>

    <!-- 店铺基础信息和授权信息 -->
    <resultMap id="BaseAuthResultMap"
               type="com.differ.jackyun.omsapibase.data.shopv2.entity.ShopBaseAuthEntity">
        <result column="base_shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="shop_name" jdbcType="VARCHAR" property="shopName"/>
        <result column="shop_status" jdbcType="TINYINT" property="shopStatus"/>
        <result column="shop_type" jdbcType="INTEGER" property="shopType"/>
        <result column="poly_token" jdbcType="VARCHAR" property="polyToken"/>
        <result column="system_plat_code" jdbcType="VARCHAR" property="systemPlatCode"/>
        <result column="base_extra_json" jdbcType="VARCHAR" property="baseExtraJson"/>
        <result column="base_gmt_modified" jdbcType="TIMESTAMP" property="baseGmtModified"/>
        <result column="base_gmt_create" jdbcType="TIMESTAMP" property="baseGmtCreate"/>
        <result column="app_key" jdbcType="VARCHAR" property="appKey"/>
        <result column="auth_status" jdbcType="TINYINT" property="authStatus"/>
        <result column="auth_session_key" jdbcType="VARCHAR" property="authSessionKey"/>
        <result column="auth_remain_time" jdbcType="DATE" property="authRemainTime"/>
        <result column="self_use_auth" jdbcType="VARCHAR" property="selfUseAuth"/>
        <result column="auth_sell_nick" jdbcType="VARCHAR" property="authSellNick"/>
        <result column="plat_shop_id" jdbcType="VARCHAR" property="platShopId"/>
        <result column="plat_shop_name" jdbcType="VARCHAR" property="platShopName"/>
        <result column="purchase_expire_time" jdbcType="DATE" property="purchaseExpireTime"/>
        <result column="auth_extra_json" jdbcType="VARCHAR" property="authExtraJson"/>
        <result column="auth_gmt_modified" jdbcType="TIMESTAMP" property="authGmtModified"/>
        <result column="auth_gmt_create" jdbcType="TIMESTAMP" property="authGmtCreate"/>
    </resultMap>

    <!-- 字段语句块 -->
    <sql id="shop_auth_base_field">
        base.shop_id as base_shop_id,
        base.shop_name,
        base.shop_status,
        base.shop_type,
        base.system_plat_code,
        base.extra_json as base_extra_json,
        base.gmt_create as base_gmt_create,
        base.gmt_modified as base_gmt_modified,
        base.poly_token as poly_token,
        auth.app_key,
        auth.auth_status,
        auth.auth_session_key,
        auth.auth_remain_time,
        auth.self_use_auth,
        auth.auth_sell_nick,
        auth.plat_shop_id,
        auth.plat_shop_name,
        auth.purchase_expire_time,
        auth.extra_json as auth_extra_json,
        auth.gmt_create as auth_gmt_create,
        auth.gmt_modified as auth_gmt_modified
    </sql>

    <!-- 新增或更新 -->
    <insert id="addOrUpdate" parameterType="com.differ.jackyun.omsapibase.data.shopv2.entity.ApiShopAuthEntity">
        INSERT INTO j_api_shop_auth_info
        (shop_id,
        poly_token,
        app_key,
        auth_status,
        auth_session_key,
        auth_remain_time,
        self_use_auth,
        auth_sell_nick,
        plat_shop_id,
        plat_shop_name,
        purchase_expire_time,
        extra_json
        )
        VALUES
        (#{shopId}
        , #{polyToken}
        , #{appKey}
        , #{authStatus}
        , #{authSessionKey}
        , #{authRemainTime}
        , #{selfUseAuth}
        , #{authSellNick}
        , #{platShopId}
        , #{platShopName}
        , #{purchaseExpireTime}
        , #{extraJson}
        )
        ON DUPLICATE KEY UPDATE
        poly_token = #{polyToken},
        app_key = #{appKey},
        auth_status = #{authStatus},
        auth_session_key = #{authSessionKey},
        auth_remain_time = #{authRemainTime},
        self_use_auth = #{selfUseAuth},
        auth_sell_nick = #{authSellNick},
        plat_shop_id = #{platShopId},
        plat_shop_name = #{platShopName},
        purchase_expire_time = #{purchaseExpireTime},
        extra_json = #{extraJson}
    </insert>

    <!-- 批量新增或更新 -->
    <update id="addOrUpdateBatch"
            parameterType="com.differ.jackyun.omsapibase.data.shopv2.entity.ApiShopAuthEntity">
        INSERT INTO j_api_shop_auth_info
        (shop_id,
        poly_token,
        app_key,
        auth_status,
        auth_session_key,
        auth_remain_time,
        self_use_auth,
        auth_sell_nick,
        plat_shop_id,
        plat_shop_name,
        purchase_expire_time,
        extra_json
        )
        values
        <foreach collection="apiShopAuthEntityList" item="apiShopAuthEntity" separator=",">
            (#{apiShopAuthEntity.shopId}
            , #{apiShopAuthEntity.polyToken}
            , #{apiShopAuthEntity.appKey}
            , #{apiShopAuthEntity.authStatus}
            , #{apiShopAuthEntity.authSessionKey}
            , #{apiShopAuthEntity.authRemainTime}
            , #{apiShopAuthEntity.selfUseAuth}
            , #{apiShopAuthEntity.authSellNick}
            , #{apiShopAuthEntity.platShopId}
            , #{apiShopAuthEntity.platShopName}
            , #{apiShopAuthEntity.purchaseExpireTime}
            , #{apiShopAuthEntity.extraJson}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        poly_token = VALUES(poly_token),
        app_key = VALUES(app_key),
        auth_status = VALUES(auth_status),
        auth_session_key = VALUES(auth_session_key),
        auth_remain_time = VALUES(auth_remain_time),
        self_use_auth = VALUES(self_use_auth),
        auth_sell_nick = VALUES(auth_sell_nick),
        plat_shop_id = VALUES(plat_shop_id),
        plat_shop_name = VALUES(plat_shop_name),
        purchase_expire_time = VALUES(purchase_expire_time),
        extra_json = VALUES(extra_json)
    </update>

    <!-- 更新非null的字段，根据条件对象 -->
    <update id="updateByShopId" parameterType="com.differ.jackyun.omsapibase.data.shopv2.entity.ApiShopAuthEntity">
        update j_api_shop_auth_info
        <set>
            <if test="polyToken != null">`poly_token` = #{polyToken,jdbcType=VARCHAR},</if>
            <if test="appKey != null">`app_key` = #{appKey,jdbcType=VARCHAR},</if>
            <if test="authStatus != null">`auth_status` = #{authStatus,jdbcType=Byte},</if>
            <if test="authSessionKey != null">`auth_session_key` = #{authSessionKey,jdbcType=VARCHAR},</if>
            <if test="authRemainTime != null">`auth_remain_time` = #{authRemainTime,jdbcType=DATE},</if>
            <if test="selfUseAuth != null">`self_use_auth` = #{selfUseAuth,jdbcType=VARCHAR},</if>
            <if test="authSellNick != null">`auth_sell_nick` = #{authSellNick,jdbcType=VARCHAR},</if>
            <if test="platShopId != null">`plat_shop_id` = #{platShopId,jdbcType=VARCHAR},</if>
            <if test="platShopName != null">`plat_shop_name` = #{platShopName,jdbcType=VARCHAR},</if>
            <if test="purchaseExpireTime != null">`purchase_expire_time` = #{purchaseExpireTime,jdbcType=DATE},</if>
            <if test="extraJson != null">`extra_json` = #{extraJson,jdbcType=VARCHAR},</if>
        </set>
        where shop_id = #{shopId}
    </update>

    <!-- 更新最后修改时间 -->
    <update id="updateModifyTime">
        update j_api_shop_auth_info
        set gmt_modified = now()
        where shopId = #{shopId}
    </update>

    <!-- 按会员查询 -->
    <select id="queryAll" resultMap="BaseResultMap">
        SELECT
        shop_id,
        poly_token,
        app_key,
        auth_status,
        auth_session_key,
        auth_remain_time,
        self_use_auth,
        auth_sell_nick,
        plat_shop_id,
        plat_shop_name,
        purchase_expire_time,
        extra_json,
        gmt_modified,
        gmt_create
        FROM
        j_api_shop_auth_info
    </select>

    <!-- 按店铺Id查询 -->
    <select id="queryByShopId" parameterType="Map" resultMap="BaseResultMap">
        SELECT
        shop_id,
        poly_token,
        app_key,
        auth_status,
        auth_session_key,
        auth_remain_time,
        self_use_auth,
        auth_sell_nick,
        plat_shop_id,
        plat_shop_name,
        purchase_expire_time,
        extra_json,
        gmt_modified,
        gmt_create
        FROM
        j_api_shop_auth_info
        WHERE
        shop_id = #{shopId}
    </select>

    <!-- 按店铺Id查询 -->
    <select id="queryByShopIds" parameterType="Map" resultMap="BaseResultMap">
        SELECT
        shop_id,
        poly_token,
        app_key,
        auth_status,
        auth_session_key,
        auth_remain_time,
        self_use_auth,
        auth_sell_nick,
        plat_shop_id,
        plat_shop_name,
        purchase_expire_time,
        extra_json,
        gmt_modified,
        gmt_create
        FROM
        j_api_shop_auth_info
        WHERE shop_id IN
        <foreach collection="shopIds" item="shopId" separator="," open="(" close=")">
            #{shopId}
        </foreach>
    </select>

    <!--更新店铺授权信息-->
    <update id="updateAutoInfo" parameterType="Map">
        UPDATE j_api_shop_auth_info
        Set auth_session_key = #{sessionKey},
        auth_remain_time = #{sessionKeyExpireTime}
        where shop_id = #{shopId}
    </update>

    <!--批量更新店铺授权状态-->
    <update id="batchUpdateAuthStatus">
        update j_api_shop_auth_info
        set auth_status = #{authStatus}
        where shop_id in
        <foreach collection="shopIds" item="shopId" index="index" separator="," open="(" close=")">
            #{shopId}
        </foreach>;
    </update>

    <!-- 更新授权到期时间 -->
    <update id="batchUpdateAuthRemainTime">
        update j_api_shop_auth_info
        set auth_remain_time = #{authRemainTime}
        where shop_id in
        <foreach collection="idList" item="id" index="index" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

    <!--更新店铺自用型参数-->
    <update id="updateSelfUse" parameterType="Map">
        UPDATE j_api_shop_auth_info
        Set self_use_auth = #{selfUse}
        where shop_id = #{shopId}
    </update>

    <!--更新店铺 Token-->
    <update id="updatePolyToken" parameterType="Map">
        UPDATE j_api_shop_auth_info
        Set poly_token = #{polyToken}
        where shop_id = #{shopId}
    </update>

    <select id="queryByPlatAndStatus" resultMap="BaseResultMap">
        SELECT
        auth.shop_id,
        auth.auth_remain_time
        FROM
        j_api_shop_auth_info auth
        LEFT JOIN j_api_shop_base_info base
        ON base.shop_id = auth.shop_id
        <where>
            <if test="shopIdList != null">
                auth.shop_id in
                <foreach collection="shopIdList" item="shopId" open="(" close=")" separator=",">
                    #{shopId}
                </foreach>
            </if>
            <if test="plat != null">
                AND base.shop_type = #{plat}
            </if>
            <if test="authStatus != null">
                AND auth.auth_status = #{authStatus}
                AND (auth.auth_remain_time IS NOT NULL AND auth.auth_remain_time > now())
            </if>
        </where>
    </select>

    <select id="queryShopByPlatAndStatus" resultMap="BaseResultWithPlatMap">
        SELECT
        auth.shop_id,
        auth.poly_token,
        auth.auth_session_key,
        auth.self_use_auth,
        auth.auth_remain_time,
        auth.extra_json,
        base.shop_type
        FROM
        j_api_shop_auth_info auth
        LEFT JOIN j_api_shop_base_info base
        ON base.shop_id = auth.shop_id
        <where>
            <if test="plat != null">
                AND base.shop_type = #{plat}
            </if>
            <if test="authStatus != null">
                AND auth.auth_status = #{authStatus}
            </if>
            <if test="(offset!=null and offset>=0) and (pageSize!=null and pageSize>=0)">
                limit #{offset},#{pageSize}
            </if>
        </where>
    </select>

    <select id="queryShopCountByPlatAndStatus" resultType="integer">
        SELECT
        count(shop_id)
        FROM
        j_api_shop_auth_info auth
        LEFT JOIN j_api_shop_base_info base
        ON base.shop_id = auth.shop_id
        <where>
            <if test="plat != null">
                AND base.shop_type = #{plat}
            </if>
            <if test="authStatus != null">
                AND auth.auth_status = #{authStatus}
            </if>
        </where>
    </select>

    <!-- 根据平台值和授权状态查询店铺 -->
    <select id="queryByPlatAndAuthStatus" resultMap="BaseAuthResultMap">
        SELECT
        <include refid="shop_auth_base_field"/>
        FROM
        j_api_shop_auth_info auth
        LEFT JOIN j_api_shop_base_info base
        ON base.shop_id = auth.shop_id
        <where>
            <if test="plats != null and plats.size > 0">
                AND base.shop_type IN
                <foreach collection="plats" item="plat" separator="," open="(" close=")">
                    #{plat}
                </foreach>
            </if>
            <if test="authStatusList != null and authStatusList.size > 0">
                AND auth.auth_status IN
                <foreach collection="authStatusList" item="authStatus" separator="," open="(" close=")">
                    #{authStatus}
                </foreach>
            </if>
            <if test="exceptShopIds != null and exceptShopIds.size > 0">
                AND base.shop_id NOT IN
                <foreach collection="exceptShopIds" item="exceptShopId" separator="," open="(" close=")">
                    #{exceptShopId}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 根据店铺 ID 或 Token 查询店铺 -->
    <select id="queryByIdOrToken" resultMap="BaseAuthResultMap">
        SELECT
        <include refid="shop_auth_base_field"/>
        FROM
        j_api_shop_auth_info auth
        LEFT JOIN j_api_shop_base_info base
        ON base.shop_id = auth.shop_id
        <where>
            <if test="shopIds != null and shopIds.size > 0">
                base.shop_id IN
                <foreach collection="shopIds" item="shopId" separator="," open="(" close=")">
                    #{shopId}
                </foreach>
            </if>
            <if test="polyTokens != null and polyTokens.size > 0">
                OR auth.poly_token IN
                <foreach collection="polyTokens" item="polyToken" separator="," open="(" close=")">
                    #{polyToken}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 查询授权即将过期的店铺 -->
    <select id="queryAuthSoonExpireShops" resultMap="BaseAuthResultMap">
        SELECT
        <include refid="shop_auth_base_field"/>
        FROM
        j_api_shop_auth_info auth
        LEFT JOIN j_api_shop_base_info base
        ON base.shop_id = auth.shop_id
        <where>
            auth.auth_remain_time IS NOT NULL AND auth.auth_remain_time <![CDATA[ >= ]]> #{minTime} AND
            auth.auth_remain_time <![CDATA[ <= ]]> #{maxTime}
            <if test="shopIds != null and shopIds.size > 0">
                AND base.shop_id IN
                <foreach collection="shopIds" item="shopId" separator="," open="(" close=")">
                    #{shopId}
                </foreach>
            </if>
            <if test="authStatusList != null and authStatusList.size > 0">
                AND auth.auth_status IN
                <foreach collection="authStatusList" item="authStatus" separator="," open="(" close=")">
                    #{authStatus}
                </foreach>
            </if>
            <if test="exceptPlats != null and exceptPlats.size > 0">
                AND base.shop_type NOT IN
                <foreach collection="exceptPlats" item="exceptPlat" separator="," open="(" close=")">
                    #{exceptPlat}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 根据平台值查询授权有效的店铺 -->
    <select id="queryAuthedShopIdByPlat" resultType="java.lang.Long">
        select sb.shop_id
        from j_api_shop_base_info as sb
        JOIN j_api_shop_auth_info as sa ON sb.shop_id = sa.shop_id
        where sa.auth_status = 0 and sb.shop_type in
        <foreach collection="platList" item="plat" open="(" close=")" separator=",">
            #{plat}
        </foreach>
    </select>
</mapper>