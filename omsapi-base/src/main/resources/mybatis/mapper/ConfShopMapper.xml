<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.differ.jackyun.omsapibase.dao.jackyunmultidb.shop.ConfShopMapper">

    <!-- 表字段与模型属性映射 -->
    <resultMap id="BaseResultMap" type="com.differ.jackyun.omsapibase.data.shopconf.entity.ConfShopEntity">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="shop_type" jdbcType="INTEGER" property="shopType"/>
        <result column="is_auto_download" jdbcType="INTEGER" property="isAutoDownload"/>
        <result column="is_auto_download_online_after" jdbcType="INTEGER" property="isAutoDownloadOnlineAfter"/>
        <result column="is_auto_audit_online_after" jdbcType="INTEGER" property="isAutoAuditOnlineAfter"/>
        <result column="auth_remain_time" jdbcType="DATE" property="authRemainTime"/>
        <result column="shop_conf" jdbcType="LONGVARCHAR" property="shopConf"/>
        <result column="self_use_auth" jdbcType="LONGVARCHAR" property="selfUseAuth"/>
        <result column="auth_status" jdbcType="INTEGER" property="authStatus"/>
        <result column="token" jdbcType="VARCHAR" property="token"/>
        <result column="plat_token" jdbcType="VARCHAR" property="platToken"/>
        <result column="plat_shop_name" jdbcType="VARCHAR" property="platShopName"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
    </resultMap>

    <!-- 字段语句块 -->
    <sql id="Base_Column_List">
        `id`,`channel_id`,`shop_type`,`is_auto_download`,`shop_conf`,`gmt_modified`,`is_auto_download_online_after`,
        `is_auto_audit_online_after`,`self_use_auth`,`auth_status`,`plat_shop_name`,`auth_remain_time`,`token`,`plat_token`,`gmt_create`
    </sql>

    <!-- 清空店铺授权信息 -->
    <update id="cleanAuthorizationInfoByChannelId"
            parameterType="com.differ.jackyun.omsapibase.data.shopconf.entity.ConfShopEntity">
        update j_conf_shop
        <set>
            <if test="isAutoDownload != null">`is_auto_download` = #{isAutoDownload,jdbcType=INTEGER},</if>
            <if test="authRemainTime != null">`auth_remain_time` = #{authRemainTime,jdbcType=DATE},</if>
            <if test="selfUseAuth != null">`self_use_auth` = #{selfUseAuth,jdbcType=LONGVARCHAR},</if>
            <if test="authStatus != null">`auth_status` = #{authStatus,jdbcType=INTEGER},</if>
            <if test="token != null">`token` = #{token,jdbcType=VARCHAR},</if>
            <if test="platToken != null">`plat_token` = #{platToken,jdbcType=VARCHAR},</if>
            <if test="platShopName != null">`plat_shop_name` = #{platShopName,jdbcType=VARCHAR},</if>
        </set>
        where channel_id = #{channelId,jdbcType=INTEGER}
    </update>

    <!-- 更新店铺授权信息 -->
    <update id="updateConfigShopAuthInfo" parameterType="map">
        UPDATE j_conf_shop
        Set auth_remain_time = #{sessionKeyExpireTime}
        where channel_id = #{shopId,jdbcType=BIGINT}
    </update>

    <!-- 更新店铺授权状态 -->
    <update id="updateShopStatus" parameterType="map">
        UPDATE j_conf_shop
        <if test="authStatus != null">`auth_status` = #{authStatus,jdbcType=INTEGER}</if>
        where channel_id = #{shopId,jdbcType=BIGINT}
    </update>

    <!-- 查询，根据主键 -->
    <select id="getByShopId" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from j_conf_shop
        where channel_id = #{shopId,jdbcType=BIGINT}
    </select>

    <!-- 插入，并返回自增ID，只插入非null的字段 -->
    <insert id="insertShopConfig" parameterType="com.differ.jackyun.omsapibase.data.shopconf.entity.ConfShopEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into j_conf_shop
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="channelId != null">`channel_id`,</if>
            <if test="shopType != null">`shop_type`,</if>
            <if test="isAutoDownload != null">`is_auto_download`,</if>
            <if test="isAutoDownloadOnlineAfter != null">`is_auto_download_online_after`,</if>
            <if test="isAutoAuditOnlineAfter != null">`is_auto_audit_online_after`,</if>
            <if test="authRemainTime != null">`auth_remain_time`,</if>
            <if test="shopConf != null">`shop_conf`,</if>
            <if test="selfUseAuth != null">`self_use_auth`,</if>
            <if test="authStatus != null">`auth_status`,</if>
            <if test="token != null">`token`,</if>
            <if test="platToken != null">`plat_token`,</if>
            <if test="platShopName != null">`plat_shop_name`,</if>
            <if test="gmtCreate != null">`gmt_create`,</if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="channelId != null">#{channelId,jdbcType=BIGINT},</if>
            <if test="shopType != null">#{shopType,jdbcType=INTEGER},</if>
            <if test="isAutoDownload != null">#{isAutoDownload,jdbcType=INTEGER},</if>
            <if test="isAutoDownloadOnlineAfter != null">#{isAutoDownloadOnlineAfter,jdbcType=INTEGER},</if>
            <if test="isAutoAuditOnlineAfter != null">#{isAutoAuditOnlineAfter,jdbcType=INTEGER},</if>
            <if test="authRemainTime != null">#{authRemainTime,jdbcType=DATE},</if>
            <if test="shopConf != null">#{shopConf,jdbcType=LONGVARCHAR},</if>
            <if test="selfUseAuth != null">#{selfUseAuth,jdbcType=LONGVARCHAR},</if>
            <if test="authStatus != null">#{authStatus,jdbcType=INTEGER},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="platToken != null">#{platToken,jdbcType=VARCHAR},</if>
            <if test="platShopName != null">#{platShopName,jdbcType=VARCHAR},</if>
            <if test="gmtCreate != null">#{gmtCreate,jdbcType=TIMESTAMP},</if>

        </trim>
    </insert>

    <!-- 更新非null的字段，根据主键 -->
    <update id="updateByPrimaryKey"
            parameterType="com.differ.jackyun.omsapibase.data.shopconf.entity.ConfShopEntity">
        update j_conf_shop
        <set>
            <if test="channelId != null">`channel_id` = #{channelId,jdbcType=BIGINT},</if>
            <if test="shopType != null">`shop_type` = #{shopType,jdbcType=INTEGER},</if>
            <if test="isAutoDownload != null">`is_auto_download` = #{isAutoDownload,jdbcType=INTEGER},</if>
            <if test="isAutoDownloadOnlineAfter != null">`is_auto_download_online_after` =
                #{isAutoDownloadOnlineAfter,jdbcType=INTEGER},
            </if>
            <if test="isAutoAuditOnlineAfter != null">`is_auto_audit_online_after` =
                #{isAutoAuditOnlineAfter,jdbcType=INTEGER},
            </if>
            `auth_remain_time` = #{authRemainTime,jdbcType=DATE},
            <if test="shopConf != null">`shop_conf` = #{shopConf,jdbcType=LONGVARCHAR},</if>
            <if test="selfUseAuth != null">`self_use_auth` = #{selfUseAuth,jdbcType=LONGVARCHAR},</if>
            <if test="authStatus != null">`auth_status` = #{authStatus,jdbcType=INTEGER},</if>
            <if test="token != null">`token` = #{token,jdbcType=VARCHAR},</if>
            <if test="platToken != null">`plat_token` = #{platToken,jdbcType=VARCHAR},</if>
            <if test="platShopName != null">`plat_shop_name` = #{platShopName,jdbcType=VARCHAR},</if>

        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 保存店铺的菠萝派token -->
    <update id="saveShopToken">
        update j_conf_shop set token=#{shopToken} where channel_id=#{shopId}
    </update>
</mapper>
