package com.differ.jackyun.omsapibase.component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.differ.jackyun.framework.component.basic.dto.JackYunResponse;
import com.differ.jackyun.framework.component.multidb.config.prop.PoolProperties;
import com.differ.jackyun.omsapibase.core.BaseTest;
import com.differ.jackyun.omsapibase.data.constant.OmsApiConstant;
import com.differ.jackyun.omsapibase.data.core.BaseEntity;
import com.differ.jackyun.omsapibase.data.operation.AdminManualDownloadTaobaoEntity;
import com.differ.jackyun.omsapibase.data.rds.db.RDSDbEntity;
import com.differ.jackyun.omsapibase.data.rdsorder.GetOrdersByTimeAndShopWithLimitQuery;
import com.differ.jackyun.omsapibase.data.rdsorder.RDSShopTypesEnum;
import com.differ.jackyun.omsapibase.data.rdsorder.response.rds.RDSOrderEntity;
import com.differ.jackyun.omsapibase.domain.core.DomainUtils;
import com.differ.jackyun.omsapibase.domain.dataproxy.core.Common2DataProxy;
import com.differ.jackyun.omsapibase.domain.dataproxy.core.CommonDataProxy;
import com.differ.jackyun.omsapibase.domain.dataproxy.shopconf.ShopConfAutoDownloadDataProxy;
import com.differ.jackyun.omsapibase.domain.dataproxy.worker.WorkerDataProxy;
import com.differ.jackyun.omsapibase.feign.FeignTestClient;
import com.differ.jackyun.omsapibase.infrastructure.component.cache.DataCacheTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.component.mq.data.core.MQMonitorData;
import com.differ.jackyun.omsapibase.infrastructure.component.mq.data.worker.send.SendWorkerMQData;
import com.differ.jackyun.omsapibase.infrastructure.component.mq.omsmq.MQTypesEnum;
import com.differ.jackyun.omsapibase.infrastructure.component.thread.JTask;
import com.differ.jackyun.omsapibase.infrastructure.enums.ApplicationTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.ErrorCodes;
import com.differ.jackyun.omsapibase.infrastructure.exception.AppException;
import com.differ.jackyun.omsapibase.infrastructure.openapi.manualdownloadorder.CreateDownloadOrderWorkerRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.core.FeignPostDataResultEnum;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.authorization.BuildeAuthorizeUrlRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.utils.*;
import com.differ.jackyun.omsapibase.paltrequest.polyapi.core.PolyAPIUtils;
import com.differ.jackyun.omsapibase.plugins.cacher.OmsRedis;
import com.differ.jackyun.omsapibase.service.core.ServiceUtils;
import com.differ.jackyun.omsapibase.service.rdsorder.IRDSOrderService;
import com.differ.jackyun.omsapibase.service.worker.postdata.BaseWorkerPostDataStatusManager;
import com.differ.jackyun.omsapibase.tasks.mq.MQManager;
import com.differ.jackyun.omsapibase.tasks.taskmanager.plugins.UnvaluedMemberTaskManager;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;

import static com.differ.jackyun.omsapibase.data.constant.OmsApiConstant.POST_ORDER_REPEAT_ERROR_MSG;
import static com.differ.jackyun.omsapibase.domain.core.DomainUtils.getRDSDbByRule;

/**
 * <AUTHOR>
 * @since 2019-06-25  17:36:17
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Ignore
public class LmzTest extends BaseTest {

    @Before
    public void beforeTest() {
        super.init(true);
        DomainUtils.bindMemberUser("jackyun_dev");
    }

    @Autowired
    OmsRedis omsRedis;

    public static class TestEntity extends BaseEntity {
        private int age;
        private String name;

        public int getAge() {
            return age;
        }

        public void setAge(int age) {
            this.age = age;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    @Test
    public void test1() {

        LocalDateTime dtNow = LocalDateTime.now();
        /*List<TestEntity> dataEntity = new ArrayList<>();
        for (int i = 0; i < 10000; i++) {
            TestEntity entity = new TestEntity();
            entity.setAge(i);
            entity.setName(i+"岁");
            dataEntity.add(entity);
        }*/
        List<TestEntity> data = omsRedis.listGetRange("test.lst", 0, 10050, TestEntity.class);
        System.out.println(CoreUtils.diffTime(dtNow));

        LocalDateTime dtNow1 = LocalDateTime.now();
        List<TestEntity> data1 = new ArrayList<>();
        int total = 9999;
        int pageSize = 10;
        int pageNum = (total / pageSize) + ((total % pageSize > 0) ? 1 : 0);
        for (int i = 0; i < pageNum; i++) {
            data1.addAll(omsRedis.listGetRange("test.lst", pageSize * i, Math.min((i + 1) * pageSize, total), TestEntity.class));
        }
        System.out.println(CoreUtils.diffTime(dtNow1));

        TestEntity entity = new TestEntity();
        entity.setName("s11s");
        entity.setAge(15);
        System.out.println(omsRedis.listSet("test.lst", 55, entity));
        ;
    }

    @Test
    public void test2() {
        //实际不抛出异常的线程数量是 fixThread + 有界阻塞队列长度
        Executor executor = new ThreadPoolExecutor(3, 3, 0L, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(15), new ThreadPoolExecutor.AbortPolicy());
        for (int i = 0; i < 19; i++) {
            executor.execute(() -> {
                try {

                    System.out.println(1);
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            });
        }

        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public static class Student {
        public static final Object object = new Object();

        private static Object test = null;

        public void iniTask(int a) {
            if (test == null) {
                synchronized (object) {
                    if (test == null) {
                        test = new Object();
                    }
                }
            }

            try {
                test.toString();
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }

        private boolean sex;
        private String name;
        private List<Student> names;

        private LocalDateTime dt;

        public LocalDateTime getDt() {
            return dt;
        }

        public void setDt(LocalDateTime dt) {
            this.dt = dt;
        }

        public boolean isSex() {
            return sex;
        }

        public void setSex(boolean sex) {
            this.sex = sex;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<Student> getNames() {
            return names;
        }

        public void setNames(List<Student> names) {
            this.names = names;
        }
    }

    @Test
    public void test3() {
        String str = "{\"isAsync\":true,\"isAutomatic\":true,\"isWriteBigContent\":true,\"isWriteBusinessLog\":true,\"memberName\":\"420001\",\"orders\":[{\"isSplit\":0,\"jhLogisticCode\":\"JH_014\",\"logisticCode\":\"SF\",\"logisticName\":\"顺丰快递\",\"logisticTel\":\"\",\"platOrderNo\":\"190718053106308\",\"postId\":\"23423434\",\"sendBackData\":\"{\\\"shopType\\\":\\\"JH_001\\\",\\\"invoiceNo\\\":null,\\\"invoiceDate\\\":null}\",\"sendType\":\"1\",\"sysTradeId\":672935917826441984,\"tradeId\":672934748664201984}],\"otherCacheKey\":\"673084756337394560\",\"platValue\":101,\"shopID\":672930664780694016,\"token\":\"f03763879a0b4b508b55c3e8a518cc67\",\"userId\":\"czc\",\"workerNO\":673084756337394560}###################null###################null###################null";
        SendWorkerMQData data = new SendWorkerMQData();
        data.fillFromSplicedJson(str);
    }

    @Test
    public void test4() {
        BlockingQueue<String> bq = new ArrayBlockingQueue<>(2000);
    }

    @Test
    public void test5() {
        PoolProperties poolProperties = SpringResolveManager.resolve(PoolProperties.class);
        System.out.println(poolProperties.getMaxWait());
        System.out.println(poolProperties.getInitialSize());
        System.out.println(poolProperties.getPoolPreparedStatements());
        System.out.println(poolProperties.getMaxActive());
    }

    @Test
    public void test6() {
        DomainUtils.bindMemberUser("jackyun_dev");
        System.out.println(LocalConfig.get().getGroupId());
    }

    @Test
    public void test7() {
        String bizContent = "";
        CreateDownloadOrderWorkerRequestBizData bizData = new CreateDownloadOrderWorkerRequestBizData();
        bizData.setDownloadBegin(LocalDateTime.now().minusDays(1));
        bizData.setDownloadEnd(LocalDateTime.now());
        List<CreateDownloadOrderWorkerRequestBizData.DownloadOrderNotice> notices = new ArrayList<>();
        CreateDownloadOrderWorkerRequestBizData.DownloadOrderNotice notice = new CreateDownloadOrderWorkerRequestBizData.DownloadOrderNotice();
        notice.setShopId(645887716305426560L);
        notices.add(notice);
        bizData.setNotices(notices);
        bizContent = JsonUtils.toJson(bizData);
        System.out.println(bizContent);
    }

    /**
     * JTASK类级变量
     */
    private static JTask jTask = null;

    /**
     * 并发锁对象
     */
    private static final Object OBJ_LOCK = new Object();

    @Test
    public void test() {
        for (int i = 0; i < 100; i++) {
            int a = i;
            Thread thread = new Thread(() -> {
                test9(a);
            });
            thread.start();
        }

        try {
            Thread.sleep(500000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public void test9(int time) {
        System.out.println("开始执行第" + time + "次test任务");
        // 创建线程池对象实例(方法结束之前要释放线程池资源)。
        if (jTask == null) {
            synchronized (OBJ_LOCK) {
                System.out.println("第" + time + "次test任务，判断是否创建JTASK");
                if (jTask == null) {
                    System.out.println("第" + time + "次test任务，创建JTASK");
                    jTask = JTask.createInstance(10);
                }
            }
        }

        //生成唯一ID，对应异步任务
        List<Long> uIDTasks = new ArrayList<>();

        //异步去执行多个根据sku数量分页的子任务，并合并结果返回
        Random random = new Random();
        for (int j = 0; j < 5; j++) {
            //生成唯一ID，并加入数组中
            Long uIDTask = jTask.startTask(null, (o) -> {
                try {
                    Thread.sleep(random.nextInt(5) * 1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                return 1;
            });
            uIDTasks.add(uIDTask);
        }

        //根据ID找到任务,等待结果处理完毕
        Object[] resultByIds = jTask.getResultByIds(uIDTasks);
        for (Object obj : resultByIds) {
            System.out.println(obj);
        }

        System.out.println("第" + time + "次任务执行完毕");
    }

    @Test
    public void test10() {
        List<String> list = new ArrayList<>();
        list.add("666666");
        list.add("jackyun");
        list.add("jackyun_dev");
        list.add("420001");
        Common2DataProxy.saveLargeOrderMembers(list);

        List<String> list1 = Common2DataProxy.getLargeOrderMembers();
        list1.remove(0);
        list1.remove(0);

        Common2DataProxy.saveLargeOrderMembers(list1);

        List<String> list2 = Common2DataProxy.getLargeOrderMembers();
    }

    @Autowired
    private IRDSOrderService service;
    /**
     * rdsId
     */
    private Long rdsId = 632321048605894651L;

    //证明mysql的事务隔离级别是 可重复读（即第一次读出的数据，后面重复读都相同。即使别的地方更改了此数据）
    @Test
    public void test11() {

        LocalDateTime startTime = LocalDateTime.of(2020, 01, 05, 23, 0, 0);
        GetOrdersByTimeAndShopWithLimitQuery query = new GetOrdersByTimeAndShopWithLimitQuery(startTime, LocalDateTime.now(), "佳琦全球严选", 1);

        CoreUtils.doTransaction((o) -> {
            List<RDSOrderEntity> lst1 = service.getOrdersByTimeAndShopNickWithLimit(rdsId, query, RDSShopTypesEnum.NORMAL);
            System.out.println(lst1.get(0).getJsonOrderData());
            List<RDSOrderEntity> lst2 = service.getOrdersByTimeAndShopNickWithLimit(rdsId, query, RDSShopTypesEnum.NORMAL);
            System.out.println(lst2.get(0).getJsonOrderData());
            List<RDSOrderEntity> lst3 = service.getOrdersByTimeAndShopNickWithLimit(rdsId, query, RDSShopTypesEnum.NORMAL);
            System.out.println(lst3.get(0).getJsonOrderData());
        });
        List<RDSOrderEntity> lst4 = service.getOrdersByTimeAndShopNickWithLimit(rdsId, query, RDSShopTypesEnum.NORMAL);
        System.out.println(lst4.get(0).getJsonOrderData());

    }

    @Test
    public void test13() {
        Student student = new Student();
        List<Student> lit = new ArrayList<>();
        Student a = new Student();
        a.setName("aa");
        a.setSex(true);
        lit.add(a);
        lit.add(a);
        student.setNames(lit);
        System.out.println(JsonUtils.toJson(student));
        System.out.println(JsonUtils.toOMSOnlineJson(student));

    }

    @Test
    public void test12() {
        for (int i = 0; i < 10; i++) {

            LocalDateTime now = LocalDateTime.now();
            System.out.println(DomainUtils.getGroupNo("jackyun_dev"));
            ;
            System.out.println(CoreUtils.diffTime(now));
        }
    }

    @Test
    public void test14() {
        JTask jTask = new JTask(10, 5000, 20, 0, "PoolTest");
        for (int i = 0; i < 12; i++) {
            System.out.println(">>>>>>>start");
            System.out.println(jTask.getThreadPoolInfo());
            int a = i;
            jTask.runTask(null, (o) -> {
                System.out.println("执行线程" + a);
                return null;
            });
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            System.out.println(jTask.getThreadPoolInfo());

            System.out.println(">>>>>>>end");
        }
        jTask.changeCoreThreadNumber(20);

        for (int i = 0; i < 12; i++) {
            System.out.println("$$$$$$$start");
            System.out.println(jTask.getThreadPoolInfo());
            int a = i;
            jTask.runTask(null, (o) -> {
                System.out.println("执行线程" + a);
                return null;
            });
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            System.out.println(jTask.getThreadPoolInfo());

            System.out.println("$$$$$$$end");
        }
    }

    @Test
    public void test15() {
        ShopConfAutoDownloadDataProxy proxy = ShopConfAutoDownloadDataProxy.get();
        List<String> lst = proxy.getDataStrByShopId(726062542887191168L);
    }

    @Autowired
    private FeignTestClient feignClient;

    @Test
    public void test16() {
        for (String response : POST_ORDER_REPEAT_ERROR_MSG) {
            JackYunResponse responses = new JackYunResponse();
            responses.setMsg(response);
            FeignPostDataResultEnum feignResult = DomainUtils.analyzeFeignCallResponse(responses);
            System.out.println(response);
            System.out.println(JsonUtils.toJson(feignResult));
        }
    }


    @Test
    public void test18() {
        Set<String> cache = WorkerDataProxy.get().getKeys(DataCacheTypeEnum.WORKER, "worker.[!syncstock!rds]*");
    }

    @Test
    public void test19() {
        String names = "101000,101070,101071,101075,101077,101078,101080,101081,101082,101083,101084,682455,188496,195433,101086,101087,903547,920438,11111,832678,544557,66666,55555,00000,810956,859901,99999,815080,667711,886547,880660,244377,188405,266008,68880,68899,58888,28888,30188,30288,30388,30588,30688,30788,667788,998855,200211,110641,688888,610099,100866,997998,201901,800518,176076,619069,968220,690200,886322,804005,881188,139400,827688,670925,800200,123168,690399,210280,172755,758241,891120,977899,154289,718119,880081,909686,911366,280228,846688,880118,855330,590994,919500,690603,200880,838436,672799,388279,360548,331669,830889,187147,643033,197818,885006,600822,866822,520114,994996,850669,785968,100188,7194259,7112188,7161709,7175742,7171493,7189582,7150217,851022,7100736,7126611,7104044,655188,7107405,657788,121618,880784,7152903,7190049,944788,110086,7164814,147258,7188496,31088,33088,34088,712543,566996,37088,38088,7117452,7194612,358558,228822,998599,158900,913009,7141537,7110263,268362,777610,911422,123522,521001,880029,7132386,593900,980219,7196066,38288,765110,566596,919993,7182033,140135,391058,7113559,190080,377047,404080,551155,876800,997896,277887,700622,861688,351700,7135897,828858,258766,695868,882808,182321,688788,7109755,7157820,133499,688256,923880,7104877,285800,877987,160018,588671,884807,112811,200688,124322,200108,955123,948988,124205,762055,198828,878082,835531,994310,636018,31188,389565,307684,285364,7171345,621815,628565,295329,363780,653010,229459,902051,821836,671051,286541,156688,942683,937962,190049,717920,375771,871667,958976,7104391,882952,606692,544744,350520,7126272,765642,644525,881865,989158,873242,164814,700352,385722,927846,7124265,7174169,668849,7107454,7135138,7194770,7137110,886873,969005,77777,90822,51010,95598,19102,56666,53541,55055,51234,00002,00004,00005,00001,880774,7136708,54168,52014,53333,51212,56888,52012,7130240,7164941,550066,832310,7175925,738681,980695,7127239,550428,511688,25088,7172096,7103919,7158346,7107496,188288,933113,998831,700785,277746,802874,7137902,862118,201156,870171,520649,968198,120012,860921,881004,191055,320223,195504,909163,882311,58611,682311,25388,21888,658853,155344,868033,201106,188584,113633,850269,567788,940827,925222,688658,323858,120281,818980,749996,226228,123966,775885,900269,65432,779919,685038,917963,668669,586008,886788,21988,646095,348660,262321,119921,767722,832600,544951,664189,920927,629464,821185,101169,731932,723014,537799,771670,650083,650080,672867,500194,279684,663388,200076,159969,367003,112211,789918,924499,730997,928082,119145,729979,600918,300503,837997,977002,129881,530628,255729,171493,554434,930323,289488,921231,386689,518998,319350,768369,181561,317797,534499,686093,192800,549194,127030,220382,889700,817412,741818,807876,716826,662372,127714,672421,225656,128939,717798,891003,773681,709758,707989,283004,109810,197369,375838,896863,360530,648393,120539,232123,320070,880928,201773,193016,726209,661400,175177,886112,201852,235563,890518,815230,145783,132213,191242,201810,850063,737105,161164,213866,706268,788557,165867,711019,242201,141662,876117,680166,243919,105807,201941,940106,720719,106308,267886,588253,944162,733056,510630,886622,300514,288013,324401,858899,909780,260762,818568,952361,744471,716261,858123,789199,157704,163065,660825,230745,977482,224192,201811,183258,700096,988006,988022,988012,988007,988013,988023,988014,988015,988025,988031,988026,988028,988020,988029,988036,134216,100700,891233,288799,725506,856835,245913,370820,198804,966886,570614,771432,109412,332990,150298,927017,122853,980901,180856,799530,985211,968020,504014,668158,319604,775533,199105,901202,158309,523488,581134,348988,368728,997809,126211,801824,224508,698067,883345,988938,652634,286628,807260,279310,715277,223881,802336,340991,297556,756596,885600,682599,338855,786222,292539,563554,927581,808084,744577,857855,688737,121129,692811,696221,636688,355699,530460,225426,235987,939533,330088,574458,615821,900908,122363,268411,372759,382287,858868,665951,626061,298251,510089,907719,114651,950621,102050,900601,210043,412928,493180,476410,112231,493835,437453,419094,455651,352222,567867,346668,595591,854448,892111,405328,823872,211141,451005,479738,311158,187879,599968,602190,606012,467980,112227,749997,768684,436722,423251,888536,226262,679191,403373,452435,211158,525151,303434,179792,460594,650518,111278,485315,427065,465606,400560,787666,499928,691919,355554,777222,755529,467215,888638,451775,406808,302999,492786,809991,621116,441004,498844,409744,566645,984321,416563,478582,726269,453197,888815,468534,443328,888917,475931,471614,558581,473110,696987,493977,880808,602888,410098,555737,490687,499142,459865,451210,431623,447630,244476,456518,155583,436753,788888,777989,255584,680111,769888,944400,456182,577718,470690,522203,403637,404366,571716,194440,333604,800057,492033,479647,442812,350123,498439,450785,433725,445989,460159,462242,468173,345675,429405,944461,417255,488898,492071,222712,520009,467132,481825,238080,654000,888572,336262,432647,437082,436063,999530,426409,433741,406143,410050,935777,999463,154343,264666,455096,594777,603336,474554,292911,614888,431271,222271,241212,989804,435961,555820,159999,445604,469474,448168,944442,437320,491925,187659,410515,553553,656664,518000,450138,503777,411026,203111,483432,405595,252509,474420,366363,434644,437774,376999,407756,562111,428097,988989,437825,409508,406705,414349,427625,102525,765419,641416,373232,418566,160333,453539,415207,421931,418625,288882,777498,950202,451296,244486,487046,448608,331616,411847,443621,476707,498895,206868,116116,429595,460799,222146,422403,262676,448205,481548,445559,449548,595958,484192,420447,732424,469614,498305,408409,772323,421554,855573,400500,558989,666081,402543,462421,482045,417427,452560,433914,740555,441396,484191,111740,444687,483913,422362,456790,316666,666953,452537,483750,443772,667672,405257,321005,359993,188846,445799,805656,898444,460141,427705,666184,555532,999218,302223,404554,403504,292222,888885,439736,487833,681113,898673,936716,502959,620871,950172,365214,396405,713220,384157,384950,602963,856583,331609,741083,213265,349120,911992,525154,925568,350967,906741,356634,546697,715322,644623,353158,875065,152321,840447,717092,187644,340026,940682,861909,273795,570546,350173,271064,771845,710068,291210,153663,113497,837447,885386,144186,314751,718238,539184,700139,213205,650754,830779,195082,380854,956733,540994,355308,183028,992659,764914,750059,670278,560991,743788,282283,555002,605547,706898,334706,648960,858185,588150,106963,608184,787116,954983,937395,336083,943959,167405,504023,384638,236125,313775,289079,730167,124159,165109,649234,627143,798817,885178,882085,550676,713968,559249,214246,659435,129597,938584,375640,972328,937491,862152,217737,210687,757131,281048,350529,890041,249367,530742,853105,609337,629416,130167,256613,922815,145321,968938,179747,189758,512852,390029,184668,681009,978091,916759,571450,531715,308022,511919,187323,250508,216625,162568,313882,935808,665719,642097,184762,575269,185166,655961,875883,116819,380758,216908,553473,365129,187890,670592,541966,279739,901974,510926,617077,799097,761199,665458,203677,691748,737186,119872,516480,749167,531145,581542,121507,259362,571705,753886,150093,998302,897892,833478,272612,336647,883660,230046,872185,207924,390522,744016,394281,928700,364177,741058,944657,631592,365296,265397,773085,526444,363483,366724,295256,736691,276275,248386,611230,718061,275206,332779,765270,618917,760762,628061,859585,187215,383363,289003,845018,321965,994467,751266,815515,597966,927305,141896,989946,367550,316058,932087,910756,305682,284387,746885,251337,264024,643583,272138,203069,304758,781944,602509,341125,579884,241440,718494,939832,607208,507054,657996,967986,362057,774190,116752,345334,326157,150258,983959,673620,984632,277183,587632,834229,311438,125037,835100,802509,280939,940659,635318,569722,232795,267928,684358,720239,282072,223504,970255,155742,500729,719062,125985,505463,796708,136252,629172,737197,121850,914396,578595,840341,801702,210227,937839,722970,138401,595490,140942,715256,802057,715299,915825,545831,230124,848614,304194,732927,581292,524482,378828,532140,926063,535828,250029,552941,512438,828920,110639,181807,867279,608283,909117,181977,998690,534656,391951,899241,362419,151002,900356,281911,113590,925018,965918,522610,819616,662138,668093,963369,861185,258217,606659,373681,569062,330068,110755,771382,532450,615753,260327,534112,529234,273484,951018,896553,332760,868904,226146,886455,236809,898991,962199,398688,930067,267728,913452,518312,688041,866881,570069,332811,711763,826026,584281,578780,693834,806861,719288,661846,518598,357537,233040,765692,875483,565010,628218,782735,311997,337784,154900,293408,920724,779977,212598,530709,646123,223073,884727,587396,792532,210203,873133,351113,603211,684146,139031,300487,123180,320253,907995,117611,380993,895082,282263,668792,299796,818426,835661,133245,614412,366030,580713,711945,740900,891022,230043,796115,816881,851221,707718,251107,231083,669569,178232,637071,383930,185430,782430,268770,280818,803078,123099,680886,110680,208219,909695,820118,678876,332434,876490,387481,864940,683325,123356,344840,263776,802285,606782,702279,375550,986688,686966,885539,928899,853472,851223,637259,927223,285660,509301,661981,805038,507704,699552,520522,960224,988119,579159,856852,122301,191019";
        String[] nameArray = names.split(",");
        for (String name : nameArray) {
            if ("g000".equals(DomainUtils.getGroupNo(name))) {
                System.out.println(name + ":g000");
                if (!DomainUtils.isValidMemberName(name)) {
                    System.out.print("无价值");
                }
            }

        }
    }

    @Test
    public void test20() {
        String a = null;
        ServiceUtils.doServiceTryCatch("测试", ApplicationTypeEnum.ADMIN, true, () -> {
            System.out.println(1);
            System.out.println(1);
            System.out.println(1);
            CommonDataProxy.get().tryLock("COMMON", "DISLOCK-ORDER-REFUND-jackyun_dev", 20000, () -> {
                System.out.println(1);
                a.equalsIgnoreCase("测试");
                return true;
            });
            System.out.println(1);
            System.out.println(1);
            System.out.println(1);
            System.out.println(1);
            return true;
        }, (Exception ex) -> {
            System.out.println(CoreUtils.exceptionToString(ex));
        }, () -> {
            System.out.println(1);
            return true;
        });
    }

    @Test
    public void test21() {
        for (int i = 0; i < 100; i++) {
            RDSDbEntity rdsDbEntity = getRDSDbByRule("jackyun_dev", null);
            rdsDbEntity = getRDSDbByRule("123", null);
            rdsDbEntity = getRDSDbByRule("456", null);
            rdsDbEntity = getRDSDbByRule("222", null);
        }
    }

    @Test
    public void test22() throws Exception {
        // 构造请求参数, sortedMap 可以让key按字典序排列。
        BuildeAuthorizeUrlRequestBizData bizRequestData = new BuildeAuthorizeUrlRequestBizData();
        // 作为授权型平台授权过程的唯一标识(获取sessionKey和同步账号，吉客号-店铺编号-用户编号)。
        bizRequestData.setState("123");

        SortedMap<String, String> sortedMap = new TreeMap<>();
        sortedMap.put("method", "Differ.JH.BuildeAuthorizeUrl");
        sortedMap.put("appkey", "a78c4fcbb8434f4096683d63b9dd19b4");
        sortedMap.put("token", "6352d413c36a43deaf3cac36e6ad9212");
        sortedMap.put("platid", "2");
        sortedMap.put("version", OmsApiConstant.POLY_VERSION);
        sortedMap.put("bizcontent", JsonUtils.toJson(bizRequestData));
        sortedMap.put("contenttype", OmsApiConstant.POLY_CONTENT_TYPE);
        sortedMap.put("timestamp", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        sortedMap.put("sign", PolyAPIUtils.makeSign("b944d3f623c84d79bffd5946a6025c48", sortedMap));

        //region 加入其他参数(私下约定的内部参数,不计入签名)

        //请求是否来自omsAPI。
        sortedMap.put("sourcefromtype", OmsApiConstant.SERVICE);
        sortedMap.put("outusername", "jackyun_test");
        // 店铺Id(同步账号保存店铺Id)。
        sortedMap.put("shopid", "1008477903386641536");
        String gateWay = "http://101.124.6.12/OpenAPI/do";
        ScheduledExecutorService es = new ScheduledThreadPoolExecutor(1);
        es.scheduleAtFixedRate(() -> {

            try {

                String result = HttpUtils.postData(gateWay, sortedMap);
                if (!result.contains("AppKey")) {
                    System.out.println(result);
                }
            } catch (IOException e) {
                e.printStackTrace();
            }

        }, 0, 200, TimeUnit.MILLISECONDS);

    }

    @Test
    public void test23() {
        String str = "";
        JSONObject object = JsonUtils.deJson(str);
        JSONArray array = object.getJSONObject("result").getJSONArray("data");
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < array.size(); i++) {
            JSONObject item = array.getJSONObject(i);
            if ("已冻结".equalsIgnoreCase(item.get("statusDesc").toString())) {
                sb.append(item.get("memberName")).append(",");
            }
        }
        System.out.println(sb.toString());
    }

    @Test
    public void test24() {
        OmsRedis redis = SpringResolveManager.resolve(OmsRedis.class);
        String value1 = redis.tryLockOnly("NORMAL", "LOCK.REDIS.TEST", 600000, 1000);
        String value2 = redis.tryLockOnly("NORMAL", "LOCK.REDIS.TEST", 600000, 1000);
        redis.unLock("NORMAL", "LOCK.REDIS.TEST", value1);
        redis.unLock("NORMAL", "LOCK.REDIS.TEST", value2);
    }

    @Test
    public  void test25() {
        super.init(true);
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        DomainUtils.bindMemberUser("jackyun_dev");
        int messageCount = 0;
        MQTypesEnum[] mqTypesArray = new MQTypesEnum[] { MQTypesEnum.DOWNLOADREFUNDORDERHANDLE, MQTypesEnum.DOWNLOADREFUNDORDERHANDLE_DEADLETTER };

        for (MQMonitorData monitorData : MQManager.get().getMQMonitor(mqTypesArray)) {
            //累加下载订单队列 和 对应的死信队列的消息数量。
            messageCount += monitorData.getMessageCount();
        }

        System.out.println(messageCount);
    }

    public static void main(String[] args) {
        String s = "";
        AdminManualDownloadTaobaoEntity entity = new AdminManualDownloadTaobaoEntity();
        List<AdminManualDownloadTaobaoEntity.UserDownloadInfo> dataUserInfo = new ArrayList<>();
//        AdminManualDownloadTaobaoEntity.UserDownloadInfo userDownloadInfo = new AdminManualDownloadTaobaoEntity.
//                UserDownloadInfo("688688", 874539555792019459L);
//        dataUserInfo.add(userDownloadInfo);
        //        AdminManualDownloadTaobaoEntity.UserDownloadInfo userDownloadInfo1 = new AdminManualDownloadTaobaoEntity.
        //                UserDownloadInfo("535890", 850980741646049280L);
        //        dataUserInfo.add(userDownloadInfo1);
        //
        //        AdminManualDownloadTaobaoEntity.UserDownloadInfo userDownloadInfo2 = new AdminManualDownloadTaobaoEntity.
        //                UserDownloadInfo("535890", 850980741578940419L);
        //        dataUserInfo.add(userDownloadInfo2);
        //
        //        AdminManualDownloadTaobaoEntity.UserDownloadInfo userDownloadInfo3 = new AdminManualDownloadTaobaoEntity.
        //                UserDownloadInfo("535890", 850980742661070852L);
        //        dataUserInfo.add(userDownloadInfo3);
        //
        //        AdminManualDownloadTaobaoEntity.UserDownloadInfo userDownloadInfo4 = new AdminManualDownloadTaobaoEntity.
        //                UserDownloadInfo("868106", 875153268098761344L);
        //        dataUserInfo.add(userDownloadInfo4);
        //
        //        AdminManualDownloadTaobaoEntity.UserDownloadInfo userDownloadInfo5 = new AdminManualDownloadTaobaoEntity.
        //                UserDownloadInfo("296588", 846272422337511808L);
        //        dataUserInfo.add(userDownloadInfo5);
        //
        //
        //        AdminManualDownloadTaobaoEntity.UserDownloadInfo userDownloadInfo6 = new AdminManualDownloadTaobaoEntity.
        //                UserDownloadInfo("296588", 855676727497688064L);
        //        dataUserInfo.add(userDownloadInfo6);
        //
        //        AdminManualDownloadTaobaoEntity.UserDownloadInfo userDownloadInfo7 = new AdminManualDownloadTaobaoEntity.
        //                UserDownloadInfo("360213", 952700545974108544L);
        //        AdminManualDownloadTaobaoEntity.UserDownloadInfo userDownloadInfo8 = new AdminManualDownloadTaobaoEntity.
        //                UserDownloadInfo("360213", 944874941566780416L);
        //       dataUserInfo.add(userDownloadInfo7);
        //        dataUserInfo.add(userDownloadInfo8);

        entity.setIsAllUser(true);
        entity.setIsActived(true);
        //        entity.setDataUserInfo(dataUserInfo);
        //        List<AdminManualDownloadTaobaoEntity.UserDownloadInfo> dataUserInfo1 = new ArrayList<>();
        //        AdminManualDownloadTaobaoEntity.UserDownloadInfo userDownloadInfo = new AdminManualDownloadTaobaoEntity.UserDownloadInfo();
        //        userDownloadInfo.setMemName("22222");
        //        userDownloadInfo.setShopId(981088800454935552L);
        //        AdminManualDownloadTaobaoEntity.UserDownloadInfo userDownloadInfo1 = new AdminManualDownloadTaobaoEntity.UserDownloadInfo();
        //        userDownloadInfo1.setMemName("22222");
        //        userDownloadInfo1.setShopId(990492906861233024L);
        //        dataUserInfo1.add(userDownloadInfo);
        //        dataUserInfo1.add(userDownloadInfo1);
        //        entity.setDataIgnoreUserInfo(dataUserInfo1);

        entity.setStartTime(LocalDateTime.of(2021, 5, 20, 21, 0, 0));
        entity.setEndTime(LocalDateTime.of(2021, 5, 21, 4, 0, 0));

        //entity.setShopTypesArray(new RDSShopTypesEnum[] { RDSShopTypesEnum.REFUND });

        System.out.println(JsonUtils.toJson(entity));
    }
}
