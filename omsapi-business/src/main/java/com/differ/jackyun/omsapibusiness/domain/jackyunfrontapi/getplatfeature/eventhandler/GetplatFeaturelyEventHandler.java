/**
 * Copyright(C) 2019 Hangzhou Differsoft Co., Ltd. All rights reserved.
 *
 */
package com.differ.jackyun.omsapibusiness.domain.jackyunfrontapi.getplatfeature.eventhandler;

import com.differ.jackyun.omsapibase.data.operation.dicplatfeature.DictPlatfeaturesEntity;
import com.differ.jackyun.omsapibase.domain.core.DomainEventHandler;
import com.differ.jackyun.omsapibase.domain.core.DomainUtils;
import com.differ.jackyun.omsapibase.domain.dataproxy.operation.DicPlatFeatureDataProxy;
import com.differ.jackyun.omsapibase.infrastructure.enums.ApplicationTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.ErrorCodes;
import com.differ.jackyun.omsapibase.infrastructure.exception.AppException;
import com.differ.jackyun.omsapibase.infrastructure.jackyunfrontapi.getplatfeature.GetplatFeatureRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.jackyunfrontapi.getplatfeature.GetplatFeatureResponseBizData;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.platfeatures.PolyAPIBusinessPlatFeaturesResponseBizData;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.platfeatures.extra.PlatInfo;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.core.PolyAPIPlatEnum;
import com.differ.jackyun.omsapibase.infrastructure.utils.ExtUtils;
import com.differ.jackyun.omsapibase.infrastructure.utils.JsonUtils;
import com.differ.jackyun.omsapibusiness.domain.jackyunfrontapi.getplatfeature.event.GetplatFeaturelyEvent;
import org.springframework.stereotype.Component;

import java.util.function.Supplier;

/**
 * 领域事件处理(获取平台特性，以及平台特性值)
 * @since 2019年5月29日 下午4:07:28
 * <AUTHOR>
 */
@Component
public class GetplatFeaturelyEventHandler implements DomainEventHandler<GetplatFeaturelyEvent> {
    //region 变量

    // endregion

    // region 方法

    /**
     * 执行委托方法
     */
    @Override
    public void handle(GetplatFeaturelyEvent event) throws Exception {
       /* 业务说明： 
        * 1. 查缓存数据
        * 2. 查api
        */

        // 创建GetplatFeatureRequestBizData对象。
        GetplatFeatureRequestBizData requestBizData = (GetplatFeatureRequestBizData) event.getRequestBizData();
        if (requestBizData == null) {
            this.writeBusinessLog(() -> ExtUtils.stringBuilderAppend("所传入的对象非GetplatFeatureRequestBizData对象"), event.getContextID(), event.getIsWriteTraceLog());
            throw new AppException(ErrorCodes.LOGICERROR, "所传入的对象非GetplatFeatureRequestBizData对象");
        }

        // 准备开始获取获取平台特性，以及平台特性值
        this.writeBusinessLog(() -> ExtUtils.stringBuilderAppend("准备开始获取获取平台特性，以及平台特性值，请求参数报文：", JsonUtils.toJson(requestBizData)), event.getContextID(), event.getIsWriteTraceLog());

        //获取枚举
        PolyAPIPlatEnum platValue = PolyAPIPlatEnum.create(String.valueOf(requestBizData.getApiType()));

        // 从缓存中取出平台特性字典。
        DictPlatfeaturesEntity dicPlatFeatures = DicPlatFeatureDataProxy.get().getData(platValue);
        if (dicPlatFeatures == null) {
            // 未找到平台特性。
            this.writeBusinessLog(() -> ExtUtils.stringBuilderAppend("未找到平台特性:", platValue.getCaption()), event.getContextID(), event.getIsWriteTraceLog());

            throw new AppException(ErrorCodes.LOGICERROR, String.format("[%s]未找到平台特性", platValue.getPlatCName()));
        }

        // json对象反序列化为PolyAPIBusinessPlatFeaturesResponseBizData。
        PolyAPIBusinessPlatFeaturesResponseBizData platFeatures = JsonUtils.deJson(dicPlatFeatures.getJson(), PolyAPIBusinessPlatFeaturesResponseBizData.class);
        if (platFeatures == null) {
            // 平台特性Json对象反序列化为PolyAPIBusinessPlatFeaturesResponseBizData时为空。
            this.writeBusinessLog(() -> ExtUtils.stringBuilderAppend(platValue.getCaption(), "平台特性Json对象反序列化为PolyAPIBusinessPlatFeaturesResponseBizData时为空"), event.getContextID(), event.getIsWriteTraceLog());

            throw new AppException(ErrorCodes.LOGICERROR, String.format("[%s]平台特性Json对象反序列化为PolyAPIBusinessPlatFeaturesResponseBizData时为空", platValue.getPlatCName()));
        }

        // 获取平台特性。
        PlatInfo platInfo = platFeatures.getPlat();
        if (platInfo == null) {
            // 获取到的平台特性为null。
            this.writeBusinessLog(() -> ExtUtils.stringBuilderAppend("获取到的平台特性为null", platValue.getCaption()), event.getContextID(), event.getIsWriteTraceLog());

            throw new AppException(ErrorCodes.LOGICERROR, String.format("[%s]获取到的平台特性为null", platValue.getPlatCName()));
        }

        //封装数据
        GetplatFeatureResponseBizData responseBizData = new GetplatFeatureResponseBizData();
        responseBizData.setPlatformSpecific(platInfo);
        event.setResponseBizData(responseBizData);
    }

    // endregion

    //region 写入业务日志

    /**
     * 写入业务日志。
     * @param funCreateLogContent 生成日志内容的方法
     * @param contextID           业务日志上下文编号
     * @param isWriteBusinessLog  是否写入业务日志
     */
    private void writeBusinessLog(Supplier<StringBuilder> funCreateLogContent, Long contextID, boolean isWriteBusinessLog) {
        DomainUtils.writeBusinessLog(ApplicationTypeEnum.OPENAPI, contextID, () -> funCreateLogContent.get(), () -> isWriteBusinessLog);
    }

    //endregion
}
