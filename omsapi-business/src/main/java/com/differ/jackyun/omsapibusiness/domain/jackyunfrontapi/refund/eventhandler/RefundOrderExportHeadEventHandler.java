package com.differ.jackyun.omsapibusiness.domain.jackyunfrontapi.refund.eventhandler;

import com.differ.jackyun.omsapibase.data.refund.RefundExportHeadsEnum;
import com.differ.jackyun.omsapibase.domain.core.DomainEventHandler;
import com.differ.jackyun.omsapibase.domain.core.DomainUtils;
import com.differ.jackyun.omsapibase.infrastructure.enums.ApplicationTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.ErrorCodes;
import com.differ.jackyun.omsapibase.infrastructure.exception.AppException;
import com.differ.jackyun.omsapibase.infrastructure.jackyunfrontapi.refund.RefundOrderExportHeadRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.jackyunfrontapi.refund.RefundOrderExportHeadResponseBizData;
import com.differ.jackyun.omsapibase.infrastructure.utils.ExtUtils;
import com.differ.jackyun.omsapibase.infrastructure.utils.JsonUtils;
import com.differ.jackyun.omsapibusiness.domain.jackyunfrontapi.refund.event.RefundOrderExportHeadEvent;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 领域事件(退款单导出头)
 *
 * <AUTHOR>
 * @since 2019/6/19 11:00
 */
@Component
public class RefundOrderExportHeadEventHandler implements DomainEventHandler<RefundOrderExportHeadEvent> {
    //region 方法
    /**
     * 执行委托方法
     */
    @Override
    public void handle(RefundOrderExportHeadEvent event) throws Exception {
        RefundOrderExportHeadRequestBizData requestBizData = (RefundOrderExportHeadRequestBizData) event.getRequestBizData();
        if (requestBizData == null) {
            DomainUtils.writeBusinessLog(ApplicationTypeEnum.OPENAPI, event.getContextID(), () -> {
                return ExtUtils.stringBuilderAppend("所传入的对象非RefundOrderExportHeadRequestBizData对象");
            }, () -> {
                return event.getIsWriteTraceLog();
            });
            throw new AppException(ErrorCodes.LOGICERROR, "所传入的对象非RefundOrderExportHeadRequestBizData对象");
        }
        // 准备开始获取退款单导出头
        DomainUtils.writeBusinessLog(ApplicationTypeEnum.OPENAPI, event.getContextID(), () -> {
            return ExtUtils.stringBuilderAppend("准备开始获取退款单导出头，请求参数报文：", JsonUtils.toJson(requestBizData));
        }, () -> {
            return event.getIsWriteTraceLog();
        });

        Map<String,String> map = getRefundOrder(event.getMemberName(),requestBizData);
        RefundOrderExportHeadResponseBizData refundOrderExportHeadResponseBizData =new RefundOrderExportHeadResponseBizData();
        refundOrderExportHeadResponseBizData.setMap(map);
        event.setResponseBizData(refundOrderExportHeadResponseBizData);
    }
    /**
     * 查询并导出头
     *
     * @param requestBizDat 订单请求参数
     * @return Map
     */
    private Map<String,String> getRefundOrder(String memberName, RefundOrderExportHeadRequestBizData requestBizDat) {
        Map<String,String> map =new LinkedHashMap<>();
        for(String string :requestBizDat.getEnName()){
            for (RefundExportHeadsEnum headsEnum : RefundExportHeadsEnum.values()){
                if(headsEnum.getDbName().equals(string)){
                    map.put(headsEnum.getcNName(),headsEnum.getType());
                }
            }
        }
        return map;
    }
    //endregion
}
