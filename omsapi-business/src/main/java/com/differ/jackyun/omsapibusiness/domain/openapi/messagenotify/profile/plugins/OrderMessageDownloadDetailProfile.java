package com.differ.jackyun.omsapibusiness.domain.openapi.messagenotify.profile.plugins;

import com.differ.jackyun.omsapi.user.biz.infrastructure.utils.OrderNoticeUtils;
import com.differ.jackyun.omsapi.user.biz.tasks.mq.plugins.load.ApiOrderDetailLoadMqHandler;
import com.differ.jackyun.omsapibase.data.open.omsonline.TradeOnline;
import com.differ.jackyun.omsapibase.domain.core.DomainUtils;
import com.differ.jackyun.omsapibase.infrastructure.component.mq.data.DownloadOrderDetailMQData;
import com.differ.jackyun.omsapibase.infrastructure.enums.order.OrderTriggerTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.openapi.messagenotify.core.MessageNotifyBizData;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.core.PolyAPIPlatEnum;
import com.differ.jackyun.omsapibase.infrastructure.utils.JsonUtils;
import com.differ.jackyun.omsapibase.infrastructure.utils.SpringResolveManager;
import com.differ.jackyun.omsapibase.infrastructure.utils.YunConfigUtils;
import com.differ.jackyun.omsapibusiness.domain.openapi.messagenotify.profile.BaseOrderMessageNoticeProfile;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * 消息处理触发下载订单详情
 *
 * <AUTHOR>
 * @since 2023-12-13 11:35:53
 */
@Component("OrderMessageDownloadDetailProfile")
public class OrderMessageDownloadDetailProfile extends BaseOrderMessageNoticeProfile {

    /**
     * 业务实现。
     *
     * @param shopId               店铺ID
     * @param platValue            平台枚举值
     * @param tradeOnline          网店订单信息
     * @param messageNotifyBizData 消息通知内容
     * @return 业务处理结果
     */
    @Override
    public boolean doBusiness(Long shopId, PolyAPIPlatEnum platValue, TradeOnline tradeOnline, MessageNotifyBizData messageNotifyBizData) {
        // 封装下载订单详情参数
        DownloadOrderDetailMQData mqData = new DownloadOrderDetailMQData();
        mqData.setMemberName(DomainUtils.getMemberName());
        mqData.setShopId(shopId);
        mqData.setPlatOrderNos(Collections.singletonList(tradeOnline.getTradeNo()));
        mqData.setOperatorId(DomainUtils.getUserId());
        mqData.setOperatorName(DomainUtils.getUserName());
        mqData.setTriggerType(OrderTriggerTypeEnum.NOTICE);

        // 是否开启延迟下载，延迟下载发送到JMQ延迟队列中
        if (YunConfigUtils.enableDownloadOrderDetailDelay(messageNotifyBizData.getMessageType(), platValue, DomainUtils.getMemberName())) {
            OrderNoticeUtils.sendOrderDownloadDetailNoticeToDelay(JsonUtils.toJson(mqData));
        } else {
            SpringResolveManager.resolve(ApiOrderDetailLoadMqHandler.class).send(JsonUtils.toJson(mqData));
        }
        return true;
    }
}
