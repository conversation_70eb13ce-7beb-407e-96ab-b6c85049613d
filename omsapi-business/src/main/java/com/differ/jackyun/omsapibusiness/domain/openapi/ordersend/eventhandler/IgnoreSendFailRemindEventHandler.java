package com.differ.jackyun.omsapibusiness.domain.openapi.ordersend.eventhandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.differ.jackyun.framework.component.basic.member.member.MemberHolder;
import com.differ.jackyun.omsapibase.dao.jackyunmultidb.omsonline.DaoTradeOnlineDBSwitchMapper;
import com.differ.jackyun.omsapibase.dao.jackyunmultidb.omsonline.DaoTradeOnlineFlagDBSwitchMapper;
import com.differ.jackyun.omsapibase.dao.jackyunmultidb.omsonline.DaoTradeOnlineLogDBSwitchMapper;
import com.differ.jackyun.omsapibase.data.common.YesOrNo;
import com.differ.jackyun.omsapibase.data.open.omsonline.TradeOnline;
import com.differ.jackyun.omsapibase.data.open.omsonline.TradeOnlineFlag;
import com.differ.jackyun.omsapibase.data.open.omsonline.TradeOnlineLog;
import com.differ.jackyun.omsapibase.domain.core.DomainEventHandler;
import com.differ.jackyun.omsapibase.domain.core.DomainUtils;
import com.differ.jackyun.omsapibase.infrastructure.enums.ErrorCodes;
import com.differ.jackyun.omsapibase.infrastructure.enums.order.OrderOriginalFlagEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.order.OriginalOrderSendStatusEnum;
import com.differ.jackyun.omsapibase.infrastructure.jackyunfrontapi.send.IgnoreSendFailRemindRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.jackyunfrontapi.send.IgnoreSendFailRemindResponseBizData;
import com.differ.jackyun.omsapibase.infrastructure.utils.ValidateResult;
import com.differ.jackyun.omsapibase.infrastructure.utils.ValidateResultList;
import com.differ.jackyun.omsapibusiness.domain.openapi.ordersend.event.IgnoreSendFailRemindEvent;
import org.apache.commons.collections.CollectionUtils;
import org.apache.kafka.common.errors.ApiException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 忽略物流上传失败领域事件
 *
 * <AUTHOR> hzj
 * @date 2020年11月25日18:54:03
 */
@Component
public class IgnoreSendFailRemindEventHandler implements DomainEventHandler<IgnoreSendFailRemindEvent> {

    @Autowired
    private DaoTradeOnlineDBSwitchMapper daoTradeOnlineDBSwitchMapper;

    @Autowired
    private DaoTradeOnlineLogDBSwitchMapper tradeOnlineLogDBSwitchMapper;

    @Autowired
    private DaoTradeOnlineFlagDBSwitchMapper daoTradeOnlineFlagDBSwitchMapper;


    private final static int MAX_DEAL_INFO = 100;

    /**
     * 业务处理
     *
     * @param event 领域事件
     * @throws Exception 异常
     */
    @Override
    public void handle(IgnoreSendFailRemindEvent event) throws Exception {

        if (event == null || event.getRequestBizData() == null) {
            throw new ApiException("忽略发货失败提醒,传入参数为空");
        }
        IgnoreSendFailRemindRequestBizData requestBizData = (IgnoreSendFailRemindRequestBizData) event.getRequestBizData();
        // 导入错误信息
        IgnoreSendFailRemindResponseBizData responseBizData = new IgnoreSendFailRemindResponseBizData();
        ValidateResultList validateResultList = new ValidateResultList();
        List<Long> tradeIdList = requestBizData.getTradeIdList();
        //请求必填参数为空，直接返回错误
        if (CollectionUtils.isEmpty(tradeIdList)) {
            throw new ApiException("忽略发货失败提醒,传入参数为空");
        }

        // 按100拆分，防止大批量i/o
        int fromIndex = 0;
        while (fromIndex + MAX_DEAL_INFO < tradeIdList.size()) {
            this.deal(event, validateResultList, tradeIdList.subList(fromIndex, fromIndex + MAX_DEAL_INFO));
            fromIndex += MAX_DEAL_INFO;
        }
        this.deal(event, validateResultList, tradeIdList.subList(fromIndex, tradeIdList.size()));

        if (!validateResultList.isEmpty()) {
            responseBizData.setValidateResultList(validateResultList);
            event.setResponseBizData(responseBizData);
        }
    }

    private void deal(IgnoreSendFailRemindEvent event, ValidateResultList validateResultList, List<Long> tradeIdList) {
        //查询订单列表
        List<TradeOnline> tradeOnlineList = daoTradeOnlineDBSwitchMapper.getTradeOnlineInfo(event.getMemberName(), tradeIdList);
        if (CollectionUtils.isEmpty(tradeOnlineList)) {
            this.packageFailResult(tradeIdList, validateResultList, ErrorCodes.SEARCHDBFAIL);
            return;
        }

        //筛选需要忽略提醒的订单
        List<TradeOnline> ignoreTradeOnlineList = this.screenNeedIgnoreTradeOnlineList(tradeOnlineList, validateResultList);
        if (CollectionUtils.isEmpty(ignoreTradeOnlineList)) {
            return;
        }

        //过滤已经设置过忽略提醒的订单
        ignoreTradeOnlineList = this.filterHasBeenIgnoredTradeOnlineList(ignoreTradeOnlineList, validateResultList);
        if (CollectionUtils.isEmpty(ignoreTradeOnlineList)) {
            return;
        }

        List<TradeOnlineFlag> flagList = new ArrayList<>();
        ignoreTradeOnlineList.forEach(tradeOnline -> {
            String sysFlagIds = tradeOnline.getSysFlagIds();
            List<String> sysFlagIdList = JSONObject.parseArray(sysFlagIds, String.class);
            if (sysFlagIdList == null) {
                sysFlagIdList = new ArrayList<>();
            }
            sysFlagIdList.add(String.valueOf(OrderOriginalFlagEnum.IGNORE_FAIL_REMIND.getValue()));
            tradeOnline.setSysFlagIds(JSON.toJSONString(sysFlagIdList));

            flagList.add(convert2TradeOnlineFlag(tradeOnline));
        });
        //更新物流上传状态
        this.daoTradeOnlineDBSwitchMapper.batchUpdateTradeOnlineFlags(event.getMemberName(), ignoreTradeOnlineList);

        this.daoTradeOnlineFlagDBSwitchMapper.batchAddOrderOriginalFlag(event.getMemberName(), flagList);

        //记录日志
        List<TradeOnlineLog> logList = new ArrayList<>();
        ignoreTradeOnlineList.forEach(tradeOnline -> logList.add(TradeOnlineLog.make(tradeOnline.getTradeId(), DomainUtils.getUserId()
                , DomainUtils.getRealName(), "忽略物流信息上传失败提醒")));
        this.tradeOnlineLogDBSwitchMapper.batchAddOrderOriginalLog(MemberHolder.getMemberName(), logList);
    }

    /**
     * 过滤已经设置过忽略提醒的订单
     *
     * @param ignoreTradeOnlineList 待忽略提醒订单
     * @param validateResultList    错误集合
     * @return 待忽略提醒订单
     */
    private List<TradeOnline> filterHasBeenIgnoredTradeOnlineList(List<TradeOnline> ignoreTradeOnlineList, ValidateResultList validateResultList) {

        List<TradeOnline> needIgnoreTradeOnlineList = new ArrayList<>();
        ignoreTradeOnlineList.forEach(tradeOnline -> {
            if (StringUtils.isEmpty(tradeOnline.getSysFlagIds())
                    || !tradeOnline.getSysFlagIds().contains(OrderOriginalFlagEnum.IGNORE_FAIL_REMIND.getValue().toString())) {
                needIgnoreTradeOnlineList.add(tradeOnline);
            } else {
                validateResultList.add(ValidateResult.make(ErrorCodes.HAS_BEEN_IGNORED, tradeOnline.getTradeId()));
            }
        });

        return needIgnoreTradeOnlineList;
    }

    /**
     * 筛选需要忽略提醒的订单
     *
     * @param tradeOnlineList    订单集合
     * @param validateResultList 错误集合
     * @return 需要忽略提醒的订单
     */
    private List<TradeOnline> screenNeedIgnoreTradeOnlineList(List<TradeOnline> tradeOnlineList, ValidateResultList validateResultList) {
        List<TradeOnline> ignoreTradeOnlineList = new ArrayList<>();
        tradeOnlineList.forEach(tradeOnline -> {
            if (OriginalOrderSendStatusEnum.SHIP_FAILED.getValue().equals(tradeOnline.getSynStatus())
                    || OriginalOrderSendStatusEnum.SHIP_FAILED_SPLIT.getValue().equals(tradeOnline.getSynStatus())) {
                ignoreTradeOnlineList.add(tradeOnline);
            } else {
                validateResultList.add(ValidateResult.make(ErrorCodes.NOT_SHIP_FAILED, tradeOnline.getTradeId()));
            }
        });
        return ignoreTradeOnlineList;
    }

    /**
     * 封装错误信息
     *
     * @param tradeIdList        tradeId 集合
     * @param validateResultList 错误集合
     * @param errorCodes         错误描述
     */
    private void packageFailResult(List<Long> tradeIdList, ValidateResultList validateResultList, ErrorCodes errorCodes) {
        tradeIdList.forEach(tradeId -> validateResultList.add(ValidateResult.make(errorCodes, tradeId)));

    }

    /**
     * 转换为tradeOnlineFlag
     *
     * @param tradeOnline 网店订单
     * @return 标记实体
     */
    public static TradeOnlineFlag convert2TradeOnlineFlag(TradeOnline tradeOnline) {
        TradeOnlineFlag tradeOnlineFlag = new TradeOnlineFlag();
        tradeOnlineFlag.setTradeId(tradeOnline.getTradeId());
        tradeOnlineFlag.setFlagId(OrderOriginalFlagEnum.IGNORE_FAIL_REMIND.getValue());
        tradeOnlineFlag.setFlagName(OrderOriginalFlagEnum.IGNORE_FAIL_REMIND.getCaption());
        tradeOnlineFlag.setFlagType(YesOrNo.NO.getCode());
        return tradeOnlineFlag;
    }
}
