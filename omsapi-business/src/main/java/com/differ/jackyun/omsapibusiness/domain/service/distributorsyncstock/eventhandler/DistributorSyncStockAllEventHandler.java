package com.differ.jackyun.omsapibusiness.domain.service.distributorsyncstock.eventhandler;

import com.differ.jackyun.framework.component.basic.member.member.MemberHolder;
import com.differ.jackyun.omsapibase.data.distributorsyncstock.syncstocklist.DistributorSyncStockListOpenApiRequestBizData;
import com.differ.jackyun.omsapibase.data.distributorsyncstock.syncstocklist.DistributorSyncStockListOpenApiResponseBizData;
import com.differ.jackyun.omsapibase.domain.core.DomainEventHandler;
import com.differ.jackyun.omsapibase.domain.core.DomainEventHandlerAsynchronously;
import com.differ.jackyun.omsapibase.domain.core.DomainEventServiceManager;
import com.differ.jackyun.omsapibase.domain.core.DomainUtils;
import com.differ.jackyun.omsapibase.domain.dataproxy.stock.DistributorSyncStockAllProgressDataProxy;
import com.differ.jackyun.omsapibase.infrastructure.component.mq.data.business.DistributorStockSyncAllMqData;
import com.differ.jackyun.omsapibase.infrastructure.component.mq.omsmq.MQTypesEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.ApplicationTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.utils.*;
import com.differ.jackyun.omsapibase.tasks.mq.MQManager;
import com.differ.jackyun.omsapibusiness.domain.jackyunfrontapi.distributorssyncstock.event.DistributorSyncStockListEvent;
import com.differ.jackyun.omsapibusiness.domain.service.distributorsyncstock.event.DistributorSyncStockAllEvent;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import static com.differ.jackyun.omsapibase.infrastructure.enums.ConfigKeyEnum.SYNCSTOCK_DISTRIBUTOR_ALL_BREAK;
import static com.differ.jackyun.omsapibase.infrastructure.enums.ConfigKeyEnum.SYNCSTOCK_DISTRIBUTOR_ALL_PAGESIZE;

/**
 * 分销商库存同步一键同步(分页）
 *
 * <AUTHOR>
 * @since 2020-05-11 16:06
 */
@Component
@DomainEventHandlerAsynchronously
public class DistributorSyncStockAllEventHandler implements DomainEventHandler<DistributorSyncStockAllEvent> {

    @Override
    public void handle(DistributorSyncStockAllEvent event) throws Exception {
        //会员绑定
        DomainUtils.bindMemberUser(event.getMemName());

        //获取该分销商的总数
        int count = this.getDistributorSyncStockCount(event.getDistributorName());
        BusinessLogUtils.write(DomainUtils.autoBindContextID(), String.format("分销商库存同步一键同步(分页）- 分销商名 %s，总数 %s", event.getDistributorName(), count));
        //判断总数为0则返回,否则开始分页获取并发送MQ
        if (count == 0) {
            DistributorSyncStockAllProgressDataProxy.get().deleteTask(event.getMemName(),event.getDistributorName());
            return;
        }

        //分页获取该分销商的库存同步货品，并分页丢入MQ进行库存同步。
        boolean isHasNextPage = true;
        int index = 0;
        int size = Integer.parseInt(YunConfigUtils.getDBConfig(SYNCSTOCK_DISTRIBUTOR_ALL_PAGESIZE));
        boolean isFistToMQ = true;
        while (isHasNextPage) {
            //如果配置了中断，则不发起任务
            if (YunConfigUtils.contains(event.getMemName(), SYNCSTOCK_DISTRIBUTOR_ALL_BREAK)) {
                break;
            }

            //分页获取
            DistributorSyncStockListOpenApiResponseBizData bizData = this.getDistributorSyncStockList(index, size, event.getDistributorName());
            //当前页有问题，
            if (bizData == null) {
                index++;
                continue;
            }

            //判断是否停止分页
            List<DistributorSyncStockListOpenApiResponseBizData.DistributorSyncStockItem> itemList = bizData.getItemList();
            if (CollectionUtils.isEmpty(itemList) || itemList.size() < size) {
                isHasNextPage = false;
            }

            //发送MQ
            DistributorStockSyncAllMqData mqData = new DistributorStockSyncAllMqData();
            mqData.setMemberName(MemberHolder.getMemberName());
            mqData.setjLinkId(Long.parseLong(bizData.getItemList().get(0).getJlinkId()));
            mqData.setDistributorMemberName(bizData.getItemList().get(0).getDistributorAccountId());
            mqData.setDistributorName(event.getDistributorName());
            mqData.setItemList(itemList.stream().map(item -> {
                DistributorStockSyncAllMqData.DistributorManualSyncStockItem mqItem = new DistributorStockSyncAllMqData.DistributorManualSyncStockItem();
                mqItem.setDistributorId(item.getDistributorId());
                mqItem.setDistributorSkuId(String.valueOf(item.getDistributorSkuId()));
                mqItem.setVendSkuId(String.valueOf(item.getVendSkuId()));
                return mqItem;
            }).collect(Collectors.toList()));
            MQManager.get().sendMessage(MQTypesEnum.AUTOSTOCKSYNC_DISTRIBUTOR_ALL, JsonUtils.toJson(mqData));

            //任务进度开始记录
            if (isFistToMQ) {
                DistributorSyncStockAllProgressDataProxy.get().addFirstTask(MemberHolder.getMemberName(), mqData.getDistributorName());
            } else {
                DistributorSyncStockAllProgressDataProxy.get().addOneTask(MemberHolder.getMemberName(), mqData.getDistributorName());
            }

            BusinessLogUtils.write(DomainUtils.autoBindContextID(),
                    String.format("分销商库存同步一键同步(分页）- 分销商ID:%s, 当前页pageIndex:%s，返回总数:%s", mqData.getDistributorMemberName(), index, itemList.size()));
            isFistToMQ = false;
            index++;
        }
    }

    /**
     * 获取当前分销商总数
     *
     * @param distributorName 分销商名称
     * @return 分销商总数
     */
    public Integer getDistributorSyncStockCount(String distributorName) throws Exception {
        DistributorSyncStockListEvent listEvent = new DistributorSyncStockListEvent();
        DistributorSyncStockListOpenApiRequestBizData requestBizData = new DistributorSyncStockListOpenApiRequestBizData();
        listEvent.setRequestBizData(requestBizData);
        requestBizData.setIsOnlyCount(true);
        requestBizData.setDistributorName("$lk$"+distributorName);
        requestBizData.setDistributorGoodsName("$in$");

        try {
            DomainEventServiceManager.get().publish(listEvent);
        }catch (Exception e){
            LogUtils.writeError(ApplicationTypeEnum.BUSINESS,"获取当前分销商总数失败："+CoreUtils.exceptionToString(e));
            return 0;
        }
        return ((DistributorSyncStockListOpenApiResponseBizData) listEvent.getResponseBizData()).getCountTotal();
    }

    /**
     * 获取当前页分销商库存同步货品
     *
     * @param distributorName 分销商名称
     * @return 分销商总数
     */
    public DistributorSyncStockListOpenApiResponseBizData getDistributorSyncStockList(Integer pageIndex, Integer pageSize, String distributorName) {
        DistributorSyncStockListOpenApiRequestBizData requestBizData = new DistributorSyncStockListOpenApiRequestBizData();
        DistributorSyncStockListEvent distributorSyncStockListEvent = new DistributorSyncStockListEvent();
        requestBizData.setDistributorName("$lk$"+distributorName);
        requestBizData.setDistributorGoodsName("$in$");
        requestBizData.setIsOnlyFindGoods(true);
        requestBizData.setPageSize(pageSize);
        requestBizData.setPageIndex(pageIndex);
        distributorSyncStockListEvent.setRequestBizData(requestBizData);

        // 事件发布
        try {
            DomainEventServiceManager.get().publish(distributorSyncStockListEvent);
        } catch (Exception e) {
            BusinessLogUtils.write(DomainUtils.autoBindContextID(),
                    String.format("分销商库存同步一键同步(分页）- distributorName:%s 当前页获取异常, pageIndex:%s，异常:%s", distributorName, pageIndex, CoreUtils.exceptionToString(e)));
            LogUtils.writeError(CoreUtils.exceptionToString(e));
            return null;
        }
        return (DistributorSyncStockListOpenApiResponseBizData) distributorSyncStockListEvent.getResponseBizData();
    }
}