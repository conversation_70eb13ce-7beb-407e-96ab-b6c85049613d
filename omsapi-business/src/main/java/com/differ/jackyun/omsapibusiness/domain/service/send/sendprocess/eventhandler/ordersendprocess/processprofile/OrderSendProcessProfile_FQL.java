package com.differ.jackyun.omsapibusiness.domain.service.send.sendprocess.eventhandler.ordersendprocess.processprofile;

import com.differ.jackyun.omsapibase.data.order.send.OrderInfoForSend;
import com.differ.jackyun.omsapibase.infrastructure.component.mq.data.worker.core.ext.SendOrderData;
import com.differ.jackyun.omsapibusiness.domain.service.send.sendprocess.eventhandler.ordersendprocess.BaseOrderSendProcessProfile;
import com.differ.jackyun.omsapibusiness.domain.service.send.sendprocess.eventhandler.ordersendprocess.OrderSendProcessUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 分期乐
 *
 * <AUTHOR>
 * @since 2021/6/16 11:02
 */
@Component
@Lazy(true)
public class OrderSendProcessProfile_FQL extends BaseOrderSendProcessProfile {

    /**
     * 封装3C信息
     */
    @Override
    public void packing3CInfo(OrderInfoForSend orderInfoForSend, SendOrderData sendOrderData) {
        OrderSendProcessUtils.packing3CInfo(orderInfoForSend, sendOrderData);
    }

}
