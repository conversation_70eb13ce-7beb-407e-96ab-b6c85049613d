package com.differ.jackyun.omsapibusiness.domain.service.send.sendprocess.eventhandler.ordersendprocess.processprofile;

import com.differ.jackyun.omsapibase.data.order.send.OrderInfoForSend;
import com.differ.jackyun.omsapibase.infrastructure.component.mq.data.worker.core.ext.SendOrderData;
import com.differ.jackyun.omsapibusiness.domain.service.send.sendprocess.eventhandler.ordersendprocess.BaseOrderSendProcessProfile;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 格格家发货特殊处理
 *
 * <AUTHOR> hjh
 * @date Date : 2020年02月14日 11:48
 */
@Component
@Lazy(true)
public class OrderSendProcessProfile_GGJ extends BaseOrderSendProcessProfile {
    // region 需要重写的方法

    /**
     * 格格家特殊处理
     *
     * @param orderInfoForSend 订单信息
     * @return
     */
    @Override
    public List<SendOrderData.InitProductData> packingProductsDataInfos(OrderInfoForSend orderInfoForSend) {
        List<SendOrderData.InitProductData> initProductDataList = new ArrayList<>(orderInfoForSend.getTradeOnlineGoodsList().size());
        orderInfoForSend.getTradeOnlineGoodsList().forEach(tradeOnlineGoods -> {
            SendOrderData.InitProductData productData = new SendOrderData.InitProductData();
            productData.setPlatGoodsId(tradeOnlineGoods.getPlatGoodsId());
            productData.setPlatSkuId(tradeOnlineGoods.getPlatSkuId());
            productData.setTradeGoodsNo(tradeOnlineGoods.getGoodsBarcode());
            productData.setSubTradeNo(tradeOnlineGoods.getSubTradeNo());
            productData.setSellCount(tradeOnlineGoods.getSellCount());
            initProductDataList.add(productData);
        });
        return initProductDataList;
    }
    // endregion
}
