package com.differ.jackyun.omsapibusiness.domain.service.send.sendprocess.eventhandler.ordersendprocess.processprofile;

import com.alibaba.fastjson.JSON;
import com.differ.jackyun.omsapibase.data.common.YesOrNo;
import com.differ.jackyun.omsapibase.data.open.oms.ExtraDto;
import com.differ.jackyun.omsapibase.data.order.send.OrderInfoForSend;
import com.differ.jackyun.omsapibase.data.shopconf.extra.ShopConfigExtra;
import com.differ.jackyun.omsapibase.infrastructure.component.mq.data.worker.core.ext.SendOrderData;
import com.differ.jackyun.omsapibase.infrastructure.enums.ErrorCodes;
import com.differ.jackyun.omsapibase.infrastructure.utils.JsonUtils;
import com.differ.jackyun.omsapibase.infrastructure.utils.ValidateResult;
import com.differ.jackyun.omsapibusiness.domain.service.send.sendprocess.eventhandler.ordersendprocess.BaseOrderSendProcessProfile;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 乐天发货处理
 *
 * <AUTHOR> hjl
 * @date Date : 2019年09月12日 11:48
 */
@Component
@Lazy(true)
public class OrderSendProcessProfile_Rakuten extends BaseOrderSendProcessProfile {

    /**
     * 封装发货回传信息
     *
     * @param orderInfoForSend 订单信息
     * @param sendOrderData    发货信息
     */
    @Override
    public void packingSendDateBack(OrderInfoForSend orderInfoForSend, SendOrderData sendOrderData) {
        super.packingSendDateBack(orderInfoForSend, sendOrderData);

        if (null != orderInfoForSend.getTradeOnlineExtra() && !StringUtils.isBlank(orderInfoForSend.getTradeOnlineExtra().getExtraData())) {
            ExtraDto extraDto = JsonUtils.deJson(orderInfoForSend.getTradeOnlineExtra().getExtraData(), ExtraDto.class);
            sendOrderData.setSendBackData(JSON.toJSONString(extraDto));
        }
    }

    @Override
    public ValidateResult checkOrderDataForOrderSend(OrderInfoForSend orderInfoForSend, ShopConfigExtra shopConfigExtra) {
        ValidateResult check = super.checkOrderDataForOrderSend(orderInfoForSend, shopConfigExtra);
        if (check != null) {
            return check;
        }
        if (null == orderInfoForSend.getTradeOnlineExtra() || StringUtils.isAllBlank(orderInfoForSend.getTradeOnlineExtra().getExtraData())) {
            ValidateResult.make(ErrorCodes.SEND_ERROR_RAKUTEN_NO_SORTING_CODE, orderInfoForSend.getTradeOnline().getTradeId(), YesOrNo.YES.getCode());
        }
        return null;
    }
}

