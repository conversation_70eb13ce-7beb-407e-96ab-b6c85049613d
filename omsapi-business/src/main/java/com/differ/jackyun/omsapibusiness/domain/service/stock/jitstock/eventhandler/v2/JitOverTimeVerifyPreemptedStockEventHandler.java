package com.differ.jackyun.omsapibusiness.domain.service.stock.jitstock.eventhandler.v2;

import com.differ.jackyun.framework.component.basic.member.member.MemberHolder;
import com.differ.jackyun.omsapibase.dao.jackyunmultidb.stock.JitPreemptedStockDBSwitchMapper;
import com.differ.jackyun.omsapibase.data.shopconf.entity.ShopConfigEntity;
import com.differ.jackyun.omsapibase.data.stock.JitPreemptedStockEntity;
import com.differ.jackyun.omsapibase.domain.core.DomainEventHandler;
import com.differ.jackyun.omsapibase.domain.core.DomainUtils;
import com.differ.jackyun.omsapibase.domain.dataproxy.shopconf.ShopConfDataProxy;
import com.differ.jackyun.omsapibase.infrastructure.enums.ApplicationTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.utils.CollectionsUtil;
import com.differ.jackyun.omsapibase.infrastructure.utils.LogUtils;
import com.differ.jackyun.omsapibase.infrastructure.utils.YunConfigUtils;
import com.differ.jackyun.omsapibusiness.domain.service.stock.jitstock.JitStockUtils;
import com.differ.jackyun.omsapibusiness.domain.service.stock.jitstock.event.v2.JitOverTimeVerifyPreemptedStockEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 超时归还预占库存领域事件
 *
 * <AUTHOR>
 * @since 2021年5月25日11:11:06
 */
@Component
public class JitOverTimeVerifyPreemptedStockEventHandler implements DomainEventHandler<JitOverTimeVerifyPreemptedStockEvent> {
    // region 引用

    /**
     * 预占库存数据库操作类
     */
    @Autowired
    private JitPreemptedStockDBSwitchMapper jitPreemptedStockDBSwitchMapper;

    /**
     * 店铺配置缓存类
     */
    @Autowired
    private ShopConfDataProxy shopConfDataProxy;

    // endregion

    // region 超时归还预占库存领域事件具体处理

    /**
     * 超时自动归还预占
     *
     * @param event 领域事件
     * @throws Exception 异常
     */
    @Override
    public void handle(JitOverTimeVerifyPreemptedStockEvent event) throws Exception {
        DomainUtils.doTryCatch("超时归还预占库存领域事件处理", ApplicationTypeEnum.BUSINESS, () -> {

            LogUtils.writeTrace(ApplicationTypeEnum.COMMON, "[超时归还预占库存领域事件处理]开始执行");

            // 根据会员查出需要归还预占的订单信息和商品信息
            List<JitPreemptedStockEntity> overTimePreemptedStockEntityList =
                    this.jitPreemptedStockDBSwitchMapper.getOverTimeVerifyPreemptedEntity(MemberHolder.getMemberName(), YunConfigUtils.getOverTimeVerifyPreemptedDays());
            if (CollectionsUtil.isBlank(overTimePreemptedStockEntityList)) {
                return true;
            }

            // 过滤出订单号
            List<String> overTimeOrderNoList = overTimePreemptedStockEntityList.stream().map(JitPreemptedStockEntity::getOrderNo).collect(Collectors.toList());
            if (CollectionsUtil.isBlank(overTimeOrderNoList)) {
                return true;
            }
            // 库中修改，将状态设置为已核销
            this.jitPreemptedStockDBSwitchMapper.editPreemptedEntityVerify(MemberHolder.getMemberName(), overTimeOrderNoList);

            // 预占库存按店铺id分组
            Map<Long, List<JitPreemptedStockEntity>> overTimePreemptedStockMap =
                    overTimePreemptedStockEntityList.stream().collect(Collectors.groupingBy(JitPreemptedStockEntity::getShopId));
            // 重算预占数量
            for (Map.Entry<Long, List<JitPreemptedStockEntity>> overTimePreemptedStockEntry : overTimePreemptedStockMap.entrySet()) {

                ShopConfigEntity shopConfig = this.shopConfDataProxy.getData(MemberHolder.getMemberName(), overTimePreemptedStockEntry.getKey());
                // 序列化店铺配置信息。
                if (shopConfig == null) {
                    continue;
                }
                List<JitPreemptedStockEntity> overTimePreemptedStockList = overTimePreemptedStockEntry.getValue();
                overTimePreemptedStockList.forEach(entity ->
                        JitStockUtils.updatePreemptedStock(MemberHolder.getMemberName(), shopConfig.getShopConfObj(), entity.getGoodsId()
                                , entity.getCooperationNo(), entity.getWarehouseCode()));
            }

            return true;
        });
    }

    // endregion


}