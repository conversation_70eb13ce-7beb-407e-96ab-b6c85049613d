package com.differ.jackyun.omsapibusiness.domain.service.stock.syncstock.eventhandler.process;

import com.differ.jackyun.framework.component.basic.member.member.MemberHolder;
import com.differ.jackyun.framework.component.basic.util.IdWorkerUtil;
import com.differ.jackyun.omsapibase.dao.jackyunmultidb.stock.ApiStockSyncLogDetailDbSwitchMapper;
import com.differ.jackyun.omsapibase.dao.jackyunmultidb.stock.StockSyncExtraConfigDBSwitchMapper;
import com.differ.jackyun.omsapibase.data.common.YesOrNo;
import com.differ.jackyun.omsapibase.data.shopconf.extra.ShopConfigExtra;
import com.differ.jackyun.omsapibase.data.stock.ApiStockSyncLogDetailEntity;
import com.differ.jackyun.omsapibase.data.stock.StockSyncExtraConfigEntity;
import com.differ.jackyun.omsapibase.data.stock.StockSyncMatchGoodsEntity;
import com.differ.jackyun.omsapibase.domain.core.DomainUtils;
import com.differ.jackyun.omsapibase.infrastructure.component.mq.data.SyncStockNoticeMQData;
import com.differ.jackyun.omsapibase.infrastructure.component.mq.data.business.ErpSyncStockEventMessageDto;
import com.differ.jackyun.omsapibase.infrastructure.enums.ApplicationTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.ErrorCodes;
import com.differ.jackyun.omsapibase.infrastructure.enums.LogTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.jackyunfrontapi.goodsbindling.GoodsBindingEntity;
import com.differ.jackyun.omsapibase.infrastructure.openapi.platgoods.TaoBaoGoodsUpDownStatusResponseDto;
import com.differ.jackyun.omsapibase.infrastructure.openapi.syncstocknotice.SyncStockNoticeRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.openapi.syncstocknotice.SyncStockResultOpenApiRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.enums.Business_SyncStockTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.core.PolyAPIPlatEnum;
import com.differ.jackyun.omsapibase.infrastructure.utils.*;
import com.differ.jackyun.omsapibusiness.domain.service.stock.syncstock.event.SyncStockProcessEvent;
import com.differ.jackyun.omsapibusiness.domain.service.stock.syncstock.eventhandler.ISyncStockProcess;
import com.differ.jackyun.omsapibusiness.domain.service.stock.syncstock.eventhandler.SyncStockProcessFactory;
import com.differ.jackyun.omsapibusiness.domain.service.stock.syncstock.eventhandler.processprofile.BaseSyncStockProcessProfile;
import com.differ.jackyun.omsapibusiness.domain.service.stockv2.entity.SyncStockRequireInfoEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 库存同步流程处理(基类)。
 *
 * <AUTHOR> hjl
 * @date Date : 2019年11月08日 15:55
 */
@Component("BaseSyncStockProcess")
public class BaseSyncStockProcess implements ISyncStockProcess {
    //region 变量

    @Autowired
    private StockSyncExtraConfigDBSwitchMapper stockSyncExtraConfigDBSwitchMapper;
    @Autowired
    private ApiStockSyncLogDetailDbSwitchMapper apiStockSyncLogDetailDbSwitchMapper;

    //endregion

    // region 公共方法

    // region 库存同步具体处理

    /**
     * 库存同步具体处理
     *
     * @param syncStockEvent 库存同步事件
     * @return
     */
    @Override
    public ValidateResultList handle(SyncStockProcessEvent syncStockEvent) {
        DomainUtils.doTryCatch("库存同步具体处理", ApplicationTypeEnum.BUSINESS, () -> {
            this.handleProfile(syncStockEvent);
            return true;
        });

        return null;
    }

    // endregion

    // region 库存同步处理流程

    /**
     * 库存同步处理流程
     *
     * @param syncStockProcessEvent 库存同步处理流程
     * @return
     */
    protected void handleProfile(SyncStockProcessEvent syncStockProcessEvent) {
        /**
         * 业务说明：
         *    1、获取所有需要库存同步的SkuId
         *    2、获取skuId下所有库存同步缓存信息(normal、jit，jit算预占库存有用)
         *    3、获取ERP仓库库存信息
         *    4、查询库存同步例外规则
         *    5、按每个SkuId对应具体库存信息开始遍历计算
         *      5.1、获取该skuId下对应的所有库存信息
         *      5.2、获取需要库存同步缓存对应的所有店铺配置信息
         *      5.3、库存同步数量开始计算(normal、jit)
         *      5.4、平台差异性扩展
         */

        // region 变量初始化

        // 操作员id
        String operatorId = DomainUtils.getUserId();
        // 操作员
        String operatorName = DomainUtils.getRealName();
        // 店铺配置实体
        Map<Long, ShopConfigExtra> shopConfigExtraMap = new HashMap<>();
        // 库存同步失败结果
        List<SyncStockResultOpenApiRequestBizData.StockSyncSkuResultDto> stockSyncSkuFailResultDtoList = new ArrayList<>();
        // 库存同步商品信息集合
        List<SyncStockNoticeMQData.SyncStockNoticeMQItem> syncStockNoticeMQItemList = new ArrayList<>();
        // 需要库存同步的缓存
        List<StockSyncMatchGoodsEntity> needStockSyncMatchGoodsEntityList = syncStockProcessEvent.getStockSyncMatchGoodsEntityList();
        // 库存同步日志详情
        List<ApiStockSyncLogDetailEntity> apiStockSyncLogDetailEntityList = new ArrayList<>();

        // endregion

        // 获取所有需要库存同步的SkuId
        List<Long> needSyncStockSkuIdList = BaseSyncStockProcessUtil.getNeedSyncStockSkuIdList(syncStockProcessEvent);

        // 获取skuId下所有库存同步缓存信息
        List<StockSyncMatchGoodsEntity> allStockSyncMatchGoodsEntityList = this.getAllStockSyncMatchGoodsEntity(syncStockProcessEvent);

        // 重新封装库存同步缓存信息
        this.rePackingStockSyncMatchGoodsEntity(syncStockProcessEvent.getStockSyncMatchGoodsEntityList(), shopConfigExtraMap);

        // 查询例外规则
        List<StockSyncExtraConfigEntity> stockSyncExtraConfigEntityList = BaseSyncStockProcessUtil.getStockSyncExtraConfigEntityList(needStockSyncMatchGoodsEntityList);

        // 需要库存同步缓存信息按skuId进行分组
        Map<Long, List<StockSyncMatchGoodsEntity>> needStockSyncMatchGoodsEntityMap = needStockSyncMatchGoodsEntityList.stream().collect(Collectors.groupingBy(StockSyncMatchGoodsEntity::getSkuId));

        // 查询所有店铺配置信息
        Set<Long> allShopIdSet = allStockSyncMatchGoodsEntityList.stream().map(StockSyncMatchGoodsEntity::getSalechannelid).collect(Collectors.toSet());
        List<ShopConfigExtra> allShopConfigExtraList = BaseSyncStockProcessUtil.searchShopConfigExtra(allShopIdSet, shopConfigExtraMap);
        if (CollectionsUtil.isBlank(allShopConfigExtraList)) {
            return;
        }

        // 获取ERP仓库库存信息
        List<ErpSyncStockEventMessageDto.ErpSyncStockGoodsInfo> erpSyncStockGoodsInfoList = BaseSyncStockProcessUtil.getAllErpSyncStockGoodsInfos(needSyncStockSkuIdList, allShopConfigExtraList, stockSyncExtraConfigEntityList);
        syncStockProcessEvent.setErpSyncStockGoodsInfoList(erpSyncStockGoodsInfoList);
        if (CollectionsUtil.isBlank(erpSyncStockGoodsInfoList)) {
            BaseSyncStockProcessUtil.writeBusinessLog(MemberHolder.getMemberName(), 0L, ErrorCodes.STOCK_SYNC_NO_AVAILABLEQUANTITY.getCaption(), LogTypeEnum.TRACE);
            return;
        }

        // 所有库存同步缓存信息按skuId进行分组
        Map<Long, List<StockSyncMatchGoodsEntity>> allStockSyncMatchGoodsEntityMap = allStockSyncMatchGoodsEntityList.stream().collect(Collectors.groupingBy(StockSyncMatchGoodsEntity::getSkuId));

        // 开始计时
        StopWatch stopWatch = CoreUtils.starDiffTime();

        // 开始计时
        StopWatch stopWatchTrace = CoreUtils.starDiffTime();

        for (Map.Entry<Long, List<StockSyncMatchGoodsEntity>> stockSyncMatchGoodsEntry : needStockSyncMatchGoodsEntityMap.entrySet()) {
            // region 相关初始化

            // 单skuId下需要库存同步缓存
            List<StockSyncMatchGoodsEntity> needStockSyncMatchGoodsEntitySingleSkuList = stockSyncMatchGoodsEntry.getValue();
            // 单SkuId下所有库存同步缓存
            List<StockSyncMatchGoodsEntity> allStockSyncMatchGoodsEntitySingleSkuList = allStockSyncMatchGoodsEntityMap.get(stockSyncMatchGoodsEntry.getKey());

            // endregion

            // 获取该SkuId对应的ERP库存信息
            ErpSyncStockEventMessageDto.ErpSyncStockGoodsInfo erpSyncStockGoodsInfo = erpSyncStockGoodsInfoList.stream().filter(erpSyncStockGoodsInfo1 -> erpSyncStockGoodsInfo1.getSkuId().equals(stockSyncMatchGoodsEntry.getKey())).findFirst().orElse(null);
            if (ObjectUtils.isEmpty(erpSyncStockGoodsInfo)) {
                continue;
            }

            // 获取该skuId对应缓存下所有店铺配置信息
            Set<Long> shopIdSet = allStockSyncMatchGoodsEntitySingleSkuList.stream().map(StockSyncMatchGoodsEntity::getSalechannelid).collect(Collectors.toSet());
            List<ShopConfigExtra> shopConfigExtraList = BaseSyncStockProcessUtil.searchShopConfigExtra(shopIdSet, shopConfigExtraMap);
            if (CollectionsUtil.isBlank(shopConfigExtraList)) {
                continue;
            }

            // 单skuId 库存同步计算
            List<SyncStockNoticeMQData.SyncStockNoticeMQItem> syncStockNoticeMQItemSingleSkuList = this.stockCalculationSingleSku(needStockSyncMatchGoodsEntitySingleSkuList, allStockSyncMatchGoodsEntitySingleSkuList, erpSyncStockGoodsInfo, erpSyncStockGoodsInfoList, shopConfigExtraList, stockSyncSkuFailResultDtoList, syncStockProcessEvent, stockSyncExtraConfigEntityList, apiStockSyncLogDetailEntityList);
            if (CollectionsUtil.isBlank(syncStockNoticeMQItemSingleSkuList)) {
                continue;
            }

            // 结果按店铺分组
            BaseSyncStockProcessUtil.processSyncStockItemGroupByShopId(syncStockNoticeMQItemList, syncStockNoticeMQItemSingleSkuList);
        }

        // 库存同步统计追踪
        BaseSyncStockProcessUtil.writeSyncStockStatisticsLog(String.format("[库存计算-可用库存版]耗时：%s毫秒", CoreUtils.diffTime(stopWatchTrace)));

        // 调用OmsApi接口进行库存同步
        if (CollectionsUtil.isNotBlank(syncStockNoticeMQItemList)) {
            SyncStockNoticeRequestBizData syncStockNoticeRequestBizData = SyncStockNoticeRequestBizData.make(MemberHolder.getMemberName(), System.currentTimeMillis(), syncStockNoticeMQItemList, operatorName, operatorId, syncStockProcessEvent.getRetryTimes());
            BaseSyncStockProcessUtil.syncStockNoticeToOmsApi(syncStockNoticeRequestBizData, erpSyncStockGoodsInfoList, syncStockProcessEvent);
        }

        // 记录库存同步失败记录
        if (CollectionsUtil.isNotBlank(stockSyncSkuFailResultDtoList)) {
            BaseSyncStockProcessUtil.publishStockSyncResultFailEvent(MemberHolder.getMemberName(), stockSyncSkuFailResultDtoList);
        }

        // 将日志详情入库
        if (CollectionsUtil.isNotBlank(apiStockSyncLogDetailEntityList)) {

            // 开始计时
            StopWatch stopWatch2 = CoreUtils.starDiffTime();

            // 详情入库
            this.apiStockSyncLogDetailDbSwitchMapper.batchAdd(MemberHolder.getMemberName(), apiStockSyncLogDetailEntityList);

            // 库存同步统计追踪
            BaseSyncStockProcessUtil.writeSyncStockStatisticsLog(String.format("[日志详情入库]详情数量：%d，耗时：%s毫秒", apiStockSyncLogDetailEntityList.size(), CoreUtils.diffTime(stopWatch2)));
        }

        // 记录耗时
        BaseSyncStockProcessUtil.writeBusinessLog(MemberHolder.getMemberName(), 0L, String.format("会员:%s,库存同步总耗时:%s", MemberHolder.getMemberName(), CoreUtils.diffTime(stopWatch)), LogTypeEnum.TRACE);
    }

    // endregion

    // region 同步结果封装

    /**
     * 同步结果封装
     *
     * @param stockSyncMatchGoodsEntity
     * @param quantity
     * @param shopConfigExtra
     * @return
     */
    protected SyncStockNoticeMQData.SyncStockNoticeMQItem.StockShopGoodsItem packingSyncStockShopGoodsItem(ErpSyncStockEventMessageDto.ErpSyncStockGoodsInfo erpSyncStockGoodsInfo, StockSyncMatchGoodsEntity stockSyncMatchGoodsEntity, BigDecimal quantity, ShopConfigExtra shopConfigExtra) {
        SyncStockNoticeMQData.SyncStockNoticeMQItem.StockShopGoodsItem stockShopGoodsItem = BaseSyncStockProcessUtil.convert2StockShopGoodsItem(stockSyncMatchGoodsEntity);
        // 若同步负库存至平台，同步0数量
        if (BigDecimal.ZERO.compareTo(quantity) > 0) {
            quantity = BigDecimal.ZERO;
        }
        stockShopGoodsItem.setQuantity(quantity);
        stockShopGoodsItem.setApiContextId(IdWorkerUtil.getId());

        // 允许是否自动上架 (菠萝派判断只判断!="down"上架，未判断是否下架，下架是平台自己的操作)。
        // 当库存数量大于指定值时才进行上架(防止频繁上下架影响店铺在平台的运营)。
        boolean shelves;
        // 兼容老数据，当为null或为0时，数量大于0则需要同步上架。
        if (shopConfigExtra.getGoodsStockSync().getShelvesQuantity() == null || shopConfigExtra.getGoodsStockSync().getShelvesQuantity().compareTo(BigDecimal.ZERO) == 0) {
            shelves = YesOrNo.YES.getCode().equals(shopConfigExtra.getGoodsStockSync().getIsAllowAutoShelves()) && quantity.compareTo(BigDecimal.ZERO) > 0;
        } else {
            shelves = YesOrNo.YES.getCode().equals(shopConfigExtra.getGoodsStockSync().getIsAllowAutoShelves()) && quantity.compareTo(shopConfigExtra.getGoodsStockSync().getShelvesQuantity()) > 0;
        }

        stockShopGoodsItem.setStatus(shelves ? "up" : "down");

        // 特殊平台特殊处理
        BaseSyncStockProcessProfile baseSyncStockProcessProfile = SyncStockProcessFactory.createSyncStockProcessProfile(shopConfigExtra.getShopType());
        baseSyncStockProcessProfile.packingSyncStockShopGoodsItemSpecial(stockShopGoodsItem, erpSyncStockGoodsInfo, stockSyncMatchGoodsEntity, shopConfigExtra);

        return stockShopGoodsItem;
    }

    // endregion

    // region 查询出平台商品信息

    /**
     * 查询出平台商品信息
     *
     * @param stockSyncMatchGoodsEntityList 库存同步缓存
     * @return
     */
    private void rePackingStockSyncMatchGoodsEntity(List<StockSyncMatchGoodsEntity> stockSyncMatchGoodsEntityList, Map<Long, ShopConfigExtra> shopConfigExtraMap) {
        /**
         * 业务说明：
         *    1、获取淘宝上下架状态
         *    2、获取淘宝时效商品信息
         */

        // 获取店铺配置信息
        Set<Long> shopIdSet = stockSyncMatchGoodsEntityList.stream().map(StockSyncMatchGoodsEntity::getSalechannelid).collect(Collectors.toSet());
        List<ShopConfigExtra> shopConfigExtraList = BaseSyncStockProcessUtil.searchShopConfigExtra(shopIdSet, shopConfigExtraMap);

        // 是否获取淘宝上下架状态
        if (YunConfigUtils.isGetTaoBaoUpDownStatusMembers(MemberHolder.getMemberName())) {
            this.packTaoBaoUpDownStatus(shopConfigExtraList, stockSyncMatchGoodsEntityList);
        }

        // 获取淘宝时效商品信息
        this.packTmallTimingGoods(shopConfigExtraList, stockSyncMatchGoodsEntityList);
    }

    /**
     * 封装淘宝上下架状态
     *
     * @param shopConfigExtraList
     * @param stockSyncMatchGoodsEntityList
     */
    private void packTaoBaoUpDownStatus(List<ShopConfigExtra> shopConfigExtraList, List<StockSyncMatchGoodsEntity> stockSyncMatchGoodsEntityList) {
        List<Long> taoBaoShopIdList = shopConfigExtraList.stream().filter(shopConfigExtra -> PolyAPIPlatEnum.BUSINESS_TAOBAO.equals(shopConfigExtra.getShopType())).map(ShopConfigExtra::getSalechannelId).collect(Collectors.toList());
        List<StockSyncMatchGoodsEntity> taoBaoStockSyncMatchGoodsEntityList = stockSyncMatchGoodsEntityList.stream().filter(stockSyncMatchGoodsEntity -> null != stockSyncMatchGoodsEntity.getSalechannelid() && taoBaoShopIdList.contains(stockSyncMatchGoodsEntity.getSalechannelid())).collect(Collectors.toList());
        if (CollectionsUtil.isBlank(taoBaoStockSyncMatchGoodsEntityList)) {
            return;
        }

        // 获取淘宝商品信息
        List<TaoBaoGoodsUpDownStatusResponseDto.TaobaoGoodsUpDownStatusItem> taobaoGoodsUpDownStatusItemList = BaseSyncStockProcessUtil.getTaoBaoUpDownStatusItems(stockSyncMatchGoodsEntityList);
        if (CollectionsUtil.isBlank(taobaoGoodsUpDownStatusItemList)) {
            return;
        }

        // 封装商品信息
        taoBaoStockSyncMatchGoodsEntityList.forEach(taoBaoStockSyncMatchGoodsEntity -> {
            TaoBaoGoodsUpDownStatusResponseDto.TaobaoGoodsUpDownStatusItem taobaoGoodsUpDownStatusItem = taobaoGoodsUpDownStatusItemList.stream().filter(taobaoGoodsUpDownStatusItem1 ->
                    null != taoBaoStockSyncMatchGoodsEntity.getPlatgoodsid() && taoBaoStockSyncMatchGoodsEntity.getPlatgoodsid().equals(taobaoGoodsUpDownStatusItem1.getPlatGoodsId())).findFirst().orElse(null);
            if (null != taobaoGoodsUpDownStatusItem && YesOrNo.YES.getCode().equals(taobaoGoodsUpDownStatusItem.getIsUp())) {
                taoBaoStockSyncMatchGoodsEntity.setIsUp(taobaoGoodsUpDownStatusItem.getIsUp());
            }
        });
    }

    /**
     * 封装淘宝时效商品信息
     *
     * @param shopConfigExtraList
     * @param stockSyncMatchGoodsEntityList
     */
    private void packTmallTimingGoods(List<ShopConfigExtra> shopConfigExtraList, List<StockSyncMatchGoodsEntity> stockSyncMatchGoodsEntityList) {
        // 查询开启时效商品店铺
        List<ShopConfigExtra> taoBaoShopConfigExtraList = shopConfigExtraList.stream().filter(shopConfigExtra -> PolyAPIPlatEnum.BUSINESS_TAOBAO.equals(shopConfigExtra.getShopType()) && YesOrNo.YES.getCode().equals(shopConfigExtra.getGoodsStockSync().getTmallTimingShopSwitch()) && CollectionsUtil.isNotBlank(shopConfigExtra.getGoodsStockSync().getTmallTimingWarehouseMappingList())).collect(Collectors.toList());
        if (CollectionsUtil.isBlank(taoBaoShopConfigExtraList)) {
            return;
        }

        List<Long> taoBaoShopIdList = taoBaoShopConfigExtraList.stream().map(ShopConfigExtra::getSalechannelId).collect(Collectors.toList());
        List<StockSyncMatchGoodsEntity> taoBaoStockSyncMatchGoodsEntityList = stockSyncMatchGoodsEntityList.stream().filter(stockSyncMatchGoodsEntity -> null != stockSyncMatchGoodsEntity.getSalechannelid() && taoBaoShopIdList.contains(stockSyncMatchGoodsEntity.getSalechannelid())).collect(Collectors.toList());
        if (CollectionsUtil.isBlank(taoBaoStockSyncMatchGoodsEntityList)) {
            return;
        }

        // 查询时效达商品信息
        List<GoodsBindingEntity> goodsBindingEntityList = BaseSyncStockProcessUtil.getTmallGoodsBindings(taoBaoStockSyncMatchGoodsEntityList);
        if (CollectionsUtil.isBlank(goodsBindingEntityList)) {
            return;
        }

        taoBaoStockSyncMatchGoodsEntityList.forEach(taoBaoStockSyncMatchGoodsEntity -> {
            GoodsBindingEntity goodsBindingEntity = goodsBindingEntityList.stream().filter(goodsBindingEntity1 ->
                    null != goodsBindingEntity1 && ExtUtils.getStringOrEmpty(taoBaoStockSyncMatchGoodsEntity.getPlatgoodsid()).equals(ExtUtils.getStringOrEmpty(goodsBindingEntity1.getPlatGoodsId()))
                            && ExtUtils.getStringOrEmpty(taoBaoStockSyncMatchGoodsEntity.getPlatskuid()).equals(ExtUtils.getStringOrEmpty(goodsBindingEntity1.getPlatSkuId()))).findFirst().orElse(null);
            if (null == goodsBindingEntity) {
                return;
            }

            taoBaoStockSyncMatchGoodsEntity.setTmallTimingGoodsItemId(goodsBindingEntity.getRealItemId());
        });
    }

    // endregion

    // endregion

    // region 需要重写的方法

    // region 单skuId 库存同步计算

    /**
     * 单skuId 库存同步计算
     *
     * @param needStockSyncMatchGoodsEntityList         需要库存同步的库存同步缓存
     * @param allStockSyncMatchGoodsEntitySingleSkuList 单sku所有库存同步缓存
     * @param erpSyncStockGoodsInfo                     erp货品库存
     * @param erpSyncStockGoodsInfoList                 所有库存同步信息
     * @param shopConfigExtraList                       店铺配置信息
     * @param stockSyncSkuFailResultDtoList             库存同步失败结果
     * @param syncStockProcessEvent                     库存同步事件
     * @param apiStockSyncLogDetailEntityList           库存同步日志详情
     * @return
     */
    protected List<SyncStockNoticeMQData.SyncStockNoticeMQItem>
    stockCalculationSingleSku(List<StockSyncMatchGoodsEntity> needStockSyncMatchGoodsEntityList, List<StockSyncMatchGoodsEntity> allStockSyncMatchGoodsEntitySingleSkuList, ErpSyncStockEventMessageDto.ErpSyncStockGoodsInfo erpSyncStockGoodsInfo, List<ErpSyncStockEventMessageDto.ErpSyncStockGoodsInfo> erpSyncStockGoodsInfoList, List<ShopConfigExtra> shopConfigExtraList, List<SyncStockResultOpenApiRequestBizData.StockSyncSkuResultDto> stockSyncSkuFailResultDtoList, SyncStockProcessEvent syncStockProcessEvent, List<StockSyncExtraConfigEntity> stockSyncExtraConfigEntityList, List<ApiStockSyncLogDetailEntity> apiStockSyncLogDetailEntityList) {
        /**
         * 业务说明：
         *    1、遍历库存同步缓存开始计算
         *    2、获取每个库存同步缓存对应的店铺配置信息
         *    3、计算
         *    4、结果汇总
         */

        // region 相关初始化

        // 单Sku耗时记录
        StopWatch singleSkuCalculationStopWatch = CoreUtils.starDiffTime();
        // 库存同步结果
        List<SyncStockNoticeMQData.SyncStockNoticeMQItem> syncStockNoticeMQItemList = new ArrayList<>();

        // endregion

        for (StockSyncMatchGoodsEntity stockSyncMatchGoodsEntity : needStockSyncMatchGoodsEntityList) {
            // 获取每个库存同步缓存对应的店铺配置信息
            ShopConfigExtra shopConfigExtra = shopConfigExtraList.stream().filter(shopConfigExtra1 -> stockSyncMatchGoodsEntity.getSalechannelid().equals(shopConfigExtra1.getSalechannelId())).findFirst().orElse(null);
            if (null == shopConfigExtra) {
                BaseSyncStockProcessUtil.addFailStockSyncSkuResultDto(stockSyncSkuFailResultDtoList, ErrorCodes.STOCK_SYNC_ERROR_NO_SHOP_CONFIG, stockSyncMatchGoodsEntity.getSalechannelid(), stockSyncMatchGoodsEntity, null, new SyncStockRequireInfoEntity());
                continue;
            }

            // 是否需要库存同步
            if (BaseSyncStockProcessUtil.isPlatDisableSyncStock(shopConfigExtra)) {
                BaseSyncStockProcessUtil.addFailStockSyncSkuResultDto(stockSyncSkuFailResultDtoList, ErrorCodes.STOCK_SYNC_ERROR_PLAT_DISABLE_ERROR, stockSyncMatchGoodsEntity.getSalechannelid(), stockSyncMatchGoodsEntity, null, new SyncStockRequireInfoEntity());
                continue;
            }

            // 计算
            List<SyncStockNoticeMQData.SyncStockNoticeMQItem.StockShopGoodsItem> stockShopGoodsItemList = this.stockSyncCalcutation(stockSyncMatchGoodsEntity, erpSyncStockGoodsInfo, erpSyncStockGoodsInfoList, shopConfigExtra, stockSyncSkuFailResultDtoList, syncStockProcessEvent, stockSyncExtraConfigEntityList, apiStockSyncLogDetailEntityList);
            if (CollectionsUtil.isBlank(stockShopGoodsItemList)) {
                continue;
            }

            // 结果汇总封装
            SyncStockNoticeMQData.SyncStockNoticeMQItem syncStockNoticeMQItem = syncStockNoticeMQItemList.stream().filter(syncStockNoticeMQItem1 -> syncStockNoticeMQItem1.getShopId().equals(shopConfigExtra.getSalechannelId())).findFirst().orElse(null);
            if (null == syncStockNoticeMQItem) {
                syncStockNoticeMQItem = SyncStockNoticeMQData.SyncStockNoticeMQItem.make(shopConfigExtra.getSalechannelId(), shopConfigExtra.getShopType(), new ArrayList<>());
                syncStockNoticeMQItemList.add(syncStockNoticeMQItem);
            }
            syncStockNoticeMQItem.getShopGoodsItems().addAll(stockShopGoodsItemList);
        }

        // 记录日志
        BaseSyncStockProcessUtil.writeBusinessLog(MemberHolder.getMemberName(), 0L, String.format(">>> 单skuId:%s,耗时为:%s,计算结果为:%s", erpSyncStockGoodsInfo.getSkuId(), CoreUtils.diffTime(singleSkuCalculationStopWatch), JsonUtils.toJson(syncStockNoticeMQItemList)), LogTypeEnum.TRACE);
        return syncStockNoticeMQItemList;
    }

    // endregion

    // region 单平台商品进行库存同步计算

    /**
     * 单平台商品进行库存同步计算
     *
     * @param stockSyncMatchGoodsEntity          库存同步匹配信息
     * @param erpSyncStockGoodsInfo              货品库存
     * @param erpSyncStockGoodsInfoList          所有ERP库存信息
     * @param shopConfigExtra                    店铺配置
     * @param stockSyncSkuFailResultDtoList      库存同步失败结果
     * @param syncStockProcessEvent              库存同步事件
     * @param allApiStockSyncLogDetailEntityList 库存同步日志详情
     * @return
     */
    protected List<SyncStockNoticeMQData.SyncStockNoticeMQItem.StockShopGoodsItem> stockSyncCalcutation(StockSyncMatchGoodsEntity stockSyncMatchGoodsEntity, ErpSyncStockEventMessageDto.ErpSyncStockGoodsInfo erpSyncStockGoodsInfo, List<ErpSyncStockEventMessageDto.ErpSyncStockGoodsInfo> erpSyncStockGoodsInfoList, ShopConfigExtra shopConfigExtra, List<SyncStockResultOpenApiRequestBizData.StockSyncSkuResultDto> stockSyncSkuFailResultDtoList, SyncStockProcessEvent syncStockProcessEvent, List<StockSyncExtraConfigEntity> stockSyncExtraConfigEntityList, List<ApiStockSyncLogDetailEntity> allApiStockSyncLogDetailEntityList) {
        /**
         * 业务说明：
         *     1、同步配置校验
         *     2、同步数量计算
         *     3、同步数量校验
         *     4、同步结果封装
         */

        // region 相关初始化

        // 同步错误结果
        ErrorCodes errorCodes = null;
        // 平台特殊处理类
        BaseSyncStockProcessProfile baseSyncStockProcessProfile = SyncStockProcessFactory.createSyncStockProcessProfile(shopConfigExtra.getShopType());

        // endregion

        // 同步配置校验
        if (null != (errorCodes = BaseSyncStockProcessUtil.checkStockSyncConfig(shopConfigExtra.getGoodsStockSync(), erpSyncStockGoodsInfo, erpSyncStockGoodsInfoList, stockSyncExtraConfigEntityList, stockSyncMatchGoodsEntity, syncStockProcessEvent))) {
            BaseSyncStockProcessUtil.addFailStockSyncSkuResultDto(stockSyncSkuFailResultDtoList, errorCodes, stockSyncMatchGoodsEntity.getSalechannelid(), stockSyncMatchGoodsEntity, null, new SyncStockRequireInfoEntity());
            return null;
        }

        // 获取库存同步更新方式（默认全量，为空时禁止同步）
        Business_SyncStockTypeEnum syncStockType = baseSyncStockProcessProfile.getSyncStockType(syncStockProcessEvent, stockSyncMatchGoodsEntity, shopConfigExtra);
        if (syncStockType == null) {
            return null;
        }

        // 同步数量计算
        BigDecimal quantity = BaseSyncStockProcessUtil.normalQuantityCalcutation(shopConfigExtra.getGoodsStockSync(), erpSyncStockGoodsInfo, erpSyncStockGoodsInfoList, stockSyncMatchGoodsEntity, stockSyncSkuFailResultDtoList, stockSyncExtraConfigEntityList);
        if (null == quantity) {
            return null;
        }

        // 同步数量校验
        if (null != (errorCodes = baseSyncStockProcessProfile.normalCheckSyncStockQuantity(syncStockProcessEvent, shopConfigExtra, erpSyncStockGoodsInfo, quantity))) {
            BaseSyncStockProcessUtil.addFailStockSyncSkuResultDto(stockSyncSkuFailResultDtoList, errorCodes, stockSyncMatchGoodsEntity.getSalechannelid(), stockSyncMatchGoodsEntity, null, new SyncStockRequireInfoEntity());
            return null;
        }

        // 同步结果封装(可根据平台重写)
        SyncStockNoticeMQData.SyncStockNoticeMQItem.StockShopGoodsItem stockShopGoodsItem = this.packingSyncStockShopGoodsItem(erpSyncStockGoodsInfo, stockSyncMatchGoodsEntity, quantity, shopConfigExtra);
        // 设置库存同步更新方式
        stockShopGoodsItem.setSyncStockType(syncStockType);

        // 同步结果拆分
        List<SyncStockNoticeMQData.SyncStockNoticeMQItem.StockShopGoodsItem> stockShopGoodsItemList =
                baseSyncStockProcessProfile.splitStockShopGoodsItem(erpSyncStockGoodsInfo, erpSyncStockGoodsInfoList, stockSyncMatchGoodsEntity, shopConfigExtra,
                        stockShopGoodsItem, null, stockSyncSkuFailResultDtoList, stockSyncExtraConfigEntityList, syncStockProcessEvent);

        // 同步日志记录
        List<ApiStockSyncLogDetailEntity> apiStockSyncLogDetailEntityList = BaseSyncStockProcessUtil.packingApiStockSyncLogDetails(stockShopGoodsItemList, erpSyncStockGoodsInfo, shopConfigExtra.getGoodsStockSync(), erpSyncStockGoodsInfoList, stockSyncMatchGoodsEntity, stockSyncExtraConfigEntityList);
        if (CollectionsUtil.isNotBlank(apiStockSyncLogDetailEntityList)) {
            allApiStockSyncLogDetailEntityList.addAll(apiStockSyncLogDetailEntityList);
        }

        return stockShopGoodsItemList;
    }

    // endregion

    // region 获取所有库存同步缓存信息


    /**
     * 获取所有库存同步缓存信息(非JIT用户库存同步计算不需要)
     *
     * @param syncStockProcessEvent
     * @return
     */
    protected List<StockSyncMatchGoodsEntity> getAllStockSyncMatchGoodsEntity(SyncStockProcessEvent syncStockProcessEvent) {
        return syncStockProcessEvent.getStockSyncMatchGoodsEntityList();
    }
    // endregion

    // endregion
}