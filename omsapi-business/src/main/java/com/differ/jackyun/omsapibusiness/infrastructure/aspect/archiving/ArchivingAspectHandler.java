package com.differ.jackyun.omsapibusiness.infrastructure.aspect.archiving;

import org.aspectj.lang.ProceedingJoinPoint;

/**
 * 归档切面类具体处理
 *
 * <AUTHOR>
 * @since 2020-09-14 13:34
 */
public abstract class ArchivingAspectHandler {
    // region 是否需要查询归档数据

    /**
     * 是否需要查询归档数据
     *
     * @param args
     * @return
     */
    protected abstract boolean isNeedArchiving(Object[] args);

    // endregion

    // region 归档数据查询

    /**
     * 归档数据查询
     *
     * @param joinPoint
     * @return
     */
    protected abstract Object archivingSearch(ProceedingJoinPoint joinPoint) throws Throwable;

    // endregion

    // region 绑定切表信息

    /**
     * 绑定切表信息
     */
    protected abstract void bindSwitchTable();

    // endregion

    // region 解绑切表信息

    /**
     * 解绑切表信息
     */
    protected abstract void unBindSwitchTable();

    // endregion
}