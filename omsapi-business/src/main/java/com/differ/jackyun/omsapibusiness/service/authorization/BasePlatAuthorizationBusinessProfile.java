package com.differ.jackyun.omsapibusiness.service.authorization;

import cn.hutool.core.date.CalendarUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.differ.jackyun.framework.component.basic.dto.JackYunResponse;
import com.differ.jackyun.framework.component.pagehelper.util.StringUtil;
import com.differ.jackyun.omsapi.core.infrastructure.config.business.ConfigUtil;
import com.differ.jackyun.omsapi.user.biz.domain.apicall.adapter.PlatAuthorizeAdapter;
import com.differ.jackyun.omsapi.user.biz.domain.apicall.data.ApiCallResponse;
import com.differ.jackyun.omsapi.user.biz.domain.apicall.plugins.SyncShopStatusApiCall;
import com.differ.jackyun.omsapi.user.biz.domain.apicall.plugins.shop.ShopSubscribeCancelApiCall;
import com.differ.jackyun.omsapi.user.biz.domain.apicall.plugins.subscribe.ShopMessageSubscribeApiCall;
import com.differ.jackyun.omsapi.user.biz.domain.core.ApiShopCoreUtils;
import com.differ.jackyun.omsapi.user.biz.domain.core.DomainUtil;
import com.differ.jackyun.omsapi.user.biz.domain.core.LoadFeatureUtils;
import com.differ.jackyun.omsapi.user.biz.domain.shopconfig.data.ApiShopBaseComposite;
import com.differ.jackyun.omsapi.user.biz.domain.shopconfig.data.ShopConfigCommonResult;
import com.differ.jackyun.omsapi.user.biz.domain.shopconfig.data.enhance.ShopModifyResult;
import com.differ.jackyun.omsapi.user.biz.domain.shopconfig.enhance.modify.AbstractShopModify;
import com.differ.jackyun.omsapi.user.biz.domain.shopconfig.enhance.modify.impl.ShopBaseModifyEnhance;
import com.differ.jackyun.omsapi.user.biz.domain.shopconfig.utils.ShopConfigStatisticsUtils;
import com.differ.jackyun.omsapi.user.biz.infrastructure.cache.remote.hash.shop.ApiShopAuthInfoCache;
import com.differ.jackyun.omsapi.user.biz.infrastructure.client.feign.api.internal.ApiLoadFeignClient;
import com.differ.jackyun.omsapi.user.biz.infrastructure.client.feign.api.internal.ShopFeignClient;
import com.differ.jackyun.omsapi.user.biz.infrastructure.common.LogAdapter;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.bizfeature.plugins.LoadFeatureConfigContent;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.shop.core.ApiShopAuthInfo;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.shop.core.ApiShopBaseInfo;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.shop.request.ShopAuthConflictNoticeRequestDto;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.shop.response.ShopAuthConflictNoticeResponseDto;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.platrequest.shop.ShopSubscribeCancelRequestBizData;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.platrequest.shop.ShopSubscribeCancelResponseBizData;
import com.differ.jackyun.omsapi.user.biz.service.shop.IApiShopAuthService;
import com.differ.jackyun.omsapi.user.biz.service.shop.IApiShopDataConvertService;
import com.differ.jackyun.omsapi.user.biz.service.shop.IShopAuthorizationAdapter;
import com.differ.jackyun.omsapibase.dao.jackyunmultidb.omsonline.DaoTradeOnlineDBSwitchMapper;
import com.differ.jackyun.omsapibase.dao.jackyunmultidb.shop.ConfShopLogDBMapper;
import com.differ.jackyun.omsapibase.dao.jackyunmultidb.shop.ShopDBSwitchMapper;
import com.differ.jackyun.omsapibase.dao.jackyunmultidb.shopv2.switchdb.ApiShopAuthSwitchMapper;
import com.differ.jackyun.omsapibase.dao.jackyunmultidb.shopv2.switchdb.ApiShopBaseSwitchMapper;
import com.differ.jackyun.omsapibase.data.common.PolyAPIAuthorizationEnum;
import com.differ.jackyun.omsapibase.data.common.YesOrNo;
import com.differ.jackyun.omsapibase.data.constant.OmsApiConstant;
import com.differ.jackyun.omsapibase.data.open.erp.BaseShopInfo;
import com.differ.jackyun.omsapibase.data.platauth.PlatAuthEntity;
import com.differ.jackyun.omsapibase.data.shopconf.command.AddOrUpdateShopConfCommand;
import com.differ.jackyun.omsapibase.data.shopconf.entity.ConfShopEntity;
import com.differ.jackyun.omsapibase.data.shopconf.entity.ConfShopLogEntity;
import com.differ.jackyun.omsapibase.data.shopconf.entity.ShopConfigEntity;
import com.differ.jackyun.omsapibase.data.shopconf.enums.PolyShopStatusEnum;
import com.differ.jackyun.omsapibase.data.shopconf.enums.ShopAuthStatusEnum;
import com.differ.jackyun.omsapibase.data.shopconf.extra.ShopConfigExtra;
import com.differ.jackyun.omsapibase.data.shopv2.entity.ApiShopAuthEntity;
import com.differ.jackyun.omsapibase.domain.core.DomainEventServiceManager;
import com.differ.jackyun.omsapibase.domain.core.DomainUtils;
import com.differ.jackyun.omsapibase.domain.dataproxy.platauth.PlatAuthDataProxy;
import com.differ.jackyun.omsapibase.domain.dataproxy.shopconf.ShopConfAutoDownloadDataProxy;
import com.differ.jackyun.omsapibase.domain.dataproxy.shopconf.ShopConfDataProxy;
import com.differ.jackyun.omsapibase.domain.platrequest.polyapi.business.authorization.event.BuildeAuthorizeUrlEvent;
import com.differ.jackyun.omsapibase.domain.platrequest.polyapi.business.authorization.event.CheckAccountEvent;
import com.differ.jackyun.omsapibase.domain.platrequest.polyapi.business.authorization.event.GetAuthorizeSessionKeyEvent;
import com.differ.jackyun.omsapibase.domain.platrequest.polyapi.business.authorization.event.SyncAccountEvent;
import com.differ.jackyun.omsapibase.domain.platrequest.polyapi.business.cancelauthorization.event.CancelAuthorizationEvent;
import com.differ.jackyun.omsapibase.domain.platrequest.polyapi.business.shopsubscribe.event.PolyAPIBusinessShopSubscribeEvent;
import com.differ.jackyun.omsapibase.feign.FeignGateway;
import com.differ.jackyun.omsapibase.feign.UserGroupFeignClient;
import com.differ.jackyun.omsapibase.infrastructure.component.thread.CommonTask;
import com.differ.jackyun.omsapibase.infrastructure.enums.ApplicationTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.ConfigKeyEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.ErrorCodes;
import com.differ.jackyun.omsapibase.infrastructure.enums.LogTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.shopconfig.ShopLogOperateTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.exception.AppException;
import com.differ.jackyun.omsapibase.infrastructure.openapi.authorization.PlatAuthorizationCustomBizData;
import com.differ.jackyun.omsapibase.infrastructure.openapi.cmsbms.AddUserShopRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.openapi.edi.CancelEdiShopAuthorizationRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.openapi.manualdownloadorder.CreateOmitOrderJobRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.core.BasePlatResponse;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.authorization.*;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.cancelauthorization.PolyCancelAuthorizationRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.cancelauthorization.PolyCancelAuthorizationResponseBizData;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.enums.Business_OrderStatusEnum;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.shopmessagesubscribe.PolyAPIBusinessShopMessageSubscribeRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.shopmessagesubscribe.PolyAPIBusinessShopMessageSubscribeResponseBizData;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.shopsubscribe.PolyAPIBusinessShopSubscribeRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.shopsubscribe.PolyAPIBusinessShopSubscribeResponseBizData;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.syncshopstatus.PolySyncShopStatusRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.syncshopstatus.PolySyncShopStatusResponseBizData;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.core.PolyAPIPlatEnum;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.core.PolyAPITypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.utils.*;
import com.differ.jackyun.omsapibase.service.core.ServiceUtils;
import com.differ.jackyun.omsapibase.service.shopconf.IShopConfService;
import com.differ.jackyun.omsapibusiness.feign.CmsBmsFeignClient;
import com.differ.jackyun.omsapibusiness.feign.EdiCommonFeignClient;
import com.differ.jackyun.omsapibusiness.feign.ErpBaseInfoFeignClient;
import com.differ.jackyun.omsapibusiness.feign.ErpFeignClient;
import com.differ.jackyun.omsapibusiness.service.authorization.ext.*;
import javafx.util.Pair;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.net.URLDecoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.differ.jackyun.omsapibusiness.service.authorization.ShopAuthorizationAdapter.SYNC_SHOP_AUTHORIZATION_STATUS_CAPTION;

/**
 * 平台授权执行业务相关基类。
 *
 * <AUTHOR>
 * @since 2019-08-13 11:55:06
 */
@Component("BasePlatAuthorizationBusinessProfile")
@Scope("prototype")
public class BasePlatAuthorizationBusinessProfile implements IPlatAuthorizationBusinessProfile {
    //region 变量

    /**
     * 店铺服务接口。
     */
    @Autowired
    private IShopConfService serviceShopConf;

    /**
     * 店铺dao接口。
     */
    @Autowired
    private ShopDBSwitchMapper shopMapper;

    /**
     * 店铺基础信息查询Mapper
     */
    @Autowired
    private ApiShopBaseSwitchMapper apiShopBaseSwitchMapper;

    /**
     * 网店订单信息查询mapper
     */
    @Autowired
    private DaoTradeOnlineDBSwitchMapper tradeOnlineMapper;

    /**
     * ERP Feign接口。
     */
    @Autowired
    protected ErpFeignClient erpFeignClient;

    /**
     * ERP Feign接口。
     */
    @Autowired
    protected ErpBaseInfoFeignClient erpBaseInfoFeignClient;

    /**
     * cms-bms Feign接口。
     */
    @Autowired
    private CmsBmsFeignClient cmsBmsFeignClient;

    /**
     * edi-common Feign接口。
     */
    @Autowired
    private EdiCommonFeignClient deiCommonFeignClient;

    @Autowired
    private ApiLoadFeignClient apiLoadFeignClient;

    /**
     * ERP Feign接口(user-biz)。
     */
    @Autowired
    private com.differ.jackyun.omsapi.user.biz.infrastructure.client.feign.ErpFeignClient bizErpFeignClient;

    /**
     * 店铺的feign接口
     */
    @Autowired
    private ShopFeignClient shopFeignClient;

    /**
     * 店铺授权服务
     */
    @Autowired
    private IApiShopAuthService shopAuthService;

    @Autowired
    private IApiShopDataConvertService apiShopDataConvertService;

    /**
     * 店铺授权信息仓储
     */
    @Autowired
    private ApiShopAuthSwitchMapper apiShopAuthMapper;

    /**
     * 店铺日志
     */
    @Autowired
    private ConfShopLogDBMapper confShopLogDBMapper;


    //endregion

    //region IPlatAuthorizationBusinessProfile

    /**
     * 获取平台授权网址(授权型平台使用)。
     *
     * @param request 请求参数
     * @return 平台授权网址相关信息
     */
    @Override
    public BuildPlatAuthorizationUrlResponseBizData getPlatAuthorizationUrl(BuildPlatAuthorizationUrlRequestBizData request) {
        /*
         * 业务说明：
         * 1. 获取平台注册信息(appKey、appSecret、回调url等)。
         * 2. 重新组装平台注册信息(appKey、appSecret每个用户可能不同，需要参数传入，采多多、人人店微商等)。
         * 3. 获取平台授权网址。
         * 4. 返回结果处理。
         */

        this.writeBusinessLog(() -> ExtUtils.stringBuilderAppend("准备获取平台授权网址，请求参数：", JsonUtils.toJson(request)));

        // 获取平台授权网址结果对象。
        BuildPlatAuthorizationUrlResponseBizData result = new BuildPlatAuthorizationUrlResponseBizData();
        ServiceUtils.doServiceTryCatch("获取平台授权网址", ApplicationTypeEnum.BUSINESS, false, () -> {
            // 获取平台注册信息。
            PlatAuthEntity platAuthData = this.getPlatAuthInfo(request.getPlatValue(), request.getCustom(), request.getShopId());

            //region 组装获取平台授权网址业务参数

            // 组装获取平台授权网址业务参数。
            BuildeAuthorizeUrlRequestBizData bizRequestData = new BuildeAuthorizeUrlRequestBizData();
            bizRequestData.setAppKey(platAuthData.getAppKey());
            bizRequestData.setAppSecret(platAuthData.getAppSecret());
            bizRequestData.setCallbackUrl(platAuthData.getCallbackUrl());
            // 作为授权型平台授权过程的唯一标识(获取sessionKey和同步账号，吉客号-店铺编号-用户编号)。
            bizRequestData.setState(this.makeState(request.getShopId(), request.getUserId()));
            bizRequestData.setItemCode(this.makeItemCode(platAuthData));
            // 生成授权网址请求参数值特殊处理
            this.buildeAuthorizeUrlBizRequestDataSpecial(bizRequestData, request);


            //endregion

            //region 生成平台授权网址

            // 生成平台授权网址领域事件。
            BuildeAuthorizeUrlEvent event = new BuildeAuthorizeUrlEvent(DomainUtils.getMemberName(), PolyAPITypeEnum.COMMOM_BUILDEAUTHORIZEURL, request.getPlatValue(), bizRequestData);
            // 不需要token时，token必须为空字符串，不可为null。
            event.setToken("");

            this.writeBusinessLog(() -> ExtUtils.stringBuilderAppend("准备发布生成平台授权网址领域事件，领域事件报文：", JsonUtils.toJson(event)));

            // 发布领域事件。
            DomainEventServiceManager.get().publishSafe(event);
            // 平台授权地址信息。
            BasePlatResponse<BuildeAuthorizeUrlResponseBizData> response = event.getResponse();

            //endregion

            this.writeBusinessLogForce(() -> ExtUtils.stringBuilderAppend("生成平台授权地址，返回报文：", JsonUtils.toJson(response)));

            //region 生成平台授权网址结果处理

            // 判断生成平台授权网址返回结果是否为null。
            if (response == null) {
                result.setIsSuccess(false);
                result.setMsg("获取平台授权网址失败");
                return false;
            }

            // 判断生成平台授权网址是否成功。
            result.setIsSuccess(Boolean.TRUE.equals(response.getIsSuccess()));
            if (!result.getIsSuccess()) {
                result.setMsg(response.getMsg() + response.getSubMessage());
                return false;
            }

            // 判断业务参数是否为空。
            if (response.getBizData() == null) {
                result.setIsSuccess(false);
                result.setMsg("未获取到平台授权网址");
                return false;
            }

            result.setUrl(response.getBizData().getAuthorizeUrl());
            result.setState(bizRequestData.getState());

            //endregion

            this.writeBusinessLogForce(() -> ExtUtils.stringBuilderAppend("获取平台授权地址报文：", JsonUtils.toJson(result)));

            return true;
        }, (Exception ex) -> {
            result.setIsSuccess(false);
            result.setMsg("获取平台授权地址发生异常：" + ex.getMessage());

            final String message = "获取平台授权地址发生异常：" + CoreUtils.exceptionToString(ex);
            this.writeBusinessLogForce(() -> ExtUtils.stringBuilderAppend(message));
            if (ex instanceof AppException) {
                AppException.throwException(ErrorCodes.LOGICERROR, ((AppException) ex).getAttachedMessage());
            }
            AppException.throwException(ErrorCodes.LOGICERROR, message);
        }, null);

        return result;
    }

    /**
     * 执行平台授权业务实现。
     *
     * @param request 请求参数
     * @return 平台授权后相关信息
     */
    @Override
    public HandlerPlatAuthorizationResponseBizData platAuthorizationProfile(HandlerPlatAuthorizationRequestBizData request) {
        try {
            /*
             * 业务说明：
             * 1. 判断授权模式，然后使用对应授权方法进行授权操作。
             * 2. 自用型授权模式使用自用型授权，授权型和混合型授权使用授权型授权。
             */

            // 判断授权模式。
            if (PolyAPIAuthorizationEnum.CUSTOM.equals(request.getAuthorizationMode())) {
                // 自用型平台授权(直接使用账号进行验证身份信息)。
                return this.customPlatAuthorization(request);
            } else {
                // 授权型平台授权(在第三方平台返回授权网址上使用账号登录，然后通过同步授权信息进行授权)。
                return this.platAuthorization(request);
            }
        } catch (Exception ex) {

            LogAdapter.writeSystemLog("授权异常", CoreUtils.exceptionToString(ex), LogTypeEnum.ERROR);
            HandlerPlatAuthorizationResponseBizData response = new HandlerPlatAuthorizationResponseBizData();
            response.setIsSuccess(false);
            response.setMsg(String.format("[%s]系统异常", DomainUtils.getContextID()));
            return response;
        }

    }

    /**
     * 取消授权。
     *
     * @param request 请求参数
     * @return 取消授权结果
     */
    @Override
    public CancelPlatAuthorizationResponseBizData cancelAuthorizationProfile(CancelPlatAuthorizationRequestBizData request) {
        CancelPlatAuthorizationResponseBizData bizResponseData = new CancelPlatAuthorizationResponseBizData();

        this.writeBusinessLog(() -> ExtUtils.stringBuilderAppend("准备取消店铺授权，请求参数：", JsonUtils.toJson(request)));

        // 获取店铺缓存信息。
        ShopConfigEntity shopConfig = ShopConfDataProxy.get().getData(DomainUtils.getMemberName(), request.getShopId());
        if (shopConfig == null) {
            this.writeBusinessLogForce(() -> ExtUtils.stringBuilderAppend("取消店铺授权时，店铺不存在"));

            bizResponseData.setIsSuccess(true);
            bizResponseData.setMsg("店铺不存在");
            return bizResponseData;
        }

        // 判断店铺是否已授权。
        if (StringUtils.isBlank(shopConfig.getPolyToken())) {
            this.writeBusinessLogForce(() -> ExtUtils.stringBuilderAppend("店铺polyToken为空，无需取消授权，店铺配置: ",
                    JsonUtils.toJson(shopConfig)));
            bizResponseData.setIsSuccess(true);
            bizResponseData.setMsg("店铺polyToken为空，无需取消授权");
            return bizResponseData;
        }

        //region 取消推送关系

        // 淘宝取消授权接口需要放在取消店铺授权之前，否则会提示当前店铺不可用，用户无法取消授权。
        boolean success = this.cancelOrderDataNotify(shopConfig);
        if (!success) {
            bizResponseData.setIsSuccess(false);
            bizResponseData.setMsg("取消淘宝推送数据关系失败，请稍后重试");
            return bizResponseData;
        }

        //endregion

        //region 取消店铺授权

        success = this.cancelAuthorization(shopConfig);
        if (!success) {
            bizResponseData.setIsSuccess(false);
            bizResponseData.setMsg("取消授权失败，请稍后重试");
            // 这里添加是 继续通知下erp
            noticeErpAuthInvalid(DomainUtils.getMemberName(), shopConfig.getShopId());
            return bizResponseData;
        }

        //endregion

        //region 取消店铺订阅

        if (YunConfigUtils.contains(shopConfig.getShopType().toString(), ConfigKeyEnum.BUSINESS_CANCEL_SUBSCRIBE_PLAT)) {
            success = this.cancelSubscribe(shopConfig);
            if (!success) {
                bizResponseData.setIsSuccess(false);
                bizResponseData.setMsg("取消店铺订阅失败，请稍后重试");
                return bizResponseData;
            }
        }

        //endregion

        // 是否允许清空授权信息，默认允许清除
        boolean isAllowCleanShopAuth = isAllowCleanShopAuthInfo(request, shopConfig.getMemName());

        // 判断店铺是否开启自动下载订单功能，若开启则需要移除自动下载店铺缓存配置(移除的原因是防止缓存信息过多)。
        if (shopConfig.getIsAutoDownload() && isAllowCleanShopAuth) {
            ShopConfAutoDownloadDataProxy.get().removeCache(shopConfig.getShopId());
        }

        //region 清空店铺授权信息(j_conf_shop 和 j_api_shop_config)

        // 清空店铺授权信息（来自渠道变更）
        if (request.isFromChannelChanged()) {
            this.cleanShopAuthorizationByChannelChanged(shopConfig);
        }
        // 清空店铺授权信息（来自其它途径）
        else {
            this.cleanShopAuthorization(request, shopConfig);
        }

        //endregion

        // 推单模式提示取消推送地址
        switch (shopConfig.getShopType()) {
            case BUSINESS_HPK:
            case BUSINESS_TST:
            case BUSINESS_YANXUAN:
            case BUSINESS_HAIDAI:
            case BUSINESS_GLG:
            case BUSINESS_XINGYUN:
            case BUSINESS_TJFXG:
            case TAKEAWAY_MEITUAN:
            case BUSINESS_TIANTIANGONGCHANG:
                bizResponseData.setMsg("请及时关闭推送订单功能");
                break;
            case BUSINESS_YZLS:
            case BUSINESS_YZMY:
            case BUSINESS_YOUZAN2:
            case BUSINESS_YOUZANFENXIAO:
                bizResponseData.setMsg("请及时取消店铺订阅");
                break;
            default:
                break;
        }

        // 同步ERP授权失效
        noticeErpAuthInvalid(DomainUtils.getMemberName(), shopConfig.getShopId());
        bizResponseData.setIsSuccess(true);
        return bizResponseData;
    }

    /**
     * 更新店铺状态
     *
     * @param request 请求参数
     * @return 返回结果
     */
    @Override
    public SyncShopStatusResponseBizData updateShopStatusProfile(SyncShopStatusRequestBizData request) {
        // 同步店铺授权状态到菠萝派
        return this.syncShopStatusToPoly(request);
    }

    /**
     * 获取平台注册信息
     *
     * @param platValue
     * @param customBizData
     * @return
     */
    @Override
    public PlatAuthEntity getPlatAuthInfo(PolyAPIPlatEnum platValue, PlatAuthorizationCustomBizData customBizData) {
        // shopId没用，传0
        return this.getPlatAuthInfo(platValue, customBizData, 0L);
    }

    //endregion

    //region 继承类重写方法

    //region 生成授权网址请求参数值特殊处理

    /**
     * 生成授权网址请求参数值特殊处理
     *
     * @param polyBizRequestData 菠萝派请求
     * @param orginalRequest     原始请求
     */
    protected void buildeAuthorizeUrlBizRequestDataSpecial(BuildeAuthorizeUrlRequestBizData polyBizRequestData, BuildPlatAuthorizationUrlRequestBizData orginalRequest) {

    }

    //endregion

    //region 设置itemCode

    /**
     * 设置itemCode
     *
     * @param platAuthData 平台注册信息
     * @return itemCode
     */
    protected String makeItemCode(PlatAuthEntity platAuthData) {
        return null;
    }

    //endregion

    //region 生成授权唯一标识

    /**
     * 生成授权唯一标识。
     *
     * @param shopId 店铺Id
     * @param userId 用户Id
     * @return 授权唯一标识
     */
    protected String makeState(long shopId, String userId) {
        return DomainUtils.getMemberName() + "-" + shopId + "-" + userId;
    }

    //endregion

    //region 获取平台注册信息

    /**
     * 获取平台注册信息。(授权型重写该方法)
     *
     * @param platValue     平台枚举
     * @param customBizData 授权自定义参数
     * @param shopId        店铺ID
     * @return 平台注册信息
     */
    protected PlatAuthEntity getPlatAuthInfo(PolyAPIPlatEnum platValue, PlatAuthorizationCustomBizData customBizData, Long shopId) {
        return this.getPlatAuthInfo(platValue, customBizData, OmsApiConstant.EMPTY_STR);
    }

    /**
     * 获取平台注册信息。
     *
     * @param platValue     平台枚举
     * @param customBizData 授权自定义参数
     * @param appType       平台类型
     * @return 平台注册信息
     */
    protected PlatAuthEntity getPlatAuthInfo(PolyAPIPlatEnum platValue, PlatAuthorizationCustomBizData customBizData, String appType) {
        //region 获取平台注册信息

        // 获取平台注册信息。
        PlatAuthEntity platAuthData = PlatAuthDataProxy.get().getData(platValue, appType, DomainUtils.getPlatCodeByMem());
        // 判断平台注册信息是否为null。
        if (platAuthData == null) {
            this.writeBusinessLogForce(() -> ExtUtils.stringBuilderAppend("未找到平台注册信息"));
            throw new AppException(ErrorCodes.AUTHORIZATIONERROR, String.format("未找到%s平台注册信息", platValue.getPlatCName()));
        }

        //endregion

        //region 重组平台注册信息

        // 平台注册信息中没有appKey时，使用请求参数中的appKey。
        if (ExtUtils.isNullOrEmpty(platAuthData.getAppKey())) {
            Optional<PlatAuthorizationCustomBizData.Item> appKeyOrNull = customBizData.getItems().stream().filter(item -> "appKey".equalsIgnoreCase(item.geteKey())).findFirst();
            appKeyOrNull.ifPresent(appKey -> platAuthData.setAppKey(appKey.getValue()));
        }

        // 平台注册信息中没有appSecret时，使用请求参数中的appSecret。
        if (ExtUtils.isNullOrEmpty(platAuthData.getAppSecret())) {
            Optional<PlatAuthorizationCustomBizData.Item> appSecretOrNull = customBizData.getItems().stream().filter(item -> "appSecret".equalsIgnoreCase(item.geteKey())).findFirst();
            appSecretOrNull.ifPresent(appSecret -> platAuthData.setAppSecret(appSecret.getValue()));
        }

        //endregion

        return platAuthData;
    }

    //endregion

    //region 授权型平台授权相关

    /**
     * 平台授权(授权型平台)。
     *
     * @param bizRequestData 授权参数
     * @return 授权结果
     */
    protected HandlerPlatAuthorizationResponseBizData platAuthorization(HandlerPlatAuthorizationRequestBizData bizRequestData) {
        /*
         * 业务说明：
         * 1. 获取平台注册信息。
         * 2. 重新组装平台注册信息(appKey、appSecret每个用户可能不同，需要参数传入，采多多、人人店微商等)。
         * 3. 获取SessionKey信息。
         * 4. 同步账号到菠萝派。
         */
        // 授权结果。
        HandlerPlatAuthorizationResponseBizData bizResponseData = new HandlerPlatAuthorizationResponseBizData();

        // 获取平台注册信息。
        PlatAuthEntity platAuthData = this.getPlatAuthInfo(bizRequestData.getPlatValue(), bizRequestData.getCustom(), bizRequestData.getShopId());

        // 判断是否订购吉客云应用
        try {
            String decodedUrl = URLDecoder.decode(ExtUtils.getStringOrEmpty(bizRequestData.getCallbackUrl()), "UTF-8");
            if (StringUtils.isNotEmpty(decodedUrl) && decodedUrl.contains("need purchase")) {
                bizResponseData.setIsSuccess(false);
                StringBuilder sb = new StringBuilder();
                sb.append("该店铺账号未订购服务，请前往服务市场订购！");
                if (StringUtils.isNotEmpty(platAuthData.getPurchaseUrl())) {
                    sb.append(String.format("订购链接：<a target=\"_blank\" href=\"%s\">%s</a>", platAuthData.getPurchaseUrl(), platAuthData.getPurchaseUrl()));
                }
                bizResponseData.setMsg(sb.toString());
                return bizResponseData;
            }
        } catch (Exception e) {
            LogAdapter.writeSystemLog("授权解码回调地址异常", e);
        }

        // 重新组装回调地址。
        bizRequestData.setCallbackUrl(this.resetCallbackUrl(bizRequestData.getCallbackUrl(), platAuthData.getCallbackUrl()));
        //region 获取SessionKey

        GetAuthorizeSessionKeyRequestBizData getSessionKeyRequestData = new GetAuthorizeSessionKeyRequestBizData();
        getSessionKeyRequestData.setAppKey(platAuthData.getAppKey());
        getSessionKeyRequestData.setAppSecret(platAuthData.getAppSecret());
        getSessionKeyRequestData.setCallbackUrl(bizRequestData.getCallbackUrl());

        // 其他请求参数扩展。
        this.getSessionKeyParamExtend(getSessionKeyRequestData, bizRequestData);

        // 获取SessionKey
        BasePlatResponse<GetAuthorizeSessionKeyResponseBizData> response = getAuthorizeSessionKey(bizRequestData, getSessionKeyRequestData);

        // 判断结果是否为null。
        if (response == null) {
            this.writeBusinessLogForce(() -> ExtUtils.stringBuilderAppend("获取SessionKey结果为null"));
            // 不使用AppException.throwException是因为sonarLink检测不出来response为null时直接抛出。
            throw new AppException(ErrorCodes.LOGICERROR, "平台授权获取SessionKey失败");
        }

        // 判断获取SessionKey是否成功。
        bizResponseData.setIsSuccess(Boolean.TRUE.equals(response.getIsSuccess()));
        if (!bizResponseData.getIsSuccess()) {
            bizResponseData.setMsg(response.getSubMessage());
            bizResponseData.setSubCode(response.getSubCode());
            bizResponseData.setPlatValue(bizRequestData.getPlatValue());
            this.platAuthorizationFailedHandler(bizResponseData);
            return bizResponseData;
        }

        // 判断获取SessionKey结果是否为null。
        if (response.getBizData() == null || ExtUtils.isNullOrEmpty(response.getBizData().getSessionKey())) {
            bizResponseData.setMsg(response.getMsg() + response.getSubMessage());
            bizResponseData.setSubCode(response.getSubCode());
            return bizResponseData;
        }

        if (YunConfigUtils.contains(platAuthData.getPlatValue().toString(), ConfigKeyEnum.NOT_ALLOW_AUTH_WITH_SUB_ACCOUNT_PLAT) && !bizRequestData.isSubAccount() && response.getBizData().getIsSubAccount()) {
            bizResponseData.setIsSuccess(false);
            bizResponseData.setMsg("授权失败，您当前使用的授权账号为店铺子账号，请使用店铺主账号授权");
            return bizResponseData;
        }
        // 若不是子账号授权，提示使用子账号授权。
        if (bizRequestData.isSubAccount() && !response.getBizData().getIsSubAccount()) {
            bizResponseData.setIsSuccess(false);
            bizResponseData.setMsg("请使用子账号授权");
            return bizResponseData;
        }

        // 对获取到的GetAuthorizeSessionKeyResponseBizData的参数的校验
        if (!this.checkResponseParam(bizRequestData, response, bizResponseData)) {
            return bizResponseData;
        }

        //endregion

        //region 同步账号参数

        bizResponseData.getAuthorization().setAppKey(platAuthData.getAppKey());
        bizResponseData.getAuthorization().setAppSecret(platAuthData.getAppSecret());
        bizResponseData.getAuthorization().setAuthorizeTime(LocalDateTime.now());
        bizResponseData.getAuthorization().setSessionKey(response.getBizData().getSessionKey());
        bizResponseData.getAuthorization().setSessionKeyExpireTime(DateTimeUtils.stringToTime2(response.getBizData().getSessionKeyExpireTime()));
        bizResponseData.getAuthorization().setSubscriptionExpireTime(DateTimeUtils.stringToTime2(response.getBizData().getSubscriptionExpireTime()));
        bizResponseData.getAuthorization().setRefreshToken(response.getBizData().getRefreshToken());
        bizResponseData.setNick(response.getBizData().getNickName());
        bizResponseData.setVenderId(response.getBizData().getVenderId());
        bizResponseData.setSubPlat(response.getBizData().getSubPlat());
        // 特殊授权参数（微信小商店）
        bizResponseData.setPrivateData(response.getBizData().getPrivateData());
        GetAuthorizeSessionKeyResponseBizData.AttrJson attrJson = JsonUtils.deJson(response.getBizData().getAttrJson(), GetAuthorizeSessionKeyResponseBizData.AttrJson.class);
        if (attrJson != null) {
            bizResponseData.setVenderCode(attrJson.getVenderCode());
        }
        // 刷新授权token(refresh_token)过期时间(店铺授权过期时间以这个为准)。
        LocalDateTime refreshTokenExpireTime = DateTimeUtils.stringToTime2(response.getBizData().getRefreshTokenExpireTime());
        // refresh_token过期时间与当前比小于3天，则认为refresh_token过期时间不准，将其至为null，前端显示已授权。
        refreshTokenExpireTime = refreshTokenExpireTime != null && CoreUtils.diffTime(LocalDateTime.now(), refreshTokenExpireTime) > 3
                ? refreshTokenExpireTime : null;
        bizResponseData.getAuthorization().setRefreshTokenExpireTime(refreshTokenExpireTime);

        bizResponseData.setMemberName(DomainUtils.getMemberName());
        bizResponseData.setShopName(bizRequestData.getShopName());
        bizResponseData.setShopId(bizRequestData.getShopId());
        bizResponseData.setPlatValue(bizRequestData.getPlatValue());
        bizResponseData.setToken(bizRequestData.getToken());
        // 将自定义参数赋值到参数中，在同步账号时，可以对特殊平台需要的自定义参数进行处理。
        bizResponseData.setCustom(bizRequestData.getCustom());
        // 子账号授权标识。
        bizResponseData.setSubAccount(bizRequestData.isSubAccount());
        //授权用特殊参数，返回给online处理
        bizResponseData.setSpecParam(bizRequestData.getSpecParam());
        // 记录卖家昵称、平台店铺id、订购到期时间到店铺日志

        StringBuilder stringBuilder = new StringBuilder();

        if (StringUtils.isNotBlank(response.getBizData().getVenderId())) {
            stringBuilder.append(String.format("，店铺id：%s", response.getBizData().getVenderId()));
        }
        if (StringUtils.isNotBlank(response.getBizData().getNickName())) {
            stringBuilder.append(String.format("，店铺昵称：%s", response.getBizData().getNickName()));
        }
        if (StringUtils.isNotEmpty(response.getBizData().getSubscriptionExpireTime())) {
            stringBuilder.append(String.format("，该店铺在平台服务市场订购的吉客云应用到期时间为：%s", response.getBizData().getSubscriptionExpireTime()));
        }
        bizResponseData.setAuthAppendLog(String.valueOf(stringBuilder));
        //endregion

        //region 重复授权校验

        // 校验是否重复授权，判断是否走新的验证方法
        if (!ConfigUtil.isConfigCompose(YunConfigUtils.getDBConfig(ConfigKeyEnum.EXCEPT_REPEAT_AUTHORIZATION_CHECK), bizResponseData.getPlatValue().toString(), bizResponseData.getMemberName())) {
            this.platAuthorizationNonRepeat(bizResponseData, PolyAPIAuthorizationEnum.AUTHORIZATION);
            if (!bizResponseData.getIsSuccess()) {
                return bizResponseData;
            }
        }


        // 授权账号变更验证
        if (!this.platAuthAccountChangeVerify(bizRequestData.getMemberName(), bizRequestData.getAuthorizationMode(), bizResponseData)) {
            return bizResponseData;
        }

        //endregion

        // 同步账号到菠萝派。
        this.syncAccountToPoly(bizResponseData, platAuthData);

        // XQ048143-授权保存应用appkey、和平台店铺ID、店铺名称开发设计
        this.saveAuthAppKey(bizResponseData, getSessionKeyRequestData);

        // 添加店铺到期信息到CMS(作为查询店铺订购到期，J系列店铺授权无法同步CMS，所以失败不进行处理)。
        CommonTask.safeExecute(() -> this.addShopAuthorizationToCMS(bizRequestData.getPlatValue(), bizResponseData.getNick()));
        return bizResponseData;
    }

    /**
     * 获取授权sessionKey
     *
     * @param bizRequestData           授权参数
     * @param getSessionKeyRequestData 获取平台SessionKey入参
     * @return SessionKey对象
     */
    protected BasePlatResponse<GetAuthorizeSessionKeyResponseBizData> getAuthorizeSessionKey(HandlerPlatAuthorizationRequestBizData bizRequestData, GetAuthorizeSessionKeyRequestBizData getSessionKeyRequestData) {
        // 创建获取SessionKey领域事件。
        GetAuthorizeSessionKeyEvent event = new GetAuthorizeSessionKeyEvent(DomainUtils.getMemberName(), PolyAPITypeEnum.COMMOM_GETAUTHORIZESESSIONKEY, bizRequestData.getPlatValue(), getSessionKeyRequestData);
        if (YunConfigUtils.contains(DomainUtils.getMemberName(), ConfigKeyEnum.GET_SESSION_KEY_SPECIAL_DEAL_WITH_TOKEN_MEMBER)) {
            bizRequestData.setToken(ExtUtils.isNullOrEmpty(bizRequestData.getToken()) ? CoreUtils.createUUIDText() : bizRequestData.getToken());
            event.setToken(bizRequestData.getToken());
        } else {
            event.setToken("");
        }

        this.writeBusinessLog(() -> ExtUtils.stringBuilderAppend("准备发布获取SessionKey领域事件，报文：", JsonUtils.toJson(event)));

        // 发布领域事件。
        DomainEventServiceManager.get().publishSafe(event);
        // 获取SessionKey结果。
        BasePlatResponse<GetAuthorizeSessionKeyResponseBizData> response = event.getResponse();

        this.writeBusinessLogForce(() -> ExtUtils.stringBuilderAppend("获取SessionKey结果，报文：", JsonUtils.toJson(response)));
        return response;
    }

    /**
     * XQ048143-授权保存应用appkey、和平台店铺ID、店铺名称开发设计
     *
     * @param bizResponseData          授权返回参数
     * @param getSessionKeyRequestData 获取sessionKey请求参数
     */
    protected void saveAuthAppKey(HandlerPlatAuthorizationResponseBizData bizResponseData, GetAuthorizeSessionKeyRequestBizData getSessionKeyRequestData) {
        if (bizResponseData == null) {
            throw new AppException(ErrorCodes.LOGICERROR, "业务对象不能为空");
        }
        List<PlatAuthorizationCustomBizData.Item> listItem = new ArrayList<>(10);
        if (bizResponseData != null && bizResponseData.getCustom() != null) {
            listItem.addAll(bizResponseData.getCustom().getItems());
        }

        if (StringUtils.isNotBlank(getSessionKeyRequestData.getAppKey())) {
            Optional<PlatAuthorizationCustomBizData.Item> appKeyOrNull = listItem.stream().filter(item -> "appKey".equalsIgnoreCase(item.geteKey())).findFirst();
            if (appKeyOrNull.isPresent()) {
                appKeyOrNull.get().setValue(getSessionKeyRequestData.getAppKey());
            } else {
                PlatAuthorizationCustomBizData.Item item = new PlatAuthorizationCustomBizData.Item();
                item.setcKey("appKey");
                item.seteKey("appKey");
                item.setValue(getSessionKeyRequestData.getAppKey());
                listItem.add(item);
            }
        }

        if (StringUtils.isNotBlank(bizResponseData.getVenderId())) {
            Optional<PlatAuthorizationCustomBizData.Item> platShopIdOrNull = listItem.stream().filter(item -> "platShopId".equalsIgnoreCase(item.geteKey())).findFirst();
            if (platShopIdOrNull.isPresent()) {
                platShopIdOrNull.get().setValue(bizResponseData.getVenderId());
            } else {
                PlatAuthorizationCustomBizData.Item item1 = new PlatAuthorizationCustomBizData.Item();
                item1.setcKey("platShopId");
                item1.seteKey("platShopId");
                item1.setValue(bizResponseData.getVenderId());
                listItem.add(item1);
            }
        }

        if (StringUtils.isNotBlank(bizResponseData.getNick())) {
            Optional<PlatAuthorizationCustomBizData.Item> platShopNameOrNull = listItem.stream().filter(item -> "platShopName".equalsIgnoreCase(item.geteKey())).findFirst();
            if (platShopNameOrNull.isPresent()) {
                platShopNameOrNull.get().setValue(bizResponseData.getNick());
            } else {
                PlatAuthorizationCustomBizData.Item item2 = new PlatAuthorizationCustomBizData.Item();
                item2.setcKey("platShopName");
                item2.seteKey("platShopName");
                item2.setValue(bizResponseData.getNick());
                listItem.add(item2);
            }
        }

        PlatAuthorizationCustomBizData customBizData = new PlatAuthorizationCustomBizData();
        customBizData.setItems(listItem);
        bizResponseData.setCustom(customBizData);
    }

    /**
     * 重新组装回调地址。
     *
     * @param callbackUrl     回调地址
     * @param platCallbackUrl 平台注册授权回调地址(http://www.jackyun.com)
     * @return 组装后的回调地址
     */
    protected String resetCallbackUrl(String callbackUrl, String platCallbackUrl) {
        // 有些平台的回调地址在?前面会多一个斜杠，导致回传给平台后报回调地址不一致错误，这里主要是去除斜杠(微盟智慧零售)。
        int index = callbackUrl.indexOf('?');
        if (index > 0) {
            return platCallbackUrl + "?" + callbackUrl.substring(index + 1);
        }

        return callbackUrl;
    }

    //endregion

    //region 自用型平台授权相关

    /**
     * 平台授权(自用型平台)。
     *
     * @param bizRequestData 请求参数
     * @return 平台授权后相关信息
     */
    protected HandlerPlatAuthorizationResponseBizData customPlatAuthorization(HandlerPlatAuthorizationRequestBizData bizRequestData) {
        /*
         * 业务说明：
         * 1. 自用型参数合法性校验。
         * 2. 将自用型参数和AppKey和AppSecret保存到返回参数中。
         * 3. 重复授权校验。
         * 4. 自用型平台授权请求参数扩展。
         * 5. 同步账号到菠萝派。
         */

        // 构造器里已经实例化授权信息和自用型授权参数，可直接使用，不会出现空指针异常。
        HandlerPlatAuthorizationResponseBizData bizResponseData = new HandlerPlatAuthorizationResponseBizData();
        bizResponseData.setPlatValue(bizRequestData.getPlatValue());
        bizResponseData.setMemberName(DomainUtils.getMemberName());
        bizResponseData.setShopName(bizRequestData.getShopName());
        bizResponseData.setShopId(bizRequestData.getShopId());
        bizResponseData.setToken(bizRequestData.getToken());

        // 参数合法性校验。
        if (bizRequestData.getCustom() == null || bizRequestData.getCustom().getItems() == null || bizRequestData.getCustom().getItems().isEmpty()) {
            this.writeBusinessLogForce(() -> ExtUtils.stringBuilderAppend("自用型授权参数为空"));

            bizResponseData.setIsSuccess(false);
            bizResponseData.setMsg("自用型授权参数为空");
            return bizResponseData;
        }

        // 自用型参数。
        bizResponseData.getCustom().setItems(bizRequestData.getCustom().getItems());

        //region 重复授权校验

        // 校验是否重复授权，判断是否走新的验证方法
        if (!ConfigUtil.isConfigCompose(YunConfigUtils.getDBConfig(ConfigKeyEnum.EXCEPT_REPEAT_AUTHORIZATION_CHECK), bizResponseData.getPlatValue().toString(), bizResponseData.getMemberName())) {
            this.platAuthorizationNonRepeat(bizResponseData, PolyAPIAuthorizationEnum.CUSTOM);
            if (!bizResponseData.getIsSuccess()) {
                return bizResponseData;
            }
        }

        //endregion

        // 自用型平台授权请求参数扩展。
        this.customPlatAuthorizationParamExtend(bizResponseData);

        // 授权账号变更验证
        if (!this.platAuthAccountChangeVerify(bizRequestData.getMemberName(), bizRequestData.getAuthorizationMode(), bizResponseData)) {
            return bizResponseData;
        }

        this.writeBusinessLogForce(() -> ExtUtils.stringBuilderAppend("自用型平台授权参数报文：", JsonUtils.toJson(bizResponseData)));

        // 同步账号到菠萝派。
        this.syncAccountToPoly(bizResponseData, null);
        // 店铺授权检查
        //this.checkAccount(bizResponseData);
        return bizResponseData;
    }

    //region 自用型平台授权固定参数赋值相关

    /**
     * 自用型平台授权请求参数扩展。
     *
     * @param bizData 请求参数
     */
    protected void customPlatAuthorizationParamExtend(HandlerPlatAuthorizationResponseBizData bizData) {
        // 平台店铺授权需要N个参数，但是M(M < N)个参数是固定的，那么可以将固定参数配置在授权固定参数中，
        // 同步账号时重新组装自定义参数，以减少客户输入参数，方便客户授权。

        // 获取自用型平台固定授权参数。
        HashMap<String, String> hashMap = this.getCustomPlatAuthorizationFixedParam();
        if (hashMap == null || hashMap.isEmpty()) {
            return;
        }

        // 获取平台注册信息。
        PlatAuthEntity platAuthData = this.getPlatAuthInfo(bizData.getPlatValue(), bizData.getCustom(), bizData.getShopId());
        // 判断平台注册信息是否为空。
        if (platAuthData == null) {
            AppException.throwException(ErrorCodes.LOGICERROR, bizData.getPlatValue().getPlatCName() + "平台注册信息为空");
        }

        // 循环扩展参数填充相应数据。
        hashMap.forEach((key, value) -> {
            Optional<PlatAuthorizationCustomBizData.Item> itemOrNull = bizData.getCustom().getItems().stream().filter(item -> key.equalsIgnoreCase(item.geteKey())).findFirst();
            if (itemOrNull.isPresent()) {
                itemOrNull.get().setValue(CoreUtils.findParam(platAuthData.getOtherauthParams(), key));
            } else {
                PlatAuthorizationCustomBizData.Item appKeyItem = new PlatAuthorizationCustomBizData.Item();
                appKeyItem.seteKey(key);
                appKeyItem.setcKey(value);
                appKeyItem.setValue(CoreUtils.findParam(platAuthData.getOtherauthParams(), key));
                bizData.getCustom().getItems().add(appKeyItem);
            }
        });
    }

    /**
     * 获取自用型授权固定参数。
     *
     * @return 自用型授权固定参数集合<eKey, cKey>
     */
    public HashMap<String, String> getCustomPlatAuthorizationFixedParam() {
        return new HashMap<>(1);
    }

    //endregion

    //endregion

    //region 获取sessionKey请求参数扩展

    /**
     * 获取SessionKey请求参数扩展。
     *
     * @param bizData 请求参数
     */
    protected void getSessionKeyParamExtend(GetAuthorizeSessionKeyRequestBizData bizData, HandlerPlatAuthorizationRequestBizData bizRequestData) {
        // 获取SessionKey方法参数扩展。
    }

    //endregion

    //region 错误信息关键字替换

    /**
     * 平台授权失败后处理(翻译平台返回错误信息)。
     *
     * @param bizData 平台授权结果
     */
    protected void platAuthorizationFailedHandler(HandlerPlatAuthorizationResponseBizData bizData) {
        // 将容易出现的异常结果翻译成文字，方便客户理解，更可以加上解决方法，减少客服咨询。
        bizData.setMsg(errorKeyWordsReplace(bizData.getPlatValue(), bizData.getSubCode(), bizData.getMsg()));
    }

    //endregion

    //region 重复授权校验相关

    /**
     * 获取相同平台已授权的店铺。
     *
     * @param bizData 授权信息
     * @return 店铺信息
     */
    protected List<ShopConfigEntity> getSamePlatAuthorizedShop(HandlerPlatAuthorizationResponseBizData bizData) {
        // 查询会员所有店铺。
        List<ShopConfigEntity> allShopConfig = ShopConfDataProxy.get().getData(DomainUtils.getMemberName());
        if (allShopConfig == null || allShopConfig.isEmpty()) {
            return new ArrayList<>();
        }

        // 过滤出相同平台且已经授权的店铺（排除当前店铺）。
        return allShopConfig.stream().filter(shopConfig -> null != shopConfig && bizData.getShopId() != shopConfig.getShopId() && bizData.getPlatValue().equals(shopConfig.getShopType()) && ShopAuthStatusEnum.AUTHORIZED.equals(shopConfig.getAuthStatus()) && !ExtUtils.isNullOrEmpty(shopConfig.getSelfUseAuth())).collect(Collectors.toList());
    }

    /**
     * 授权账号不能重复(同一个账号不能授权两个店铺)。
     *
     * @param bizData       授权信息
     * @param authorization 授权模式
     */
    protected void platAuthorizationNonRepeat(HandlerPlatAuthorizationResponseBizData bizData, PolyAPIAuthorizationEnum authorization) {
        // 默认校验通过。
        bizData.setIsSuccess(true);

        // 校验重复授权（自用型平台），因为平台账号信息会被完整保存。
        this.customPlatCheck(bizData, authorization);

        // 校验重复授权（授权型平台）
        if (ConfigUtil.isConfigCompose(YunConfigUtils.getDBConfig(ConfigKeyEnum.REPEAT_AUTHORIZATION_CHECK), bizData.getPlatValue().toString(), bizData.getMemberName())) {
            this.authorizationPlatCheck(bizData, authorization);
        }
    }

    /**
     * 校验重复授权（自用型平台）
     *
     * @param bizData
     * @param authorization
     */
    private void customPlatCheck(HandlerPlatAuthorizationResponseBizData bizData, PolyAPIAuthorizationEnum authorization) {
        // 非自用型平台不处理
        if (!PolyAPIAuthorizationEnum.CUSTOM.equals(authorization)) {
            return;
        }

        // 获取相同平台已授权的店铺。
        List<ShopConfigEntity> shopConfigs = getSamePlatAuthorizedShop(bizData);
        for (ShopConfigEntity shopConfig : shopConfigs) {
            // 反序列化自定义参数。
            PlatAuthorizationCustomBizData customBizData = JsonUtils.deJson(shopConfig.getSelfUseAuth(), PlatAuthorizationCustomBizData.class);

            int[] count = new int[]{0};
            // 自用型授权参数比对。
            bizData.getCustom().getItems().forEach(item -> {
                Optional<PlatAuthorizationCustomBizData.Item> itemOrNull = customBizData.getItems().stream().filter(bizItem ->
                        bizItem.getcKey().equalsIgnoreCase(item.getcKey()) && bizItem.geteKey().equalsIgnoreCase(item.geteKey()) && bizItem.getValue().equalsIgnoreCase(item.getValue())).findFirst();
                itemOrNull.ifPresent(m -> count[0]++);
            });

            // 授权参数和其他店铺授权参数一致，则认为授权信息一致，直接返回。
            if (bizData.getCustom().getItems().size() == count[0]) {
                bizData.setIsSuccess(false);
                bizData.setMsg(this.formatCustomPlatAuthRepeatMessage(bizData, shopConfig.getShopName()));
                return;
            }
        }
    }

    /**
     * 获取自用型平台授权重复提示信息
     *
     * @param bizData
     * @param shopName
     * @return
     */
    protected String formatCustomPlatAuthRepeatMessage(HandlerPlatAuthorizationResponseBizData bizData, String shopName) {
        String msg = "授权失败，您当前使用的授权信息与店铺【%s】重复，请确认，如需授权该店铺，需要对原店铺取消授权后再操作";
        msg = String.format(msg, shopName);

        // 记录日志
        ConfShopLogEntity logEntity = new ConfShopLogEntity();
        logEntity.setChannelId(bizData.getShopId());
        logEntity.setUserId(DomainUtils.getUserId());
        logEntity.setUserName(DomainUtils.getRealName());
        logEntity.setOperateType(ShopLogOperateTypeEnum.AUTHOR_OPERATE.getValue());
        logEntity.setLogDetail(String.format("授权失败，您当前使用的授权账号与店铺【%s】授权重复！", shopName));
        SpringResolveManager.resolve(ConfShopLogDBMapper.class).insertConfShopLogs(bizData.getMemberName(), Collections.singletonList(logEntity));
        return msg;
    }

    /**
     * 获取授权重复提示信息
     *
     * @param bizData
     * @param shopName
     * @return
     */
    protected String formatAuthRepeatMessage(HandlerPlatAuthorizationResponseBizData bizData, String shopName) {
        String msg = "授权失败，您当前使用的授权账号是【%s】，与店铺\"%s\"授权重复！<br/>" +
                "风险说明：<br/>" +
                "1.此次授权操作已导致原店铺订单下载、发货、退款检测、库存同步等功能失效 <br/>" +
                "2.您需要对原店铺\"%s\"使用账号【%s】重新授权 ";
        String account = StringUtils.isNotEmpty(bizData.getNick()) ? bizData.getNick() : bizData.getVenderId();
        msg = String.format(msg, account, shopName, shopName, account);

        // 记录日志
        ConfShopLogEntity logEntity = new ConfShopLogEntity();
        logEntity.setChannelId(bizData.getShopId());
        logEntity.setUserId(DomainUtils.getUserId());
        logEntity.setUserName(DomainUtils.getRealName());
        logEntity.setOperateType(ShopLogOperateTypeEnum.AUTHOR_OPERATE.getValue());
        logEntity.setLogDetail(String.format("授权失败，您当前使用的授权账号是【%s】，与店铺\"%s\"授权重复！", account, shopName));
        SpringResolveManager.resolve(ConfShopLogDBMapper.class).insertConfShopLogs(bizData.getMemberName(), Collections.singletonList(logEntity));
        return msg;
    }

    /**
     * 校验重复授权（授权型平台）
     *
     * @param bizData
     * @param authorization
     */
    private void authorizationPlatCheck(HandlerPlatAuthorizationResponseBizData bizData, PolyAPIAuthorizationEnum authorization) {
        // 非授权型平台不处理
        if (!PolyAPIAuthorizationEnum.AUTHORIZATION.equals(authorization)) {
            return;
        }

        // 菠萝派未返回nick、VenderId的不处理
        if (bizData == null || (StringUtils.isEmpty(bizData.getNick()) && StringUtils.isEmpty(bizData.getVenderId()))) {
            return;
        }

        // 获取当前会员所有已授权的店铺，为空不处理
        List<ApiShopBaseInfo> allValidShopBase = ApiShopCoreUtils.getAllValidShopBase(DomainUtils.getMemberName());
        if (CollectionsUtil.isBlank(allValidShopBase)) {
            return;
        }

        // 过滤出相同平台的店铺（排除当前店铺），为空不处理
        List<ApiShopBaseInfo> allShopBase = allValidShopBase.stream().filter(shop -> shop.getShopId().longValue() != (bizData.getShopId()) && shop.getShopType().equals(bizData.getPlatValue())).collect(Collectors.toList());
        if (CollectionsUtil.isBlank(allShopBase)) {
            return;
        }

        // 循环判断是否重复授权
        for (ApiShopBaseInfo shopBase : allShopBase) {
            ApiShopAuthInfo shopAuth = ApiShopCoreUtils.getShopAuth(DomainUtils.getMemberName(), shopBase.getShopId());
            if (shopAuth == null || (StringUtils.isEmpty(shopAuth.getPlatShopId()) && StringUtils.isEmpty(shopAuth.getAuthSellNick()))) {
                continue;
            }

            // 校验平台店铺id
            if (StringUtils.isNotBlank(bizData.getVenderId()) && StringUtils.isNotBlank(shopAuth.getPlatShopId()) && !bizData.getVenderId().equals(shopAuth.getPlatShopId())) {
                continue;
            }

            if (StringUtils.isNotBlank(shopAuth.getPlatShopId()) && shopAuth.getPlatShopId().equals(bizData.getVenderId())) {
                bizData.setIsSuccess(false);
                bizData.setMsg(this.formatAuthRepeatMessage(bizData, shopBase.getShopName()));
                return;
            }

            // 校验卖家昵称
            if (StringUtils.isBlank(bizData.getNick())) {
                return;
            }

            if (StringUtils.isNotBlank(bizData.getNick()) && StringUtils.isBlank(shopAuth.getAuthSellNick())) {
                continue;
            }

            if (shopAuth.getAuthSellNick().equals(bizData.getNick())) {
                bizData.setIsSuccess(false);
                bizData.setMsg(this.formatAuthRepeatMessage(bizData, shopBase.getShopName()));
                return;
            }
        }
    }

    //endregion

    //region 同步账号相关

    /**
     * 创建同步授权信息到菠萝派的业务参数(同步账号参数)。
     *
     * @param bizData      授权信息
     * @param platAuthData 平台注册信息
     * @return 同步账号参数
     */
    protected SyncAccountRequestBizData createSyncAccountBizData(HandlerPlatAuthorizationResponseBizData bizData, PlatAuthEntity platAuthData) {
        SyncAccountRequestBizData syncBizData = new SyncAccountRequestBizData();
        syncBizData.setRefreshTokenExpireTime(bizData.getAuthorization().getRefreshTokenExpireTime());
        syncBizData.setSessionKeyExpiretime(bizData.getAuthorization().getSessionKeyExpireTime());
        syncBizData.setSubscriptionExpireTime(bizData.getAuthorization().getSubscriptionExpireTime());

        syncBizData.setShowRepeatShops(Boolean.TRUE);
        syncBizData.setShopName(bizData.getShopName());
        // 当自定义参数有值时，appKey和appSecret优先取自定义参数上的。
        if (bizData.getCustom() != null && bizData.getCustom().getItems() != null && !bizData.getCustom().getItems().isEmpty()) {
            // 查找appKey。
            Optional<PlatAuthorizationCustomBizData.Item> appKeyOrNull = bizData.getCustom().getItems().stream().filter(item -> "appKey".equalsIgnoreCase(item.geteKey())).findFirst();
            appKeyOrNull.ifPresent(appKey -> syncBizData.setAppKey(appKey.getValue()));

            // 查找appSecret。
            Optional<PlatAuthorizationCustomBizData.Item> appSecretOrNull = bizData.getCustom().getItems().stream().filter(item -> "appSecret".equalsIgnoreCase(item.geteKey())).findFirst();
            appSecretOrNull.ifPresent(appSecret -> syncBizData.setAppSecret(appSecret.getValue()));

            syncBizData.setCustomSecret(bizData.getCustom().toStringItem());
        }

        // 判断授权信息不为空，则需要赋值。
        if (bizData.getAuthorization() != null) {
            // 自定义参数上没有appKey和appSecret时，直接取授权信息上的appKey和appSecret。
            syncBizData.setAppKey(ExtUtils.isNullOrEmpty(syncBizData.getAppKey()) ? bizData.getAuthorization().getAppKey() : syncBizData.getAppKey());
            syncBizData.setAppSecret(ExtUtils.isNullOrEmpty(syncBizData.getAppSecret()) ? bizData.getAuthorization().getAppSecret() : syncBizData.getAppSecret());
            syncBizData.setSessionKey(ExtUtils.isNullOrEmpty(bizData.getAuthorization().getSessionKey()) ? "" : bizData.getAuthorization().getSessionKey());
            syncBizData.setSessionKeyExpiretime(bizData.getAuthorization().getSessionKeyExpireTime() != null ? bizData.getAuthorization().getSessionKeyExpireTime() : null);
            syncBizData.setSessionKeyTimeout(bizData.getAuthorization().getSessionKeyTimeout());
            syncBizData.setRefreshTokenKey(bizData.getAuthorization().getRefreshToken());
            syncBizData.setOtherParms(platAuthData == null ? "" : platAuthData.getOtherParams());
            syncBizData.setVenderId(bizData.getVenderId());
            syncBizData.setNickName(bizData.getNick());
        }

        return syncBizData;
    }

    /**
     * 同步账号到菠萝派。
     *
     * @param bizRequestData 授权信息
     * @param platAuthData   平台注册信息
     */
    protected void syncAccountToPoly(HandlerPlatAuthorizationResponseBizData bizRequestData, PlatAuthEntity platAuthData) {
        ServiceUtils.doServiceTryCatch("同步账号到菠萝派", ApplicationTypeEnum.BUSINESS, false, () -> {

            // 创建同步账号业务参数。
            SyncAccountRequestBizData bizData = this.createSyncAccountBizData(bizRequestData, platAuthData);
            // 创建同步账号领域事件。
            SyncAccountEvent event = new SyncAccountEvent(DomainUtils.getMemberName(), PolyAPITypeEnum.COMMOM_SYNCACCOUNT, bizRequestData.getPlatValue(), bizData, bizRequestData.getShopId());
            // 判断是否存在店铺token，不存在时，则随机生成token值(店铺在菠萝派唯一标识)。
            event.setToken(ExtUtils.isNullOrEmpty(bizRequestData.getToken()) ? CoreUtils.createUUIDText() : bizRequestData.getToken());

            this.writeBusinessLog(() -> ExtUtils.stringBuilderAppend("创建同步账号领域事件，报文：", JsonUtils.toJson(event)));

            // 发布领域事件。
            DomainEventServiceManager.get().publishSafe(event);
            // 同步账号结果。
            BasePlatResponse<SyncAccountResponseBizData> response = event.getResponse();

            this.writeBusinessLogForce(() -> ExtUtils.stringBuilderAppend("同步账号结果，报文：", JsonUtils.toJson(response)));

            // 判断同步账号结果是否为null。
            if (response == null) {
                AppException.throwException(ErrorCodes.LOGICERROR, "同步账号失败");
            }

            bizRequestData.setIsSuccess(Boolean.TRUE.equals(response.getIsSuccess()));
            if (!bizRequestData.getIsSuccess()) {
                bizRequestData.setSubCode(response.getSubCode());
                bizRequestData.setMsg(response.getMsg() + response.getSubMessage());
                // 错误关键字替换
                platAuthorizationFailedHandler(bizRequestData);
                return false;
            }

            if (response.getBizData() == null || ExtUtils.isNullOrEmpty(response.getBizData().getToken())) {
                bizRequestData.setSubCode(response.getSubCode());
                bizRequestData.setMsg("同步账号返回结果为空");
                return false;
            }
            // 将店铺token填充到授权信息中。
            bizRequestData.setToken(response.getBizData().getToken());
            // 店铺授权检查
            this.checkAccount(bizRequestData);

            // 根据聚合返回信息判断店铺授权是否冲突
            if (YunConfigUtils.contains(DomainUtils.getMemberName(), ConfigKeyEnum.BUSINESS_AUTHORIZATION_CONFLICT_NOTICE) && !this.isAllowShopAuthShare(event.getUserName(), bizRequestData.getPlatValue())) {
                List<String> conflictMsgs = this.platAuthConflictNotice(event.getUserName(),
                        bizRequestData.getShopId(), response);
                if (CollectionsUtil.isNotBlank(conflictMsgs)) {
                    bizRequestData.setAuthConflictTips(conflictMsgs);
                }
            }

            // 授权共享
            if (this.isAllowShopAuthShare(event.getUserName(), bizRequestData.getPlatValue())) {
                List<String> tipMsgList = shopAuthShare(event.getUserName(), bizRequestData.getShopId(), response);
                if (CollectionsUtil.isNotBlank(tipMsgList)) {
                    tipMsgList.add(0, "<span style=\"padding-left:39%;color:green;font-size:24px;\">授权成功</span>");
                    bizRequestData.setAuthConflictTips(tipMsgList);
                }
            }

            // 店铺订阅。
            this.shopSubscribe(bizRequestData);

            // 店铺消息订阅
            this.shopMessageSubscribe(bizRequestData);

            if (Boolean.FALSE.equals(bizRequestData.getIsSuccess())) {
                // 保存店铺token
                this.saveShopToken(bizRequestData);
                return false;
            }
            // 异步发起补单
            this.omitOrder(bizRequestData);
            return true;
        }, (Exception ex) -> {
            this.writeBusinessLogForce(() -> ExtUtils.stringBuilderAppend("同步账号到菠萝派发生异常：", CoreUtils.exceptionToString(ex)));
            AppException.throwException(ErrorCodes.LOGICERROR, "同步账号到菠萝派发生异常：" + CoreUtils.exceptionToString(ex));
        }, null);
    }

    /**
     * 店铺消息订阅
     *
     * @param bizData
     */
    private void shopMessageSubscribe(HandlerPlatAuthorizationResponseBizData bizData) {
        ServiceUtils.doServiceTryCatch("授权-店铺消息订阅", ApplicationTypeEnum.BUSINESS, false, () -> {
            // 店铺消息订阅参数。
            String topics = this.makeMessageSubscribeTopic();
            // 订阅消息参数为空，说明无需订阅。
            if (StringUtils.isBlank(topics)) {
                return false;
            }
            PolyAPIBusinessShopMessageSubscribeRequestBizData requestBizData = new PolyAPIBusinessShopMessageSubscribeRequestBizData();
            requestBizData.setTopics(topics);
            requestBizData.setToken(bizData.getToken());

            // 店铺消息订阅
            ShopMessageSubscribeApiCall apiCall = SpringResolveManager.resolve(ShopMessageSubscribeApiCall.class);
            ApiCallResponse<PolyAPIBusinessShopMessageSubscribeResponseBizData> response = apiCall.call(bizData.getMemberName(), bizData.getShopId(), requestBizData);

            if (response == null) {
                bizData.setIsSuccess(false);
                bizData.setMsg("店铺消息订阅失败");
                LogUtils.writeSuperWarning(ApplicationTypeEnum.BUSINESS, String.format("[%s][%d]店铺消息订阅菠萝派返回null", DomainUtils.getMemberName(), bizData.getShopId()));
                return false;
            }


            if (!Boolean.TRUE.equals(response.getIsSuccess())) {
                bizData.setIsSuccess(false);
                bizData.setMsg("店铺消息订阅失败-" + response.getSubMessage());
                LogUtils.writeSuperWarning(ApplicationTypeEnum.BUSINESS,
                        String.format("[%s][%d]店铺消息订阅失败：%s", DomainUtils.getMemberName(), bizData.getShopId(), JsonUtils.toJson(response)));
                return false;
            }

            return true;
        }, (Exception ex) -> {
            bizData.setIsSuccess(false);
            bizData.setMsg("店铺消息订阅异常-" + ex.getMessage());
            LogUtils.writeSuperWarning(ApplicationTypeEnum.BUSINESS,
                    String.format("[%s][%d]店铺消息订阅发生异常：%s", DomainUtils.getMemberName(), bizData.getShopId(), CoreUtils.exceptionToString(ex)));
        }, null);
    }

    /**
     * 店铺授权检查
     *
     * @param bizResponseData
     */
    protected void checkAccount(HandlerPlatAuthorizationResponseBizData bizResponseData) {
        // 没有配置的平台直接返回
        if (!YunConfigUtils.contains(bizResponseData.getPlatValue().toString(), ConfigKeyEnum.BUSINESS_AUTHORIZATION_CHECK_ACCOUNT)) {
            return;
        }
        ServiceUtils.doServiceTryCatch("店铺授权检查", ApplicationTypeEnum.BUSINESS, false, () -> {
            CheckAccountEvent event = new CheckAccountEvent(bizResponseData.getMemberName(), PolyAPITypeEnum.COMMON_CHECKACCOUNT, bizResponseData.getPlatValue(), new CheckAccountRequestBizData(), bizResponseData.getShopId());
            event.setToken(bizResponseData.getToken());
            // 发布领域事件
            DomainEventServiceManager.get().publishSafe(event);
            // 店铺授权检查结果
            BasePlatResponse<CheckAccountResponseBizData> response = event.getResponse();

            if (Boolean.FALSE.equals(response.getIsSuccess())) {
                bizResponseData.setIsSuccess(false);
                bizResponseData.setSubCode(response.getSubCode());
                bizResponseData.setMsg("授权失败，可能原因：<br>\n" +
                        "（1）、授权参数不对，请根据授权指引重新获取授权参数<br>\n" +
                        "（2）、授权参数位置填反，请检查");
                return false;
            }
            return true;
        }, (Exception ex) -> {
            this.writeBusinessLogForce(() -> ExtUtils.stringBuilderAppend("店铺授权检查：", CoreUtils.exceptionToString(ex)));
        }, null);
    }

    /**
     * 保存店铺token
     * <remark>
     * 这个方法提供给同步账号后但是需要返回授权失败给前端，但是店铺token需要保存下来的地方调用
     * </remark>
     *
     * @param bizResponseData 聚合返回结果
     */
    protected void saveShopToken(HandlerPlatAuthorizationResponseBizData bizResponseData) {

        // 会员名
        String memberName = DomainUtils.getMemberName();

        // 店铺 ID
        long shopId = bizResponseData.getShopId();

        // Token 为空不处理
        String token = bizResponseData.getToken();
        if (StringUtil.isEmpty(token)) {
            return;
        }

        // 修改店铺基础信息和授权信息
        ShopConfigCommonResult modifyRet = ShopBaseModifyEnhance.build().modifyBaseAndAuth(memberName, shopId, new AbstractShopModify<ApiShopBaseComposite>() {
            @Override
            public ShopModifyResult modify(Long shopId, ApiShopBaseComposite source, ApiShopBaseComposite target) {

                // 店铺不存在或已有 Token 则不处理
                if (source.getShopBaseInfo() == null || StringUtil.isNotEmpty(source.getShopBaseInfo().getPolyToken())) {
                    return ShopModifyResult.notModify();
                }

                // 店铺不存在或已有 Token 则不处理
                if (source.getShopAuthInfo() == null || StringUtil.isNotEmpty(source.getShopAuthInfo().getPolyToken())) {
                    return ShopModifyResult.notModify();
                }

                // 更新 Token
                target.getShopBaseInfo().setPolyToken(token);
                target.getShopAuthInfo().setPolyToken(token);
                return ShopModifyResult.createSuccess();
            }
        });
    }

    //endregion

    //region 取消授权相关

    /**
     * 取消数据分配。
     *
     * @param shopConfig 店铺配置
     * @return 取消数据分配结果
     */
    protected boolean cancelOrderDataNotify(ShopConfigEntity shopConfig) {
        return true;
    }

    /**
     * 取消店铺订阅。
     *
     * @param shopConfig 店铺配置
     * @return 取消店铺订阅结果
     */
    protected boolean cancelSubscribe(ShopConfigEntity shopConfig) {
        try {
            ShopSubscribeCancelRequestBizData requestBizData = new ShopSubscribeCancelRequestBizData();
            requestBizData.setCancelPolyapi(false);

            LogAdapter.writeBusinessLog("取消订阅", () -> ExtUtils.stringBuilderAppend("取消订阅请求报文：", JsonUtils.toJson(requestBizData)));
            ApiCallResponse<ShopSubscribeCancelResponseBizData> response =
                    SpringResolveManager.resolve(ShopSubscribeCancelApiCall.class).call(shopConfig.getMemName(), shopConfig.getShopId(), requestBizData);
            LogAdapter.writeBusinessLog("取消订阅", () -> ExtUtils.stringBuilderAppend("取消订阅响应报文：", JsonUtils.toJson(response)));

            // 请求失败，记录日志
            if (null == response || !response.getIsSuccess()) {
                return false;
            }
        } catch (Exception e) {
            LogAdapter.writeSystemLog("取消订阅", e);
            return false;
        }
        return true;
    }

    /**
     * 清空店铺授权信息。
     *
     * @param shopConfig 店铺配置
     */
    protected void cleanShopAuthorization(CancelPlatAuthorizationRequestBizData request, ShopConfigEntity shopConfig) {
        // 店铺更新cmd对象。
        AddOrUpdateShopConfCommand shopConfigCommand = new AddOrUpdateShopConfCommand();
        shopConfigCommand.setMemName(shopConfig.getMemName());
        shopConfigCommand.setShopName(shopConfig.getShopName());
        shopConfigCommand.setShopId(shopConfig.getShopId());
        shopConfigCommand.setShopType(shopConfig.getShopType());

        // 授权状态改为未授权。
        shopConfigCommand.setAuthStatus(ShopAuthStatusEnum.NOT_AUTHORIZED);

        // 是否允许清空授权信息，默认允许清除
        boolean isAllowCleanShopAuth = isAllowCleanShopAuthInfo(request, shopConfig.getMemName());

        // 清空店铺授权信息
        if (isAllowCleanShopAuth) {
            shopConfigCommand.setAuthRemainTime(null);

            // 清空授权自用型参数
            shopConfigCommand.setSelfUseAuth(OmsApiConstant.EMPTY_STR);
            shopConfigCommand.setAuthSessionkey(OmsApiConstant.EMPTY_STR);

            // 清空店铺授权后，需要关闭自动下载功能。
            shopConfigCommand.setIsAutoDownload(false);
        } else {
            shopConfigCommand.setAuthRemainTime(shopConfig.getAuthRemainTime());
            shopConfigCommand.setSelfUseAuth(shopConfig.getSelfUseAuth());
            shopConfigCommand.setAuthSessionkey(shopConfig.getAuthSessionkey());
            shopConfigCommand.setIsAutoDownload(shopConfig.getIsAutoDownload());
        }

        // 清空店铺授权对象。
        ConfShopEntity confShop = new ConfShopEntity();
        confShop.setChannelId(shopConfig.getShopId());

        // 授权状态改为未授权。
        confShop.setAuthStatus((int) ShopAuthStatusEnum.NOT_AUTHORIZED.getValue());

        // 清空授权信息。
        if (isAllowCleanShopAuth) {
            confShop.setSelfUseAuth(OmsApiConstant.EMPTY_STR);
            confShop.setPlatToken(OmsApiConstant.EMPTY_STR);
            shopConfigCommand.setAuthSellNickname(OmsApiConstant.EMPTY_STR);
            confShop.setPlatShopName(OmsApiConstant.EMPTY_STR);
        } else {
            confShop.setSelfUseAuth(shopConfig.getSelfUseAuth());
            confShop.setPlatToken(shopConfig.getAuthSessionkey());
            shopConfigCommand.setAuthSellNickname(shopConfig.getAuthSellNickname());
            confShop.setPlatShopName(shopConfig.getAuthSellNickname());
        }

        Long tradeId = OmsApiConstant.ZERO_LONG;
        // 配置了的保留自用型参数
        if (StringUtils.isNotBlank(shopConfigCommand.getSelfUseAuth()) && ConfigUtil.isConfigCompose(YunConfigUtils.getDBConfig(ConfigKeyEnum.BUSINESS_AUTHORIZATION_CANCEL_RESERVE_SELF_USE_AUTH), shopConfig.getMemName(), shopConfig.getShopType().toString())) {
            tradeId = this.tradeOnlineMapper.queryOneTradeOnline(shopConfig.getMemName(), shopConfig.getShopId());
            if (tradeId != null && tradeId > OmsApiConstant.ZERO_LONG) {
                confShop.setSelfUseAuth(shopConfig.getSelfUseAuth());
                shopConfigCommand.setSelfUseAuth(shopConfig.getSelfUseAuth());
            }
        }
        // 取消授权需要保留卖家账号的平台
        if (StringUtils.isNotBlank(shopConfigCommand.getAuthSellNickname()) && YunConfigUtils.isNeedReserveSellerNick(shopConfig.getShopType())) {
            if (OmsApiConstant.ZERO_LONG.equals(tradeId)) {
                tradeId = this.tradeOnlineMapper.queryOneTradeOnline(shopConfig.getMemName(), shopConfig.getShopId());
            }

            if (tradeId != null && tradeId > OmsApiConstant.ZERO_LONG) {
                shopConfigCommand.setAuthSellNickname(shopConfig.getAuthSellNickname());
                confShop.setPlatShopName(shopConfig.getAuthSellNickname());
            }
        }

        try {
            // 更新或插入。
            this.serviceShopConf.addOrderupdateShopConf(shopConfigCommand);
            // 清空授权信息(j_conf_shop)。
            this.shopMapper.cleanAuthorizationInfoByChannelId(shopConfig.getMemName(), confShop);
            // 清空ERP渠道授权信息。
            this.erpFeignClient.erpAuthorizationUpdate(shopConfig.getMemName(), shopConfig.getShopId(), OmsApiConstant.EMPTY_STR, OmsApiConstant.EMPTY_STR, OmsApiConstant.EMPTY_STR, OmsApiConstant.EMPTY_STR);
            // 取消EDI店铺授权。
            this.cancelEdiShopAuthorization(shopConfig.getShopId());
            // 转换店铺数据
            this.apiShopDataConvertService.convertShopDataByShop(shopConfig.getMemName(), shopConfig.getShopId());
        } catch (Exception ex) {
            this.writeBusinessLogForce(() -> ExtUtils.stringBuilderAppend("清空API店铺授权出现异常，异常信息: ", CoreUtils.exceptionToString(ex)));
            LogUtils.write(LogTypeEnum.ERROR, "清空API店铺授权出现异常，异常信息: " + ex.getMessage());
        }
    }

    /**
     * 渠道变更后清空授权信息
     * <p>
     * 1、兼容做法，为避免原方法 cleanShopAuthorization 更新店铺相关信息
     * 2、普通平台仅保留请求 ERP 和 EDI 授权信息接口请求
     * 3、淘宝平台额外删除 RDS 关系
     * 4、上述操作约定为取消授权后置操作，与 API 店铺无关，授权重构时整合上述操作
     *
     * @param shopConfig 店铺配置
     */
    protected void cleanShopAuthorizationByChannelChanged(ShopConfigEntity shopConfig) {
        try {

            // 清空 ERP 渠道授权信息
            this.erpFeignClient.erpAuthorizationUpdate(shopConfig.getMemName(), shopConfig.getShopId(), OmsApiConstant.EMPTY_STR, OmsApiConstant.EMPTY_STR, OmsApiConstant.EMPTY_STR, OmsApiConstant.EMPTY_STR);

            // 记录日志
            ShopConfigStatisticsUtils.logTraceChannelChanged(shopConfig.getMemName(), () ->
                    ExtUtils.stringBuilderAppend(String.format("取消授权后置操作（清空 ERP 渠道授权信息），会员名：%s，店铺Id：%s",
                            shopConfig.getMemName(),
                            shopConfig.getShopId())
                    ));

            // 取消 EDI 店铺授权
            this.cancelEdiShopAuthorization(shopConfig.getShopId());

            // 记录日志
            ShopConfigStatisticsUtils.logTraceChannelChanged(shopConfig.getMemName(), () ->
                    ExtUtils.stringBuilderAppend(String.format("取消授权后置操作（取消 EDI 店铺授权），会员名：%s，店铺Id：%s",
                            shopConfig.getMemName(),
                            shopConfig.getShopId())
                    ));
        } catch (Exception ex) {
            this.writeBusinessLogForce(() -> ExtUtils.stringBuilderAppend("渠道变更取消授权后置动作执行失败，异常信息: ", CoreUtils.exceptionToString(ex)));
            LogUtils.write(LogTypeEnum.ERROR, "渠道变更取消授权后置动作执行失败，异常信息: " + ex.getMessage());
        }
    }

    //endregion

    //region 店铺订阅相关

    /**
     * 店铺订阅消息。
     *
     * @param bizData 授权信息
     */
    protected void shopSubscribe(HandlerPlatAuthorizationResponseBizData bizData) {
        ServiceUtils.doServiceTryCatch("授权-店铺订阅消息", ApplicationTypeEnum.BUSINESS, false, () -> {
            //region 店铺订阅消息领域事件

            // 店铺订阅消息参数。
            PolyAPIBusinessShopSubscribeRequestBizData subscribeBizData = this.makeSubscribeRequestBizData(bizData);
            // 订阅消息参数为空，说明无需订阅。
            if (subscribeBizData == null) {
                return false;
            }

            //停顿1.5s,以免菠萝派订阅还未完成
            CoreUtils.safeThreadSleep(1500);

            // 店铺订阅消息领域事件。
            PolyAPIBusinessShopSubscribeEvent event = new PolyAPIBusinessShopSubscribeEvent(DomainUtils.getMemberName(), bizData.getPlatValue(), subscribeBizData, bizData.getShopId());
            event.setToken(bizData.getToken());

            //endregion

            // 发布领域事件。
            DomainEventServiceManager.get().publishSafe(event);

            BasePlatResponse<PolyAPIBusinessShopSubscribeResponseBizData> response = event.getResponse();
            if (response == null) {
                bizData.setIsSuccess(false);
                bizData.setMsg("店铺订阅消息失败");
                LogUtils.writeSuperWarning(ApplicationTypeEnum.BUSINESS, String.format("[%s][%d]店铺订阅消息菠萝派返回null", DomainUtils.getMemberName(), bizData.getShopId()));
                return false;
            }

            if (!Boolean.TRUE.equals(response.getIsSuccess())) {
                bizData.setIsSuccess(false);
                bizData.setMsg("店铺订阅消息失败-" + response.getSubMessage());
                LogUtils.writeSuperWarning(ApplicationTypeEnum.BUSINESS,
                        String.format("[%s][%d]店铺订阅消息失败：%s", DomainUtils.getMemberName(), bizData.getShopId(), JsonUtils.toJson(response)));
                return false;
            }

            return true;
        }, (Exception ex) -> {
            bizData.setIsSuccess(false);
            bizData.setMsg("店铺订阅消息异常-" + ex.getMessage());
            LogUtils.writeSuperWarning(ApplicationTypeEnum.BUSINESS,
                    String.format("[%s][%d]店铺订阅发生异常：%s", DomainUtils.getMemberName(), bizData.getShopId(), CoreUtils.exceptionToString(ex)));
        }, null);
    }

    /**
     * 生成店铺订阅消息请求参数
     *
     * @param bizData 授权参数
     * @return 店铺订阅消息请求参数
     */
    protected PolyAPIBusinessShopSubscribeRequestBizData makeSubscribeRequestBizData(HandlerPlatAuthorizationResponseBizData bizData) {
        return null;
    }


    /**
     * 生成店铺消息订阅请求参数
     *
     * @param
     * @return
     */
    protected String makeMessageSubscribeTopic() {
        return null;
    }

    /**
     * 获取店铺订阅消息参数。
     *
     * @param configKey 配制键
     * @return 店铺订阅消息参数
     */
    protected Dictionary<String, String> getShopSubscribeMessage(ConfigKeyEnum configKey) {
        Dictionary<String, String> result = new Hashtable<>();

        // 店铺订阅消息类别及消息回传地址。
        final String configKeyValue = YunConfigUtils.getDBConfig(configKey);
        if (ExtUtils.isNullOrEmpty(configKeyValue)) {
            return result;
        }

        // 获取每个平台配置的店铺订阅消息类别及消息回传地址。
        String[] messageTypeAndUrl = configKeyValue.split("\\$\\*\\$");
        for (String item : messageTypeAndUrl) {
            String[] urls = item.split("#");
            if (urls.length == 2) {
                result.put(urls[0], urls[1]);
            }
        }

        return result;
    }

    //endregion

    //region 调用菠萝派sessionKey接口后，返回的数据校验方法

    /**
     * 返回的数据校验
     *
     * @param bizRequestData  url请求入参
     * @param response        获取sessionKey返回的信息
     * @param bizResponseData 最终返回的信息
     * @return 校验结果（true:通过，false：不通过）
     */
    protected boolean checkResponseParam(HandlerPlatAuthorizationRequestBizData bizRequestData, BasePlatResponse<GetAuthorizeSessionKeyResponseBizData> response, HandlerPlatAuthorizationResponseBizData bizResponseData) {
        return true;
    }
    //endregion

    //endregion

    //region 继承类使用方法

    /**
     * 获取ERP渠道信息(网店)。
     *
     * @param channelId 渠道ID
     * @return 渠道信息
     */
    protected BaseShopInfo getErpChannelInfo(Long channelId) {
        // 获取ERP渠道信息。
        JackYunResponse<?> response = this.erpBaseInfoFeignClient.erpGetSalechannelInfo(DomainUtils.getMemberName(), channelId);
        if (response == null || !response.isSuccess() || response.getResult() == null) {
            this.writeBusinessLogForce(() -> ExtUtils.stringBuilderAppend("查询渠道信息失败，请稍后重试"));
            return null;
        }

        BaseShopInfo salesChannel = JsonUtils.deJson(JsonUtils.toJson(response.getData()), BaseShopInfo.class);
        // 查看店铺是否停用或者删除。
        if (salesChannel == null || YesOrNo.YES.getCode().equals(salesChannel.getIsDelete()) || YesOrNo.YES.getCode().equals(salesChannel.getIsBlockup())) {
            this.writeBusinessLogForce(() -> ExtUtils.stringBuilderAppend("店铺已删除或停用，请确认"));
            return null;
        }

        return salesChannel;
    }

    /**
     * 写入业务日志。
     *
     * @param funCreateLogContent 生成日志内容的方法
     */
    protected void writeBusinessLogForce(Supplier<StringBuilder> funCreateLogContent) {
        DomainUtils.writeBusinessLogForce(ApplicationTypeEnum.BUSINESS, DomainUtils.getContextID(), funCreateLogContent);
    }

    /**
     * 写入业务日志。
     *
     * @param funCreateLogContent 生成日志内容的方法
     */
    protected void writeBusinessLog(Supplier<StringBuilder> funCreateLogContent) {
        DomainUtils.writeBusinessLog(ApplicationTypeEnum.BUSINESS, DomainUtils.getContextID(), funCreateLogContent, () -> false);
    }

    /**
     * 发起补单任务
     *
     * @param request 授权请求信息
     */
    protected void omitOrder(HandlerPlatAuthorizationResponseBizData request) {
        try {
            // 授权后店铺配置进行补单
            ShopConfigEntity shopConfig = ShopConfDataProxy.get().getData(request.getMemberName(), request.getShopId());
            if (ConfigUtil.isConfigCompose(YunConfigUtils.getDBConfig(ConfigKeyEnum.BUSINESS_AUTHORIZATION_NEED_DOWNLOAD_ORDER), request.getPlatValue().toString(), request.getMemberName())
                    && shopConfig != null && shopConfig.getIsAutoDownload()) {

                // 构建补单请求参数
                CreateOmitOrderJobRequestBizData requestBizData = this.createOmitOrderWorker(request);

                // 调用load接口进行补单
                apiLoadFeignClient.omit(request.getMemberName(), JSON.toJSONString(requestBizData));
            }
        } catch (Exception e) {
            LogAdapter.writeSystemLog("授权后补单异常", CoreUtils.exceptionToString(e));
        }

    }

    /**
     * 创建补单任务请求参数
     *
     * @param request 授权请求参数
     * @return 补单请求参数
     */
    protected CreateOmitOrderJobRequestBizData createOmitOrderWorker(HandlerPlatAuthorizationResponseBizData request) {

        // 补单请求参数
        CreateOmitOrderJobRequestBizData bizContent = new CreateOmitOrderJobRequestBizData();

        // 获取平台下载订单业务特性
        LoadFeatureConfigContent loadFeature = LoadFeatureUtils.getBizFeature(request.getMemberName(), request.getShopId());
        if (Objects.isNull(loadFeature) || Objects.isNull(loadFeature.getFirstLoadOffset())) {
            return bizContent;
        }

        bizContent.setMemberName(request.getMemberName());
        bizContent.setShopId(request.getShopId());
        bizContent.setPlat(request.getPlatValue());

        // 开始时间为当前时间 - 首次自动下次时间前时间
        Calendar calendar = CalendarUtil.calendar(System.currentTimeMillis());
        calendar.add(Calendar.SECOND, -loadFeature.getFirstLoadOffset().intValue());

        bizContent.setStartTime(DateUtil.toLocalDateTime(calendar.getTime()));

        // 结束时间为当前时间
        bizContent.setEndTime(DateTimeUtils.timeStampToLocalDateTime(System.currentTimeMillis()));
        bizContent.setIsForced(Boolean.TRUE);
        bizContent.setLoadOrderStatus(Collections.singletonList(Objects.toString(Business_OrderStatusEnum.JH_02.getValue())));
        bizContent.setNeedManual(Boolean.FALSE);
        return bizContent;
    }

    /**
     * 授权账号变更校验
     *
     * @param memberName 会员号
     * @param authMode   授权模式
     * @return 校验是否通过
     */
    protected boolean platAuthAccountChangeVerify(String memberName, PolyAPIAuthorizationEnum authMode,
                                                  HandlerPlatAuthorizationResponseBizData responseBizData) {
        try {

            // 没有开配置，或者配置不校验的直接返回true；
            if (!ConfigUtil.isConfigCompose(YunConfigUtils.getDBConfig(ConfigKeyEnum.BUSINESS_AUTHORIZATION_ACCOUNT_CHANGE_VERIFY), responseBizData.getPlatValue().toString(), memberName)
                    || ConfigUtil.isConfigCompose(YunConfigUtils.getDBConfig(ConfigKeyEnum.BUSINESS_AUTHORIZATION_ACCOUNT_WITHOUT_CHANGE_VERIFY), responseBizData.getPlatValue().toString(), memberName)) {
                return true;
            }

            // 查询该店铺里面有无网店订单信息，若无，则直接不检验
            Long tradeId = this.tradeOnlineMapper.queryOneTradeOnline(memberName, responseBizData.getShopId());
            if (null == tradeId) {
                return true;
            }

            // 查询当前店铺历史授权信息
            ApiShopAuthEntity oldShopAuthEntity =
                    ApiShopAuthInfoCache.build(memberName).getAndSyncIfAbsent(responseBizData.getShopId());
            // 如果店铺授权实体对象为空 表示店铺之前没有授权，直接通过
            if (oldShopAuthEntity == null) {
                return true;
            }
            // 授权模式的判断
            if (authMode.equals(PolyAPIAuthorizationEnum.AUTHORIZATION)) {

                // 店铺历史授权信息没有 nick 并且 VenderId 为空 表示平台之前没有返回这两个字段 不能进行账号变更验证判断,直接通过
                if (ExtUtils.isNullOrEmpty(oldShopAuthEntity.getPlatShopId()) && ExtUtils.isNullOrEmpty(oldShopAuthEntity.getAuthSellNick())) {
                    return true;
                }
                // 如果返回的 nick 并且 VenderId 为空 表示平台不能返回这两个值 无法进行账号并更判断，直接通过
                if (ExtUtils.isNullOrEmpty(responseBizData.getVenderId()) && ExtUtils.isNullOrEmpty(responseBizData.getNick())) {
                    return true;
                }

                // 对 venderid 判断
                if (!ExtUtils.isNullOrEmpty(oldShopAuthEntity.getPlatShopId()) && !ExtUtils.isNullOrEmpty(responseBizData.getVenderId()) && oldShopAuthEntity.getPlatShopId().equalsIgnoreCase(responseBizData.getVenderId())) {
                    // 平台店铺id相同，则直接校验通过，判断是同一个授权信息
                    return true;
                }
                if (!ExtUtils.isNullOrEmpty(oldShopAuthEntity.getPlatShopId()) && !ExtUtils.isNullOrEmpty(responseBizData.getVenderId()) && !oldShopAuthEntity.getPlatShopId().equalsIgnoreCase(responseBizData.getVenderId())) {

                    String authMsg = StringUtils.isBlank(oldShopAuthEntity.getAuthSellNick()) ? String.format("已授权的平台店铺id为：%s", oldShopAuthEntity.getPlatShopId()) : String.format("已授权的卖家昵称为：%s", oldShopAuthEntity.getAuthSellNick());
                    String logDetail = String.format("授权失败，不允许更换授权账号，原平台店铺id：%s，新授权平台店铺id：%s", oldShopAuthEntity.getPlatShopId(), responseBizData.getVenderId());
                    insertShopLog(responseBizData.getShopId(), memberName, logDetail);
                    responseBizData.setIsSuccess(false);
                    responseBizData.setMsg(String.format("当前店铺已经有平台授权信息，%s，更改授权失败，请使用原账号重新授权，以防店铺授权失效；<br><br><span style=\"color:red;\">如果想用新账号授权，请联系专属客户经理提交吉客云流程审批->更改特殊配置流程，说明原店铺使用新账号授权</span>", authMsg));
                    return false;
                }

                // 对 nick 判断
                if (!ExtUtils.isNullOrEmpty(oldShopAuthEntity.getAuthSellNick()) && !ExtUtils.isNullOrEmpty(responseBizData.getNick()) && !oldShopAuthEntity.getAuthSellNick().equalsIgnoreCase(responseBizData.getNick())) {

                    responseBizData.setIsSuccess(false);
                    String logDetail = String.format("授权失败，不允许更换授权账号，原卖家昵称：%s，新授权卖家昵称：%s", oldShopAuthEntity.getAuthSellNick(), responseBizData.getNick());
                    insertShopLog(responseBizData.getShopId(), memberName, logDetail);
                    responseBizData.setMsg(String.format("当前店铺已使用卖家昵称（昵称1：%s）授权，不支持用卖家昵称（昵称2：%s）授权，更改授权失败；请使用昵称1对当前店铺重新授权，以防店铺授权失效；<br><br><span style=\"color:red;\">如果想用新账号授权，请联系专属客户经理提交吉客云流程审批->更改特殊配置流程，说明原店铺使用新账号授权</span>", oldShopAuthEntity.getAuthSellNick(), responseBizData.getNick()));
                    return false;
                }

            } else if (authMode.equals(PolyAPIAuthorizationEnum.CUSTOM)) {
                // 历史店铺的自用型参数为空 不需要判断
                if (ExtUtils.isNullOrEmpty(oldShopAuthEntity.getSelfUseAuth())) {
                    return true;
                }
                try {
                    // 对新授权的自定义参数加密
                    String newSelfUseAuth = CoreUtils.makeAES(JsonUtils.toJson(responseBizData.getCustom()));
                    // 新老授权一致 直接返回
                    if (oldShopAuthEntity.getSelfUseAuth().equalsIgnoreCase(newSelfUseAuth)) {
                        return true;
                    }
                } catch (Exception ex) {
                    // 出现异常 不做任何处理 直接返回
                    return true;
                }
                // 不一致需要具体判断是什么原因导致的
                // 对老的授权自用型参数解密
                ShopConfigExtra.Custom oldCustomAuth;
                try {
                    String oldSelfUseAuth = CoreUtils.decryptAES(oldShopAuthEntity.getSelfUseAuth());
                    oldCustomAuth = JsonUtils.deJson(oldSelfUseAuth, ShopConfigExtra.Custom.class, false);
                    if (oldCustomAuth == null) {
                        return true;
                    }
                } catch (Exception ex) {
                    // 不做任何处理 直接返回
                    return true;
                }
                // 检测老授权和新授权参数是否一致  不一致不需要进行判断
                // 检测参数数量 数量不一致不需要判断
                if (oldCustomAuth.getItems().size() != responseBizData.getCustom().getItems().size()) {
                    return true;
                }
                // 检查参数名称
                for (PlatAuthorizationCustomBizData.Item customItem : responseBizData.getCustom().getItems()) {
                    Optional<ShopConfigExtra.Custom.Item> item =
                            oldCustomAuth.getItems().stream().filter(t -> t.getEkey().equalsIgnoreCase(customItem.geteKey())).findFirst();
                    // 如果老的里面不存在指定的ekey 直接返回 不需要处理
                    if (!item.isPresent()) {
                        return true;
                    }
                }
                responseBizData.setMsg("当前店铺已经有平台授权信息，更改授权失败，请对被冲突授权的店铺重新授权，以防店铺授权失效；<br><br><span style=\"color:red;\">如果想用新账号授权，请联系专属客户经理提交吉客云流程审批->更改特殊配置流程，说明原店铺使用新账号授权</span>");
                responseBizData.setIsSuccess(false);
                return false;
            }
            return true;
        } catch (Exception ex) {
            LogAdapter.writeSystemLog("授权账号变更校验", ex);
            return true;
        }
    }


    /**
     * 授权冲突通知
     *
     * @param memberName 授权的会员i给你
     * @param shopId     授权的店铺ID
     * @param response   响应结果
     * @return 提示消息集合
     */
    protected List<String> platAuthConflictNotice(String memberName, long shopId,
                                                  BasePlatResponse<SyncAccountResponseBizData> response) {
        // 跟踪日志
        StringBuilder traceLog = new StringBuilder();
        traceLog.append("[授权冲突通知]开始....<br>");
        traceLog.append(String.format("当前授权店铺id=%s", shopId));
        try {
            if (CollectionsUtil.isBlank(response.getBizData().getRepeatShops())) {
                traceLog.append("没有任何的授权冲突店铺.<br>");
                return Collections.emptyList();
            }
            // 获取当前店铺
            ApiShopBaseInfo currentShop = ApiShopCoreUtils.getShopBase(memberName, shopId);
            if (currentShop == null) {
                traceLog.append("未查询到当前的店铺信息.<br>");
                return Collections.emptyList();
            }
            // 不需要将进行提示的平台直接返回
            if (YunConfigUtils.contains(currentShop.getShopType().getValue().toString(),
                    ConfigKeyEnum.BUSINESS_AUTHORIZATION_CONFLICT_NOT_NEED_TIP_PLAT)) {
                traceLog.append("当前平台不需要发送通知.<br>");
                return Collections.emptyList();
            }
            // 去除当前会员当前店铺
            List<SyncAccountResponseRepeatShop> effectiveRepeatShopList = response.getBizData().getRepeatShops();
            // 没有任何冲突店铺
            if (CollectionsUtil.isBlank(effectiveRepeatShopList)) {
                traceLog.append("排除完当前店铺后没有任何授权冲突店铺.<br>");
                return Collections.emptyList();
            }
            // 存在冲突店铺
            // 提示信息
            List<String> tips = new ArrayList<>();
            // 按会员进行分组
            Map<String, List<String>> groupByMember = new HashMap<>();
            effectiveRepeatShopList.stream().collect(Collectors.groupingBy(SyncAccountResponseRepeatShop::getOutUserName)).forEach((member, tokenList) -> groupByMember.put(member, tokenList.stream().map(SyncAccountResponseRepeatShop::getToken).collect(Collectors.toList())));
            // 对分组的会员进行变量
            for (Map.Entry<String, List<String>> entry : groupByMember.entrySet()) {
                sendConflictNotice(memberName, currentShop, entry, tips, traceLog);
            }
            if (CollectionsUtil.isNotBlank(tips)) {
                tips.add(0, "授权成功");
            }
            return tips;
        } catch (Exception ex) {
            LogAdapter.writeSystemLog("授权冲突通知", ex);
            traceLog.append("发送异常：" + CoreUtils.exceptionToString(ex));
        } finally {
            LogAdapter.writeSystemLog(memberName + "-授权冲突通知", traceLog.toString(), LogTypeEnum.TRACE);
        }
        return Collections.emptyList();
    }

    /**
     * 发送冲突通知
     *
     * @param currentMemberName     当前会员
     * @param currentShop           当前店铺
     * @param conflictMemberShopMap 冲突的会员店铺
     * @param tips                  提示信息集合
     * @param traceLog              跟踪日志
     */
    private void sendConflictNotice(String currentMemberName, ApiShopBaseInfo currentShop, Map.Entry<String,
            List<String>> conflictMemberShopMap,
                                    List<String> tips, StringBuilder traceLog) {
        // 是否为当前会员
        if (conflictMemberShopMap.getKey().equalsIgnoreCase(currentMemberName)) {
            traceLog.append("当前操作授权的会员，自己授权冲突的店铺，");
            // 根据token查询店铺信息
            List<ApiShopBaseInfo> apiShopBaseInfoList = new ArrayList<>();
            for (String token : conflictMemberShopMap.getValue()) {
                ApiShopBaseInfo shopBaseInfo = ApiShopCoreUtils.getShopBaseByToken(conflictMemberShopMap.getKey(), token);
                if (shopBaseInfo != null && shopBaseInfo.getShopType() != null
                        && shopBaseInfo.getShopType().equals(currentShop.getShopType())
                        && !shopBaseInfo.getShopId().equals(currentShop.getShopId())) {
                    ApiShopAuthInfo shopAuthInfo = ApiShopCoreUtils.getShopAuth(conflictMemberShopMap.getKey(),
                            shopBaseInfo.getShopId());
                    // 店铺授权信息不存在 或则 非正常授权状态不需要提醒
                    if (shopAuthInfo != null && ShopAuthStatusEnum.AUTHORIZED.equals(shopAuthInfo.getAuthStatus())) {
                        apiShopBaseInfoList.add(shopBaseInfo);
                    }
                }
            }
            if (CollectionsUtil.isBlank(apiShopBaseInfoList)) {
                traceLog.append("根据返回的授权冲突的店铺token未查询到店铺信息.<br>");
                return;
            }
            String authLog = String.format("店铺授权发生冲突，在店铺【%s】中使用了同一账号授权.",
                    currentShop.getShopName());
            if (BooleanUtils.isTrue(shopAuthService.modifyShopAuthStatus(currentMemberName,
                    apiShopBaseInfoList.stream().map(ApiShopBaseInfo::getShopId).collect(Collectors.toList()),
                    ShopAuthStatusEnum.AUTO_CONFLICT, authLog))) {
                tips.add(0, String.format("当前店铺的授权账号与【%s】店铺一致",
                        StringUtils.join(apiShopBaseInfoList.stream().map(ApiShopBaseInfo::getShopName).collect(Collectors.toList()), "、")));

                for (ApiShopBaseInfo itemApiShopBaseInfo : apiShopBaseInfoList) {
                    try {
                        SpringResolveManager.resolve(IShopAuthorizationAdapter.class).syncShopAuthorizationStatusProfile(currentMemberName,
                                itemApiShopBaseInfo.getShopId(), itemApiShopBaseInfo.getPolyToken(), itemApiShopBaseInfo.getShopType(),
                                PolyShopStatusEnum.NOT_AVAILABLE,
                                "店铺授权冲突");
                    } catch (Exception ex) {
                        LogAdapter.writeSystemLog("授权冲突提醒异常", CoreUtils.exceptionToString(ex));
                    }
                }
            }
            traceLog.append("当前授权店铺与自己存在店铺授权冲突.<br>");
        } else {
            // 通知其他冲突会员
            noticeOtherMember(conflictMemberShopMap.getKey(), conflictMemberShopMap.getValue(), currentMemberName,
                    currentShop, tips, traceLog);
            traceLog.append(String.format("成功通知其他会员%s.<br>", conflictMemberShopMap.getKey()));
        }
    }

    /**
     * 通知其他会员
     *
     * @param noticeMember      通知的会员
     * @param conflictTokens    冲突的店铺tokens
     * @param currentMemberName 当前操作授权的会员
     * @param currentShop       当前操作授权的店铺
     */
    private void noticeOtherMember(String noticeMember, List<String> conflictTokens, String currentMemberName,
                                   ApiShopBaseInfo currentShop, List<String> tips, StringBuilder traceLog) {
        // 获取通知会员的集群号
        String groupNo = DomainUtils.getGroupNoOptimize(noticeMember);
        if (ExtUtils.isNullOrEmpty(groupNo)) {
            traceLog.append(String.format("通知会员%s的集群号为空", noticeMember)).append("<br>");
            return;
        }
        ShopAuthConflictNoticeRequestDto requestDto = new ShopAuthConflictNoticeRequestDto();
        requestDto.setNoticeMemberName(noticeMember);
        requestDto.setConflictShopTokens(conflictTokens);
        requestDto.setAuthShop(currentShop);
        requestDto.setAuthMemberName(currentMemberName);
        requestDto.setAuthRealName(DomainUtils.getRealName());
        ShopAuthConflictNoticeResponseDto responseDto = null;
        // 是否为当前集群
        if (BooleanUtils.isTrue(DomainUtils.isMemberBelongLocalGroup(noticeMember))) {
            responseDto = shopAuthService.shopAuthConflictNotice(requestDto);
            // 绑定回当前会员
            DomainUtils.bindJackNo(currentMemberName);
        } else {
            JackYunResponse<Object> noticeResponse = DomainUtil.doWithMemberFunc(noticeMember,
                    memberInfo -> shopFeignClient.shopAuthConflictNotice(noticeMember, groupNo,
                            JsonUtils.toJson(requestDto)));
            // 绑定回当前会员
            DomainUtils.bindJackNo(currentMemberName);
            //shopFeignClient.shopAuthConflictNotice(noticeMember, groupNo,
            //JsonUtils.toJson(requestDto));
            if (noticeResponse != null && noticeResponse.isSuccess() && noticeResponse.getResult() != null && noticeResponse.getResult().getData() != null) {
                responseDto =
                        JsonUtils.deJson(JsonUtils.toJson(noticeResponse.getResult().getData()),
                                ShopAuthConflictNoticeResponseDto.class);
            }
        }
        ShopAuthConflictNoticeResponseDto finalResponseDto = responseDto;
        LogAdapter.writeBusinessLog(currentMemberName + "-授权冲突通知", () -> ExtUtils.stringBuilderAppend("授权通知冲突通知返回：", JSON.toJSONString(finalResponseDto)));
        if (responseDto != null && CollectionsUtil.isNotBlank(responseDto.getConflictShops())) {
            String msg = String.format("店铺授权账号与吉客号【%s】的【%s】店铺一致", noticeMember,
                    StringUtils.join(responseDto.getConflictShops().stream().map(ApiShopBaseInfo::getShopName).collect(Collectors.toList()), "、"));
            tips.add(msg);
            if (responseDto.getTrackLog() != null) {
                traceLog.append(responseDto.getTrackLog().toString()).append("<br>");
            }
        }
    }

    /**
     * 错误关键字替换
     *
     * @param errorSubCode 错误子code
     * @param errorMsg     错误消息
     * @return 返回替换结果
     */
    protected String errorKeyWordsReplace(PolyAPIPlatEnum plat, String errorSubCode, String errorMsg) {
        String matchMsg = errorMsg;
        if (null != plat && YunConfigUtils.contains(plat.getValue().toString(),
                ConfigKeyEnum.SHOP_AUTH_MATCH_ERROR_KEY_WORDS)) {
            // 优先subCode
            matchMsg = DomainUtils.matchErrorMsgWithConfigV2(errorSubCode, errorMsg, plat);
        }
        return matchMsg;
    }

    // region 店铺授权共享


    /**
     * 店铺授权共享
     *
     * @param currentMemberName 当前会员
     * @param currentShopId     当前授权店铺
     * @param polyResponse      授权聚合的响应结果
     * @return 授权共享提醒内容
     */
    public List<String> shopAuthShare(String currentMemberName, long currentShopId,
                                      BasePlatResponse<SyncAccountResponseBizData> polyResponse) {
        List<String> tipList = new ArrayList<>();

        // 判断聚合返回的结果是否有授权账号相同的店铺
        if (null == polyResponse || null == polyResponse.getBizData() || CollectionsUtil.isBlank(polyResponse.getBizData().getRepeatShops())) {
            return tipList;
        }
        List<SyncAccountResponseRepeatShop> repeatShopList = polyResponse.getBizData().getRepeatShops();

        // 获取当前授权的店铺
        ApiShopBaseInfo currentShop = ApiShopCoreUtils.getShopBase(currentMemberName, currentShopId);
        if (currentShop == null) {
            return tipList;
        }
        // 是否需要被忽略提醒
        if (YunConfigUtils.contains(String.valueOf(currentShop.getShopType().getValue()), ConfigKeyEnum.SHOP_AUTH_SHARE_IGNORE_TIP_PLAT)) {
            return tipList;
        }

        // 按会员分组进行处理
        repeatShopList.stream().collect(Collectors.groupingBy(SyncAccountResponseRepeatShop::getOutUserName)).forEach((memberName, itemRepeatShopList) -> {
            List<String> itemTipList;
            // 是否为当前会员
            if (StringUtils.equalsIgnoreCase(currentMemberName, memberName)) {
                itemTipList = shopAuthShareNoticeCurrentMember(memberName, currentShop, itemRepeatShopList);
            } else {
                itemTipList = shopAuthShareNoticeOtherMember(currentMemberName, currentShop, memberName, itemRepeatShopList);
            }
            if (CollectionsUtil.isNotBlank(itemTipList)) {
                tipList.addAll(itemTipList);
            }
        });
        if (CollectionsUtil.isNotBlank(tipList)) {
            tipList.add(0, "<span style=\"color:red;\">* 风险提醒：</span>");
            tipList.add("2.该店铺两边将同时支持订单下载、发货、库存同步等功能，导致同步平台数据不一致， 请注意重复发货的问题");
        }
        return tipList;
    }

    /**
     * 授权共享通知其他会员
     *
     * @param currentMemberName 当前会员
     * @param currentShop       当前店铺
     * @param noticeMember      需要被通知的会员
     * @param repeatShopList    冲突的店铺token集合
     * @return 返回提示语
     */
    private List<String> shopAuthShareNoticeOtherMember(String currentMemberName, ApiShopBaseInfo currentShop, String noticeMember, List<SyncAccountResponseRepeatShop> repeatShopList) {
        List<String> tipList = new ArrayList<>();
        // 获取通知会员的集群号
        String groupNo = DomainUtils.getGroupNoOptimize(noticeMember);
        if (ExtUtils.isNullOrEmpty(groupNo)) {
            return tipList;
        }
        ShopAuthConflictNoticeRequestDto requestDto = new ShopAuthConflictNoticeRequestDto();
        requestDto.setNoticeMemberName(noticeMember);
        requestDto.setConflictShopTokens(repeatShopList.stream().map(SyncAccountResponseRepeatShop::getToken).collect(Collectors.toList()));
        requestDto.setAuthShop(currentShop);
        requestDto.setAuthMemberName(currentMemberName);
        requestDto.setAuthRealName(DomainUtils.getRealName());
        ShopAuthConflictNoticeResponseDto response = null;
        // 是否为当前集群
        if (BooleanUtils.isTrue(DomainUtils.isMemberBelongLocalGroup(noticeMember))) {
            response = shopAuthService.shopAuthShareNotice(requestDto);
        } else {
            JackYunResponse<Object> noticeResponse = DomainUtil.doWithMemberFunc(noticeMember,
                    memberInfo -> shopFeignClient.ShopAuthShareNotice(noticeMember, groupNo,
                            JsonUtils.toJson(requestDto)));
            if (noticeResponse != null && noticeResponse.isSuccess() && noticeResponse.getResult() != null && noticeResponse.getResult().getData() != null) {
                response = JsonUtils.deJson(JsonUtils.toJson(noticeResponse.getResult().getData()), ShopAuthConflictNoticeResponseDto.class);
            }
        }

        // 绑定回当前会员
        DomainUtils.bindJackNo(currentMemberName);

        if (null != response && CollectionsUtil.isNotBlank(response.getConflictShops())) {
            tipList.add(String.format("1.店铺授权账号与吉客号[%s]的【%s】店铺一致", noticeMember, response.getConflictShops().stream().map(ApiShopBaseInfo::getShopName).collect(Collectors.joining(","))));
        }
        return tipList;
    }

    /**
     * 授权共享通知当前会员
     *
     * @param memberName     会员名
     * @param currentShop    当前授权店铺
     * @param repeatShopList 店铺集合
     * @return 返回结果
     */
    public List<String> shopAuthShareNoticeCurrentMember(String memberName, ApiShopBaseInfo currentShop, List<SyncAccountResponseRepeatShop> repeatShopList) {
        List<String> tipList = new ArrayList<>();

        // 根据店铺token查询授权一致的店铺信息
        List<ApiShopBaseInfo> shopBaseInfoList = this.getNormalAuthShopBaseByToken(memberName, repeatShopList.stream().map(SyncAccountResponseRepeatShop::getToken).collect(Collectors.toList()));
        if (CollectionsUtil.isBlank(shopBaseInfoList)) {
            return tipList;
        }

        // 提示语
        String tip = String.format("1.当前店铺授权账号在店铺【%s】中使用了同一账号授权", shopBaseInfoList.stream().map(ApiShopBaseInfo::getShopName).collect(Collectors.joining("、")));
        tipList.add(tip);


        // 写入店铺日志
        for (ApiShopBaseInfo apiShopBaseInfo : shopBaseInfoList) {
            this.insertShopLog(apiShopBaseInfo.getShopId(), memberName, String.format("在店铺【%s】中使用了同一授权账号.", currentShop.getShopName()));
        }
        return tipList;
    }


    /**
     * 获取正常授权的店铺信息
     *
     * @param memberName 会员名
     * @param shopTokens 店铺token集合
     * @return 返回店铺集合
     */
    private List<ApiShopBaseInfo> getNormalAuthShopBaseByToken(String memberName, List<String> shopTokens) {
        return shopAuthService.getShopBaseByTokenAndAuthStatus(memberName, shopTokens, ShopAuthStatusEnum.AUTHORIZED);
    }


    // endregion

    //endregion

    //region 私有方法

    /**
     * 取消店铺授权。
     *
     * @param shopConfig 店铺配置
     * @return 是否成功
     */
    private boolean cancelAuthorization(ShopConfigEntity shopConfig) {
        // 创建取消店铺授权领域事件。
        CancelAuthorizationEvent cancelAuthorizationEvent = new CancelAuthorizationEvent(shopConfig.getMemName(), shopConfig.getShopType(), new PolyCancelAuthorizationRequestBizData(), shopConfig.getShopId());
        DomainEventServiceManager.get().publishSafe(cancelAuthorizationEvent);

        BasePlatResponse<PolyCancelAuthorizationResponseBizData> cancelAuthorizationResponseBizData = cancelAuthorizationEvent.getResponse();

        // 判断取消授权是否成功。
        final boolean success = cancelAuthorizationResponseBizData != null && Boolean.TRUE.equals(cancelAuthorizationResponseBizData.getIsSuccess());

        this.writeBusinessLogForce(() -> ExtUtils.stringBuilderAppend("请求菠萝派取消授权", success ? "成功" : "失败", "，返回结果报文: ", JsonUtils.toJson(cancelAuthorizationResponseBizData)));
        return success;
    }

    /**
     * 同步店铺授权状态到菠萝派
     *
     * @param request 更新店铺授权状态请求参数
     * @return 是否成功
     */
    private SyncShopStatusResponseBizData syncShopStatusToPoly(SyncShopStatusRequestBizData request) {
        SyncShopStatusResponseBizData response = new SyncShopStatusResponseBizData();
        // 初始化请求参数
        PolySyncShopStatusRequestBizData polySyncShopStatusRequestBizData = new PolySyncShopStatusRequestBizData();
        polySyncShopStatusRequestBizData.setToken(request.getToken());
        polySyncShopStatusRequestBizData.setShopStatus(request.getShopStatus());
        polySyncShopStatusRequestBizData.setReason(request.getOption());
        polySyncShopStatusRequestBizData.setPlat(request.getPlatValue());
        polySyncShopStatusRequestBizData.setCustom(request.getCustom());

        // 若参数中appKey 不为空则覆盖, 否则从授权缓存中获取
        if (null != request.getCustom() && CollectionUtils.isNotEmpty(request.getCustom().getItems())) {
            request.getCustom().getItems().forEach(item -> {
                if ("appkey".equals(StringUtils.lowerCase(item.getcKey()))) {
                    polySyncShopStatusRequestBizData.setAppKey(item.getValue());
                }
            });
        } else {
            PlatAuthorizeAdapter platAuthorizeAdapter = SpringResolveManager.resolve(PlatAuthorizeAdapter.class);
            Pair<String, String> platAuthorize = platAuthorizeAdapter.getPlatAuthorize(PolyAPITypeEnum.COMMON_SYNCSHOPSTATUS,
                    request.getPlatValue(), request.getMemberName(), request.getShopId());
            polySyncShopStatusRequestBizData.setAppKey(platAuthorize.getKey());
        }

        // 请求平台
        SyncShopStatusApiCall apiCall = SpringResolveManager.resolve(SyncShopStatusApiCall.class);
        ApiCallResponse<PolySyncShopStatusResponseBizData> responseBizDataApiCallResponse = apiCall.call(request.getMemberName(), request.getShopId(), polySyncShopStatusRequestBizData);

        // 判断更新店铺授权状态是否成功
        final boolean success = null != responseBizDataApiCallResponse && Boolean.TRUE.equals(responseBizDataApiCallResponse.getIsSuccess());

        // 跟踪日志
        LogAdapter.writeBusinessLog(SYNC_SHOP_AUTHORIZATION_STATUS_CAPTION, () -> ExtUtils.stringBuilderAppend("请求菠萝派更新店铺授权状态", success ? "成功" : "失败", "，结果报文：", JsonUtils.toJson(responseBizDataApiCallResponse)));

        // 处理响应结果信息
        response.setIsSuccess(success);
        if (null != responseBizDataApiCallResponse) {
            response.setMsg(responseBizDataApiCallResponse.getSubMessage());
            response.setSubMessage(responseBizDataApiCallResponse.getSubMessage());
        }
        return response;
    }

    /**
     * 添加平台店铺信息到CMS。
     *
     * @param platValue  平台枚举
     * @param sellerNick 平台店铺名称(昵称)
     */
    protected void addShopAuthorizationToCMS(PolyAPIPlatEnum platValue, String sellerNick) {
        if (ExtUtils.isNullOrEmpty(sellerNick)) {
            return;
        }

        try {
            AddUserShopRequestBizData bizData = new AddUserShopRequestBizData();
            bizData.setMemberName(DomainUtils.getMemberName());
            bizData.setPlatformType(platValue.getPlatValue());
            // 默认吉客云。
            bizData.setCompanyType(2);
            bizData.setShopName(sellerNick);

            // 添加会员店铺到期信息。
            JackYunResponse<Object> response = this.cmsBmsFeignClient.addUserShop(bizData.getMemberName(), JsonUtils.toJson(bizData));

        } catch (Exception ex) {
            DomainUtils.writeBusinessLogForce(ApplicationTypeEnum.BUSINESS, DomainUtils.getContextID(),
                    () -> ExtUtils.stringBuilderAppend("添加会员店铺到期信息发生异常：", CoreUtils.exceptionToString(ex)));
        }
    }

    /**
     * 取消EDI店铺授权。
     *
     * @param shopId 店铺Id。
     */
    private void cancelEdiShopAuthorization(Long shopId) {
        CancelEdiShopAuthorizationRequestBizData bizData = new CancelEdiShopAuthorizationRequestBizData();
        bizData.setShopId(shopId.toString());

        this.deiCommonFeignClient.cancelShopAuthorization("differ.edi.authorization.cancelshopauthorization", JsonUtils.toJson(bizData), Long.toString(DateTimeUtils.getTimestamp()), "1.0");
    }

    /**
     * 通知ERP授权失效
     *
     * @param memberName 会员名
     * @param shopId     店铺id
     */
    private void noticeErpAuthInvalid(String memberName, long shopId) {
        try {
            // 同步ERP授权失效
            JackYunResponse<Object> response = bizErpFeignClient.erpAuthorizationExpire(memberName, shopId, (byte) 1);
            if (response == null || response.getCode() != 200) {
                String msg = String.format("[%s]店铺[%s]取消授权同步ERP失败.", memberName, shopId);
                LogAdapter.writeSystemLog("取消授权", msg, LogTypeEnum.ERROR);
            }
        } catch (Exception ex) {
            LogAdapter.writeSystemLog("取消授权通知ERP出现异常", ex);
        }
    }

    /**
     * 插入授权日志
     *
     * @param shopId
     * @param memberName
     * @param logDetail
     */
    private void insertShopLog(Long shopId, String memberName, String logDetail) {
        ConfShopLogEntity logEntity = new ConfShopLogEntity();
        logEntity.setChannelId(shopId);
        logEntity.setUserId(DomainUtils.getUserId());
        logEntity.setUserName(DomainUtils.getRealName());
        logEntity.setOperateType(ShopLogOperateTypeEnum.AUTHOR_OPERATE.getValue());
        logEntity.setLogDetail(logDetail);
        SpringResolveManager.resolve(ConfShopLogDBMapper.class).insertConfShopLogs(memberName, Collections.singletonList(logEntity));

    }

    /**
     * 是否允许清空店铺授权信息
     *
     * @param memberName 吉客号
     * @return true:允许清空
     */
    private boolean isAllowCleanShopAuthInfo(CancelPlatAuthorizationRequestBizData request, String memberName) {
        // 非客户操作的取消授权不允许清空数据
        if (null != request && BooleanUtils.isFalse(request.getFromUserOperation())) {
            return false;
        }
        return YunConfigUtils.contains(memberName, ConfigKeyEnum.SHOP_AUTH_CANCEL_ALLOW_CLEAN_INFO) && !YunConfigUtils.contains(memberName, ConfigKeyEnum.SHOP_AUTH_CANCEL_NOT_ALLOW_CLEAN_INFO);
    }

    /**
     * 是否允许店铺授权信息共享
     *
     * @param memberName 会员名
     * @return 返回结果
     */
    private boolean isAllowShopAuthShare(String memberName, PolyAPIPlatEnum plat) {
        // 获取配制值。
        String configValue = YunConfigUtils.getDBConfig(ConfigKeyEnum.SHOP_AUTH_ALLOW_SHARE_MEMBER);
        return ConfigUtil.isConfigCompose(configValue, String.valueOf(plat.getPlatValue()), memberName);
    }
    //endregion
}
