package com.differ.jackyun.omsapibusiness.service.match;

import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.goods.match.goods.request.AoXiangGiftCreateRequestBizData;

/**
 * 翱象相关商品及匹配接口
 *
 * <AUTHOR>
 * @date 2022/2/22 11:01
 */
public interface IAoXiangGoodsService {

    /**
     * 翱象赠品创建货品和匹配
     *
     * @param requestBizData 请求参数
     * @return 成功or失败
     */
    Boolean aoXiangGiftCreateGoodsAndMatch(AoXiangGiftCreateRequestBizData requestBizData);
}
