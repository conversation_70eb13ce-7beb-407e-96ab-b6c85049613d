/**
 * Copyright(C) 2018 Hangzhou Differsoft Co., Ltd. All rights reserved.
 */
package com.differ.jackyun.omsapibusiness;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.differ.jackyun.framework.component.basic.dto.JackYunResponse;
import com.differ.jackyun.framework.component.basic.interceptor.CommonContextHolder;
import com.differ.jackyun.framework.component.basic.member.member.MemberHolder;
import com.differ.jackyun.framework.component.basic.member.member.MemberInfo;
import com.differ.jackyun.omsapibase.dao.jackyunmultidb.goodsdelivery.DaoTradeOrderAdditionDBSwitchMapper;
import com.differ.jackyun.omsapibase.data.open.crm.FilterCustomerInfoDto;
import com.differ.jackyun.omsapibase.data.open.erp.BaseShopInfo;
import com.differ.jackyun.omsapibase.data.open.oms.*;
import com.differ.jackyun.omsapibase.data.open.omsonline.OnlineGoodsMatchEntity;
import com.differ.jackyun.omsapibase.data.open.omsonline.OpenOmsOnlineOrderDto;
import com.differ.jackyun.omsapibase.data.open.omsonline.OpenOmsOnlineRefundDto;
import com.differ.jackyun.omsapibase.data.order.*;
import com.differ.jackyun.omsapibase.data.order.goods.AddOrderOriginalGoodsDTO;
import com.differ.jackyun.omsapibase.data.order.goods.GetOrderGoodsByTradeIdsDTO;
import com.differ.jackyun.omsapibase.data.order.goods.OrderOriginalGoodsEntity;
import com.differ.jackyun.omsapibase.data.order.original.*;
import com.differ.jackyun.omsapibase.data.order.originalextra.AddOrderOriginalExtraDTO;
import com.differ.jackyun.omsapibase.data.order.originalextra.OrderOriginalExtraEntity;
import com.differ.jackyun.omsapibase.data.refund.RefundOriginalProcessCommand;
import com.differ.jackyun.omsapibase.data.shopconf.command.AddOrUpdateShopConfCommand;
import com.differ.jackyun.omsapibase.data.shopconf.command.UpdateShopConfCommand;
import com.differ.jackyun.omsapibase.data.shopconf.entity.ShopConfigEntity;
import com.differ.jackyun.omsapibase.domain.core.DomainEventServiceManager;
import com.differ.jackyun.omsapibase.domain.core.DomainUtils;
import com.differ.jackyun.omsapibase.domain.dataproxy.jackyunpublic.BaseShopInfoProxy;
import com.differ.jackyun.omsapibase.domain.dataproxy.matchgoods.OrderOriginalGoodsMatchResultDataProxy;
import com.differ.jackyun.omsapibase.domain.dataproxy.shopconf.ShopConfDataProxy;
import com.differ.jackyun.omsapibase.feign.FeignGateway;
import com.differ.jackyun.omsapibase.feign.UserGroupFeignClient;
import com.differ.jackyun.omsapibase.infrastructure.component.mq.data.business.PostOrderToOMSMQData;
import com.differ.jackyun.omsapibase.infrastructure.component.mq.omsmq.MQTypesEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.ApplicationTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.LogTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.SiteTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.order.OriginalOrderSendStatusEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.order.OriginalOrderStatusEnum;
import com.differ.jackyun.omsapibase.infrastructure.openapi.enums.OMSAPIOpenAPITypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.openapi.getplatfeatures.GetPlatFeaturesRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.openapi.orderoriginalcreate.OrderOriginalCreateEventRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.openapi.refundoriginalcreate.RefundOriginalCreateEventRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.openapi.syncshopconfiguration.SyncShopConfigurationRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.enums.Business_OrderStatusEnum;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.getorder.PolyAPIBusinessGetOrderResponseBizData;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.getorder.PolyAPIBusinessGetRefundOrderResponseBizData;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.core.PolyAPIPlatEnum;
import com.differ.jackyun.omsapibase.infrastructure.utils.*;
import com.differ.jackyun.omsapibase.service.core.JsonDeserializerRegister;
import com.differ.jackyun.omsapibase.service.core.ServiceUtils;
import com.differ.jackyun.omsapibase.service.core.SubscribeDomainEventService;
import com.differ.jackyun.omsapibase.service.shopconf.IShopConfService;
import com.differ.jackyun.omsapibase.tasks.mq.BaseMessageHandlerFactory;
import com.differ.jackyun.omsapibase.tasks.mq.MQManager;
import com.differ.jackyun.omsapibase.tasks.taskmanager.BaseTaskManagerFactory;
import com.differ.jackyun.omsapibusiness.domain.core.BusinessSubscribeDomainEventManager;
import com.differ.jackyun.omsapibusiness.domain.openapi.getplatfeatures.event.GetPlatFeaturesEvent;
import com.differ.jackyun.omsapibusiness.domain.openapi.getplatfeatures.eventhandler.GetPlatFeaturesEventHandler;
import com.differ.jackyun.omsapibusiness.domain.service.post.event.CompensatePostEvent;
import com.differ.jackyun.omsapibusiness.domain.service.post.event.GlobalNoticeAutoPostEvent;
import com.differ.jackyun.omsapibusiness.service.order.IOrderOriginalService;
import com.differ.jackyun.omsapibusiness.service.refund.IRefundOriginalService;
import com.differ.jackyun.omsapibusiness.tasks.mq.BusinessMessageHandlerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

;

/**
 * 单元测试订单相关信息
 *
 * <AUTHOR>
 * @since 2019年4月23日 上午17:53:07
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {OmsapiBusinessApplication.class})
public class OrderOriginalTest extends BaseTest {
    // region 变量

    /**
     * 订单接口
     */

    @Autowired
    private IOrderOriginalService orderOriginalService;

    /**
     * 订单接口
     */
    @Autowired
    private IRefundOriginalService refundOriginalService;

    @Autowired
    private GetPlatFeaturesEventHandler getPlatFeaturesEventHandler;


    /**
     * 店铺配置
     */
    @Autowired
    private IShopConfService shopConfService;

    /**
     * 吉客云会员名
     */
    private String memName = "jackyun_dev";

    // endregion

    // region 单元测试执行之前

    /**
     * 单元测试执行之前
     */
    @Before
    public void beforeTest() {
        this.init();
    }

    // endregion 

    // region 单远测试初始化

    /**
     * 单远测试初始化。
     */
    @Override
    public void init() {
        this.init(true);
    }

    /**
     * 单远测试初始化
     *
     * @param isInitEDITask 是否初始化EDI任务管理器
     */
    @Override
    public void init(Boolean isInitEDITask) {
        if (SubscribeDomainEventService.get().getIsSubscribe()) {
            return;
        }

        System.out.println("开始启动");
        // 注册json反序列化配置。
        JsonDeserializerRegister.get().register();
        // 订阅领域事件
        SubscribeDomainEventService.get().Subscribe(BusinessSubscribeDomainEventManager.get());
        if (isInitEDITask) {
            this.initEDITask();
        }
        System.out.println("程序启动成功");

    }

    /**
     * 初始化EDI任务管理器。
     */
    private void initEDITask() {
        // 初始化MQ消息发送。
        MQManager.get().initMessageSender(ServiceUtils.getMQSender());

        // 初始化MQ消息发送。
        MQManager.get().initMessageReceiver(BusinessMessageHandlerFactory.get(), ServiceUtils.getMQReceiver());
    }

    /**
     * 释放EDI定时任务管理器。
     */
    @Override
    public void disposeEDITask() {
        // 程序结束时进行的操作
        MQManager.get().dispose(BaseMessageHandlerFactory.get());
        // 停止定时任务管理器。
        BaseTaskManagerFactory.get().stop();
    }

    // endregion

    // region 自动绑定并返回全局上下文请求ID

    /**
     * 自动绑定并返回全局上下文请求ID.
     *
     * @return
     */
    @Override
    protected Long bindContextID() {
        Long id = DomainUtils.generateId();
        CommonContextHolder.setLoggerSn(id);
        return id;
    }

    // endregion

    // region  获取全局上下文ID

    /**
     * 获取全局上下文ID。
     *
     * @return
     */
    @Override
    protected Long getContextID() {
        return CommonContextHolder.getLoggerSn();
    }

    // endregion

    // region 绑定会员

    /**
     * 绑定会员
     *
     * @param userName 会员名
     */
    @Override
    protected void bindMember(String userName) {
        MemberInfo mi = new MemberInfo();
        mi.setMemberName(userName);
        mi.setGroupId("g001");
        MemberHolder.bindMember(mi);
    }

    // endregion

    // region 测试获取订单信息

    /**
     * 测试获取订单信息
     */
    @Test
    public void getOrderOriginalTest() {
        DomainUtils.bindMemberUser(memName);
        GetOrderOriginalByTradeNosQuery query = new GetOrderOriginalByTradeNosQuery();
        List<String> tradeNos = new ArrayList<>();
        query.setShopId(381544268502408320l);
        tradeNos.add("94799094874");
        query.setTradeNos(tradeNos);
        List<OrderOriginalEntity> orderList = orderOriginalService.getOrderOriginalByTradeNos(memName, query);
        // 断言
        Assert.assertNotNull(orderList);
        System.out.println("订单查询结果：" + JSON.toJSONString(orderList));
    }

    // endregion

    // region 测试获取订单物品信息

    /**
     * 测试获取订单物品信息
     */
    @Test
    public void getOrderGoodsTest() {
        DomainUtils.bindMemberUser(memName);
        List<Long> tradeIdList = new ArrayList<Long>();
        tradeIdList.add(621800766931316864l);
        List<OrderOriginalGoodsEntity> goodsList = orderOriginalService.getOrderGoodsByTradeIds(memName, tradeIdList);
        // 断言
        Assert.assertNotNull(goodsList);
        System.out.println("订单物品查询结果：" + JSON.toJSONString(goodsList));
    }

    // endregion

    // region 测试获取订单物品信息

    /**
     * 测试获取订单物品信息
     */
    @Test
    public void getOrderGoodsInfoTest() {
        DomainUtils.bindMemberUser(memName);
        List<Long> tradeIdList = new ArrayList<Long>();
        tradeIdList.add(621800766931316864l);
        List<GetOrderGoodsByTradeIdsDTO> goodsList = orderOriginalService.getOrderGoodsInfoByTradeIds(memName, tradeIdList);
        // 断言
        Assert.assertNotNull(goodsList);
        System.out.println("订单物品查询结果：" + JSON.toJSONString(goodsList));
    }

    // endregion

    // region 测试获取原始单扩展表信息

    /**
     * 测试获取原始单扩展表信息
     */
    @Test
    public void getOrderOriginalextraTest() {
        DomainUtils.bindMemberUser(memName);
        List<Long> tradeIdList = new ArrayList<Long>();
        tradeIdList.add(623008109109454848l);
        List<OrderOriginalExtraEntity> orderExtraList = orderOriginalService.getOrderOriginalextraByTradeIds(memName, tradeIdList);
        // 断言
        Assert.assertNotNull(orderExtraList);
        System.out.println("订单扩展信息查询结果：" + JSON.toJSONString(orderExtraList));
    }

    // endregion

    // region 测试网店订单递交后更新订单状态

    /***
     * 测试网店订单递交后更新订单状态
     * @throws Exception
     */
    @Test
    public void updateTradeStatusTest() throws Exception {
        DomainUtils.bindMemberUser(memName);
        List<Long> lstTradeId = new ArrayList<Long>();
        ModifyOrderCurrentStatusBizData bizData = new ModifyOrderCurrentStatusBizData();
        lstTradeId.add(621800766931316864l);
        Map<Long, Long> map = new HashMap<>();
        map.put(12345678962L, 628813430400331904L);
        orderOriginalService.modifyOriginalCurrentStatus(new ModifyOriginalCurrentStatusCommond(memName, bizData));
        System.out.println("更新成功！");
    }

    // endregion

    // region 测试获取订单信息

    /**
     * 测试获取订单信息
     */
    @Test
    public void getExitsOrderTest() {
        DomainUtils.bindMemberUser(memName);
        GetOrderOriginalByTradeNosQuery query = new GetOrderOriginalByTradeNosQuery();
        List<String> tradeNos = new ArrayList<>();
        query.setShopId(381544268502408320l);
        tradeNos.add("94799094874");
        query.setTradeNos(tradeNos);
        List<ExistOrderOriginalDTO> orderList = orderOriginalService.getExitsOrder(memName, query);
        // 断言
        Assert.assertNotNull(orderList);
        System.out.println("订单查询结果：" + JSON.toJSONString(orderList));
    }

    // endregion

    // region 原始订单批量添加

    /**
     * 原始订单批量添加
     */
    @Test
    public void addOrderTest() throws Exception {
        DomainUtils.bindMemberUser(memName);
        List<AddOriginalOrderInfo> lstOriginalOrder = new ArrayList<AddOriginalOrderInfo>();
        BatchAddOrderOriginalCommand command = new BatchAddOrderOriginalCommand(memName, lstOriginalOrder);
        AddOriginalOrderInfo addOriginalOrderInfo = new AddOriginalOrderInfo();

        // 物品信息
        List<AddOrderOriginalGoodsDTO> goods = new ArrayList<AddOrderOriginalGoodsDTO>();
        AddOrderOriginalGoodsDTO addOrderGoodsDTO1 = new AddOrderOriginalGoodsDTO();
        Long tradeId = DomainUtils.generateId();
        addOrderGoodsDTO1.setTradeId(tradeId);
        addOrderGoodsDTO1.setSubTradeId(DomainUtils.generateId());
        addOrderGoodsDTO1.setGoodsName("苹果");
        addOrderGoodsDTO1.setSellPrice(new BigDecimal(10));
        addOrderGoodsDTO1.setSendTime(LocalDateTime.now());
        addOrderGoodsDTO1.setGoodsMemo("新鲜苹果");
        addOrderGoodsDTO1.setSysGoodsId(12346L);
        addOrderGoodsDTO1.setSysSkuId(12346L);
        goods.add(addOrderGoodsDTO1);

        AddOrderOriginalGoodsDTO addOrderGoodsDTO2 = new AddOrderOriginalGoodsDTO();
        addOrderGoodsDTO2.setSubTradeId(DomainUtils.generateId());
        addOrderGoodsDTO2.setTradeId(tradeId);
        addOrderGoodsDTO2.setGoodsName("菠萝");
        addOrderGoodsDTO2.setSellPrice(new BigDecimal(10));
        addOrderGoodsDTO2.setSendTime(LocalDateTime.now());
        addOrderGoodsDTO2.setGoodsMemo("新鲜菠萝");
        addOrderGoodsDTO2.setSysGoodsId(12346L);
        addOrderGoodsDTO2.setSysSkuId(12346L);
        goods.add(addOrderGoodsDTO2);

        AddOrderOriginalGoodsDTO addOrderGoodsDTO3 = new AddOrderOriginalGoodsDTO();
        addOrderGoodsDTO3.setSubTradeId(DomainUtils.generateId());
        addOrderGoodsDTO3.setTradeId(tradeId);
        addOrderGoodsDTO3.setGoodsName("菠萝3");
        addOrderGoodsDTO3.setSellPrice(new BigDecimal(10));
        addOrderGoodsDTO3.setSendTime(LocalDateTime.now());
        addOrderGoodsDTO3.setGoodsMemo("新鲜菠萝");
        addOrderGoodsDTO3.setSysGoodsId(12346L);
        addOrderGoodsDTO3.setSysSkuId(12346L);
        goods.add(addOrderGoodsDTO3);

        // 订单扩展信息
        AddOrderOriginalExtraDTO originalOrderExtra = new AddOrderOriginalExtraDTO();
        originalOrderExtra.setTradeId(tradeId);
        originalOrderExtra.setDistributeNo("113456789");

        // 订单信息
        AddOrderOriginalDTO originalOrder = new AddOrderOriginalDTO();
        originalOrder.setTradeId(tradeId);
        originalOrder.setTradeNo("123456789");
        originalOrder.setShopId(123456789L);
        originalOrder.setShopName("哈哈店铺");
        originalOrder.setTradeStatus(Business_OrderStatusEnum.JH_01);
        originalOrder.setCurStatus(OriginalOrderStatusEnum.READY);
        originalOrder.setSynStatus(OriginalOrderSendStatusEnum.NOT_SHIP);
        originalOrder.setCreateTime(LocalDateTime.now());

        addOriginalOrderInfo.setOriginalOrder(originalOrder);
        addOriginalOrderInfo.setOriginalOrderExtra(originalOrderExtra);
        addOriginalOrderInfo.setGoods(goods);

        lstOriginalOrder.add(addOriginalOrderInfo);

        command.setLstOriginalOrder(lstOriginalOrder);

        orderOriginalService.batchAdd(command);
        System.out.println("添加成功");

    }

    // endregion

    // region 订单下载

    /**
     * 订单下载
     */
    @Test
    public void orderDownloadTest() throws Exception {
        init();
        DomainUtils.bindMemberUser(memName);
        String jsonStr = "{\"bizData\":{\"apiPlat\":\"1\",\"businessMemberName\":\"jackyun_dev\",\"businessShopID\":773695535305202816,\"firstToMQTime\":null,\"isHasNextPage\":false,\"isWriteBigContent\":true,\"isWriteBusinessLog\":true,\"lastNotifyTime\":\"\",\"numNotifyTimes\":1,\"workerNO\":668106516540457600,\"numTotalOrder\":1,\"orders\":[{\"buyerPayment\":0,\"isDecryptError\":false,\"loadTime\":null,\"platOrderNo\":\"CN-***********-01\",\"preSaleStatus\":\"\",\"qQ\":\"\",\"salechannelId\":628703021935369344,\"shopType\":null,\"tradeStatus\":\"JH_02\",\"tradeStatusDescription\":\"\",\"tradeTime\":\"2019-08-18 19:29:45\",\"modifyTime\":\"1900-01-01 00:00:00\",\"collageTime\":\"\",\"nick\":\"许蓓呈\",\"userName\":\"\",\"buyerMobile\":\"\",\"receiverName\":\"许蓓呈\",\"country\":\"\",\"province\":\"浙江省\",\"city\":\"湖州市\",\"area\":\"德清县\",\"town\":\"\",\"address\":\"武康镇凯旋路58号\",\"payOrderNo\":\"1091819330167560816\",\"payType\":\"JH_Other\",\"shouldPayType\":\"\",\"zip\":\"\",\"phone\":\"***********\",\"mobile\":\"***********\",\"email\":\"\",\"customerRemark\":\"\",\"sellerRemark\":\"\",\"postFee\":12.00,\"goodsFee\":136.38,\"totalAmount\":148.37,\"calcTotalMoney\":0,\"realPayMoney\":134.38,\"favourableMoney\":0,\"platDiscountMoney\":0.01,\"taxAmount\":12.38,\"tariffAmount\":0,\"addedValueAmount\":0,\"consumptionDutyAmount\":0,\"commissionValue\":0,\"payTime\":\"1900-01-01 00:00:00\",\"sendType\":\"JH_Other\",\"sendStyle\":\"\",\"codServiceFee\":0,\"sellerFlag\":\"JH_None\",\"cardType\":\"JH_01\",\"idCard\":\"330521200005311513\",\"idCardTrueName\":\"许蓓呈\",\"idCardImgs\":\"\",\"whseCode\":\"保税仓（杭州下沙）\",\"hwgFlag\":false,\"isHwgFlag\":false,\"deliveryType\":\"\",\"shopId\":\"\",\"isNeedInvoice\":false,\"needInvoice\":false,\"invoiceType\":\"JH_NONE\",\"invoiceTitle\":\"\",\"invoiceContent\":\"\",\"businessType\":\"\",\"taxPayerIdent\":\"\",\"registeredAddress\":\"\",\"registeredPhone\":\"\",\"depositBank\":\"\",\"bankAccount\":\"\",\"fetchTime\":\"1900-01-01 00:00:00\",\"fetchTimeDesc\":\"\",\"orderSource\":\"\",\"customAttr\":\"\",\"transportDay\":\"2019-08-19 11:17:41\",\"goodInfos\":[{\"pictureURL\":\"\",\"productId\":\"*************\",\"subOrderNo\":\"\",\"tradeGoodsNo\":\"*************\",\"tradeGoodsName\":\"【澳大利亚】HEALTHY CARE 鱼油 1000MG 400粒\",\"tradeGoodsSpec\":\"1件装\",\"goodsCount\":2,\"price\":68.19,\"refundCount\":0,\"discountMoney\":0,\"taxAmount\":11.28,\"tariffAmount\":0,\"addedValueAmount\":0,\"consumptionDutyAmount\":0,\"refundStatus\":\"JH_07\",\"status\":\"JH_99\",\"remark\":\"\",\"outId\":\"\",\"outSkuId\":\"\",\"platGoodsId\":\"*************\",\"platSkuId\":\"*************\",\"subGoods\":[],\"isGift\":false,\"isHwgFlag\":false,\"deliveryType\":\"\",\"payOrderId\":\"\",\"packageOrderId\":\"\",\"activityAmount\":0,\"specialAmount\":0,\"couponAmount\":0,\"productItemId\":\"\",\"outItemId\":\"*************\",\"goodsCount2\":0,\"isPlatStorageOrder\":false,\"pictureUrl\":\"\",\"partMjzDiscount\":0,\"storeCode\":\"\",\"isDaiXiaoGoods\":false,\"goodsOrderAttr\":\"\",\"goodsDistributorPayment\":0,\"goodsBuyerPayment\":0,\"goodsRefundFee\":0}],\"couponDetails\":[],\"jackYunModified\":\"2019-08-21T15:45:11.099\",\"userLevel\":\"\",\"reminderCount\":\"\",\"verifyCode\":\"\",\"sellerOrderId\":\"\",\"qq\":\"\",\"orderFlag\":\"\",\"tradeAttr\":\"\",\"orderType\":\"\",\"isStoreOrder\":false,\"storeOrder\":false,\"isYunStoreOrder\":false,\"yunStoreOrder\":false,\"sendDate\":\"\",\"leftSendDate\":\"1900-01-01 00:00:00\",\"sortingCode\":\"\",\"partner\":\"\",\"fDeliGoodGlag\":\"\",\"ckyName\":\"\",\"createDate\":\"\",\"shouldPayMoney\":0,\"resellerId\":\"\",\"resellerShopName\":\"\",\"resellerMobile\":\"\",\"activityAmount\":0,\"specialAmount\":0,\"couponAmount\":\"0\",\"currencyCode\":\"\",\"isPreSaleOrder\":false,\"preSaleOrder\":false,\"shipTypeName\":\"\",\"bondedId\":\"\",\"bondedName\":\"\",\"logisticNo\":\"YT2006633225000\",\"logisticName\":\"圆通快递\",\"payMethod\":\"\",\"fxtId\":\"\",\"clerkName\":\"\",\"clerkPhone\":\"\",\"transactionId\":\"\",\"daiXiao\":false,\"isDaiXiao\":false,\"brandSale\":false,\"isBrandSale\":false,\"forceWlb\":false,\"isForceWlb\":false,\"isWlbOrder\":false,\"wlbOrder\":false,\"isShip\":false,\"ship\":false,\"encrypt\":false,\"isEncrypt\":false,\"fulfillmentChannel\":\"\",\"shipmentServiceLevelCategory\":\"\",\"isShippedByAmazonTFM\":false,\"shippedByAmazonTFM\":false,\"isJZOrder\":false,\"jZOrder\":false,\"tradeAttrJson\":\"\",\"endTime\":\"\",\"productNum\":0,\"serviceOrders\":[],\"logisticsInfos\":[]}],\"nextToken\":\"\",\"downloadOrderType\":\"1\"},\"memName\":\"jackyun_dev\"}";
        OrderOriginalProcessCommand bizData = JsonUtils.deJson(jsonStr, OrderOriginalProcessCommand.class);
        orderOriginalService.orderProcess(bizData);
    }

    // endregion

    // region 获取店铺缓存

    /**
     * 获取店铺缓存
     */
    @Test
    public void getshopConfCacheTest() throws Exception {
        JsonDeserializerRegister.get().register();
        ShopConfigEntity entityShop = ShopConfDataProxy.get().getData(memName, 610916143429590144L);
        System.out.println(JSON.toJSONString(entityShop));
    }

    // endregion

    // region 递交任务

    @Test
    public void postOrderTest() throws Exception {
        init();
        PostOrderToOMSMQData bizData = new PostOrderToOMSMQData();
        bizData.setIsForcePost(true);
        List<Long> tradeIds = new ArrayList<Long>();
        tradeIds.add(782395178022449280L);
        bizData.setTradeIdsToOMS(tradeIds);
        bizData.setTradeIds(tradeIds);
        bizData.setIsUseOnlineDB(true);
        bizData.setShopID(683796233854917760L);
        bizData.setMemberName(memName);
        DomainUtils.bindMemberUser(memName);
        orderOriginalService.postOrder(new PostOrderCommand(bizData));
    }

    // endregion

    // region 网店订单创建接口测试

    @Test
    public void createOrderToOmsOnlineTest() {
        init();
        DomainUtils.bindMemberUser(memName);
        String jsonStr = "{\"ishasnextpage\":\"0\",\"numtotalorder\":\"1\",\"orders\":[{\"IsDecryptError\":0,\"activityAmount\":0,\"addedvalueAmount\":0,\"address\":\"辽宁大连市中山区港湾壹号C座2102室-丹东街42\",\"area\":\"中山区\",\"bankAccount\":\"\",\"bfdeligoodglag\":\"否\",\"bondedId\":\"\",\"bondedName\":\"\",\"cardType\":\"JH_01\",\"city\":\"大连市\",\"ckyname\":\"\",\"clerkName\":\"\",\"clerkPhone\":\"\",\"codserviceFee\":0,\"collageTime\":\"\",\"commissionvalue\":0,\"consumptionDutyAmount\":0,\"country\":\"\",\"couponAmount\":0,\"coupondetails\":[{\"couponNum\":0,\"couponType\":\"JH_DiscountCoupon\",\"price\":17.00,\"skuId\":\"***********\",\"type\":\"30-单品促销优惠\"},{\"couponNum\":0,\"couponType\":\"JH_ShopCoupon\",\"price\":5.00,\"skuId\":\"\",\"type\":\"100-店铺优惠\"}],\"createDate\":\"2019-04-26 12:37:31\",\"currencyCode\":\"\",\"customerId\":\"zhang_12\",\"customerRemark\":\"\",\"customerTrueName\":\"晓华\",\"deliveryType\":\"\",\"depositBank\":\"\",\"email\":\"\",\"favourableMoney\":22.00,\"fetchTime\":\"0001-01-01 00:00:00\",\"fulfillmentChannel\":\"\",\"fxtId\":\"\",\"goodinfos\":[{\"activityAmount\":0,\"addedValueAmount\":0,\"consumptiondutyAmount\":0,\"couponAmount\":0,\"deliveryType\":\"\",\"discountMoney\":0,\"goodsBarcode\":\"\",\"goodsBuyerPayment\":0.0,\"goodsCount\":1,\"goodsCount2\":0,\"ishwgflag\":\"0\",\"isplatstorageorder\":\"0\",\"outitemid\":\"411043\",\"outskuid\":\"411043\",\"packageOrderId\":\"\",\"payOrderId\":\"\",\"platGoodsId\":\"***********\",\"platSkuId\":\"***********\",\"price\":69.50,\"productId\":\"***********\",\"productItemId\":\"\",\"refundStatus\":\"JH_99\",\"remark\":\"\",\"specialAmount\":0,\"status\":\"JH_99\",\"suborderNo\":\"***********\",\"sysGoodsId\":0,\"sysSpecId\":0,\"tariffAmount\":0,\"taxAmount\":0,\"tradeGoodsName\":\"【粤供优品】十里花香头道 鲜榨花生油 非转基因食用油 900ML \",\"tradeGoodsNo\":\"411043\",\"tradeGoodsSpec\":\"900ML\"}],\"goodsFee\":69.50,\"idCard\":\"\",\"idCardImgs\":\"\",\"idCardTrueName\":\"\",\"invoiceContent\":\"明细\",\"invoiceTitle\":\"个人\",\"invoiceType\":\"普通发票\",\"isbrandsale\":0,\"isdaixiao\":0,\"isencrypt\":0,\"isforcewlb\":0,\"ishwgflag\":\"0\",\"isjzorder\":0,\"ispresaleorder\":0,\"isship\":0,\"isshippedbyamazontfm\":\"0\",\"isstoreorder\":0,\"leftSendDate\":\"0001-01-01 00:00:00\",\"logisticName\":\"\",\"logisticNo\":\"3529490195945\",\"mobile\":\"13904085937\",\"nick\":\"zhang_12\",\"orderFlag\":\"\",\"orderSource\":\"\",\"orderType\":\"22\",\"partner\":\"*大连市-大连华乐营业部\",\"payMethod\":\"\",\"payTime\":\"2019-04-26 12:37:43\",\"payType\":\"JH_Online\",\"payorderNo\":\"94330178724\",\"phone\":\"13904085937\",\"platDiscountMoney\":0,\"platorderNo\":\"20190627001\",\"postFee\":6.00,\"province\":\"辽宁\",\"qq\":\"\",\"realPayMoney\":53.50,\"receiverName\":\"晓华\",\"registeredAddress\":\"\",\"registeredPhone\":\"\",\"resellerId\":\"\",\"resellerMobile\":\"\",\"resellerShopName\":\"\",\"salechannelId\":610916143429590144,\"sellerFlag\":\"JH_Gray\",\"sellerRemark\":\"\",\"sendDate\":\"任意时间\",\"sendStyle\":\"4-在线支付\",\"sendType\":\"JH_ExpressSend\",\"shipTypeName\":\"快递\",\"shipmentServiceLevelCategory\":\"\",\"salechannelId\":\"610916143429590144\",\"shopType\":\"SOP\",\"shouldPayMoney\":53.50,\"shouldPayType\":\"4-在线支付\",\"sortingCode\":\"\",\"specialAmount\":0,\"tariffAmount\":0,\"taxAmount\":0,\"taxPayerIdent\":\"\",\"totalAmount\":53.50,\"town\":\"\",\"tradeAttrJson\":\"\",\"tradeStatus\":\"JH_04\",\"tradeStatusDescription\":\"FINISHED_L\",\"tradeTime\":\"2019-04-26 12:37:31\",\"transactionId\":\"\",\"userName\":\"zhang_12\",\"whseCode\":\"0\",\"zip\":\"\"}],\"nexttoken\":\"\",\"code\":\"10000\",\"msg\":\"SUCCESS\",\"subcode\":\"\",\"submessage\":\"\",\"polyapitotalms\":\"359\",\"polyapirequestid\":\"071733452616\",\"businessShopID\":773695535305202816,\"businessMemberName\":\"jackyun_dev\",\"apiPlat\":1}";
        PolyAPIBusinessGetOrderResponseBizData data = JsonUtils.deJson(jsonStr, PolyAPIBusinessGetOrderResponseBizData.class);
        OpenOmsOnlineOrderDto onlineOrderDto = new OpenOmsOnlineOrderDto();
        onlineOrderDto.setPlatId(data.getApiPlat().getPlatValue());
        onlineOrderDto.setOrders(data.getOrders());
        //将boolean型变量转化为需要的0或1(暂时先这样处理)  
        Map<String, Object> map = new HashMap<>(1);
        map.put("memName", data.getBusinessMemberName());
        map.put("jsonStr", JsonUtils.toOMSOnlineJson(onlineOrderDto));
        System.out.println("网店订单创建接口入参：" + JsonUtils.toOMSOnlineJson(onlineOrderDto));
        DomainUtils.bindMemberUser(data.getBusinessMemberName());
        JackYunResponse<?> response = UserGroupFeignClient.doPost(FeignGateway.OMSONLINE_OPEN_SERVICE_NAME, FeignGateway.OMSONLINE_OPEN__ORDER_SYNC_CREATE, map, Object.class);
        //如果失败则抛出异常。
        if (response.getCode() != 200) {
            throw new RuntimeException(response.getMsg());
        }
        System.out.println("网店订单创建接口测试:" + JsonUtils.toJson(response));
    }

    // endregion

    // region 网店订单创建接口测试

    @Test
    public void createOrderToOmsApiTest() {
        init();
        DomainUtils.bindMemberUser(memName);
        String jsonStr = "";
        OrderOriginalCreateEventRequestBizData bizData = JsonUtils.deJson(jsonStr, OrderOriginalCreateEventRequestBizData.class);
        Map<String, Object> map = new HashMap<>(1);
        map.put("memberName", bizData.getMemName());
        map.put("method", OMSAPIOpenAPITypeEnum.BUSINESS_ORDERORIGINALCREATE.getMethod());
        map.put("bizContent", JsonUtils.toJson(bizData));
        DomainUtils.bindMemberUser(bizData.getMemName());
        System.err.println("网店订单创建接口测试:" + JsonUtils.toJson(bizData));
        JackYunResponse<?> response = UserGroupFeignClient.doPost(SiteTypeEnum.BUSINESS.getServiceCode(), "/omsapi-business/open/call", map, Object.class);
        //记录日志信息
        LogUtils.write(LogTypeEnum.TRACE, String.format("推送订单给OMS-Business失败,请求参数：%s,返回：%s", JsonUtils.toJson(bizData), JsonUtils.toJson(response)));
        //如果失败则抛出异常。
        if (response.getCode() != 200) {
            throw new RuntimeException("推送订单给OMS-Business失败：" + response.getMsg());
        }
        System.out.println("网店订单创建接口测试:" + JsonUtils.toJson(response));
    }

    // endregion

    // region 网店订单创建接口测试

    @Test
    public void createOrderToOmsApiTestSimple() throws Exception {
        init();
        DomainUtils.bindMemberUser(memName);
        String jsonStr = "{\"ishasnextpage\":\"0\",\"numtotalorder\":\"1\",\"orders\":[{\"buyerPayment\":0,\"isDecryptError\":\"0\",\"loadTime\":null,\"platOrderNo\":\"************\",\"preSaleStatus\":\"\",\"qQ\":\"\",\"salechannelId\":777993558747257989,\"shopType\":null,\"tradeStatus\":\"JH_01\",\"tradeStatusDescription\":\"\",\"tradeTime\":\"2019-05-20 07:28:45\",\"modifyTime\":\"0001-01-01 00:00:00\",\"collageTime\":\"\",\"nick\":\"\",\"userName\":\"\",\"buyerMobile\":\"\",\"receiverName\":\"胡灿灿\",\"country\":\"\",\"province\":\"安徽省\",\"city\":\"合肥市\",\"area\":\"蜀山区\",\"town\":\"\",\"address\":\"安徽省合肥市蜀山区政务区龙图路岸上玫瑰北区NC2-807\",\"payOrderNo\":\"\",\"payType\":\"JH_Other\",\"shouldPayType\":\"\",\"zip\":\"\",\"phone\":\"\",\"mobile\":\"***********\",\"email\":\"\",\"customerRemark\":\"\",\"sellerRemark\":\"\",\"postFee\":0,\"goodsFee\":29.9,\"totalAmount\":29.9,\"calcTotalMoney\":29.9,\"realPayMoney\":29.9,\"favourableMoney\":0,\"platDiscountMoney\":0,\"taxAmount\":0,\"tariffAmount\":0,\"addedValueAmount\":0,\"consumptionDutyAmount\":0,\"commissionValue\":0,\"payTime\":\"1970-01-01 08:00:00\",\"sendType\":\"JH_Other\",\"sendStyle\":\"\",\"codServiceFee\":0,\"sellerFlag\":\"JH_None\",\"cardType\":\"JH_01\",\"idCard\":\"\",\"idCardTrueName\":\"\",\"idCardImgs\":\"\",\"whseCode\":\"\",\"hwgFlag\":\"0\",\"isHwgFlag\":\"0\",\"deliveryType\":\"\",\"shopId\":\"777993558747257989\",\"isNeedInvoice\":\"0\",\"needInvoice\":\"0\",\"invoiceType\":\"\",\"invoiceTitle\":\"\",\"invoiceContent\":\"\",\"businessType\":\"\",\"taxPayerIdent\":\"\",\"registeredAddress\":\"\",\"registeredPhone\":\"\",\"depositBank\":\"\",\"bankAccount\":\"\",\"fetchTime\":\"0001-01-01 00:00:00\",\"fetchTimeDesc\":\"\",\"orderSource\":\"\",\"customAttr\":\"\",\"transportDay\":\"\",\"goodInfos\":[{\"pictureURL\":\"\",\"productId\":\"1572044\",\"subOrderNo\":\"\",\"tradeGoodsNo\":\"*********\",\"tradeGoodsName\":\"Cipango 【2件/49.9元】婴幼儿衣服夏季新款可爱三角连体哈衣 新生宝宝外出爬爬服\",\"tradeGoodsSpec\":\"\",\"goodsCount\":1,\"price\":29.9,\"refundCount\":0,\"discountMoney\":0,\"taxAmount\":0,\"tariffAmount\":0,\"addedValueAmount\":0,\"consumptionDutyAmount\":0,\"refundStatus\":\"JH_07\",\"status\":\"JH_99\",\"remark\":\"\",\"outId\":\"\",\"outSkuId\":\"********\",\"platGoodsId\":\"\",\"platSkuId\":\"1572044\",\"subGoods\":[],\"isGift\":\"0\",\"isHwgFlag\":\"0\",\"deliveryType\":\"\",\"payOrderId\":\"\",\"packageOrderId\":\"\",\"activityAmount\":0,\"specialAmount\":0,\"couponAmount\":0,\"productItemId\":\"\",\"outItemId\":\"\",\"goodsCount2\":0,\"isPlatStorageOrder\":\"0\",\"pictureUrl\":\"\",\"partMjzDiscount\":0,\"storeCode\":\"\",\"isDaiXiaoGoods\":\"0\",\"goodsOrderAttr\":\"\",\"goodsDistributorPayment\":0,\"goodsBuyerPayment\":0,\"goodsRefundFee\":0}],\"couponDetails\":[],\"userLevel\":\"\",\"reminderCount\":\"\",\"verifyCode\":\"\",\"sellerOrderId\":\"\",\"qq\":\"\",\"orderFlag\":\"\",\"tradeAttr\":\"\",\"orderType\":\"\",\"isStoreOrder\":\"0\",\"storeOrder\":\"0\",\"isYunStoreOrder\":\"0\",\"yunStoreOrder\":\"0\",\"sendDate\":\"\",\"leftSendDate\":\"0001-01-01 00:00:00\",\"sortingCode\":\"\",\"partner\":\"\",\"fDeliGoodGlag\":\"\",\"ckyName\":\"\",\"createDate\":\"\",\"shouldPayMoney\":0,\"resellerId\":\"\",\"resellerShopName\":\"\",\"resellerMobile\":\"\",\"activityAmount\":0,\"specialAmount\":0,\"couponAmount\":\"0\",\"currencyCode\":\"\",\"isPreSaleOrder\":\"0\",\"preSaleOrder\":\"0\",\"shipTypeName\":\"\",\"bondedId\":\"\",\"bondedName\":\"\",\"logisticNo\":\"\",\"logisticName\":\"\",\"payMethod\":\"\",\"fxtId\":\"\",\"clerkName\":\"\",\"clerkPhone\":\"\",\"transactionId\":\"\",\"daiXiao\":\"0\",\"isDaiXiao\":\"0\",\"brandSale\":\"0\",\"isBrandSale\":\"0\",\"forceWlb\":\"0\",\"isForceWlb\":\"0\",\"isWlbOrder\":\"0\",\"wlbOrder\":\"0\",\"isShip\":\"0\",\"ship\":\"0\",\"encrypt\":\"0\",\"isEncrypt\":\"0\",\"fulfillmentChannel\":\"\",\"shipmentServiceLevelCategory\":\"\",\"isShippedByAmazonTFM\":\"0\",\"shippedByAmazonTFM\":\"0\",\"isJZOrder\":\"0\",\"jZOrder\":\"0\",\"tradeAttrJson\":\"\",\"endTime\":\"\",\"productNum\":0,\"serviceOrders\":[],\"logisticsInfos\":[]},{\"buyerPayment\":0,\"isDecryptError\":\"0\",\"loadTime\":null,\"platOrderNo\":\"10448123648750\",\"preSaleStatus\":\"\",\"qQ\":\"\",\"salechannelId\":626581902120125952,\"shopType\":null,\"tradeStatus\":\"JH_01\",\"tradeStatusDescription\":\"\",\"tradeTime\":\"2019-05-20 07:16:29\",\"modifyTime\":\"0001-01-01 00:00:00\",\"collageTime\":\"\",\"nick\":\"\",\"userName\":\"\",\"buyerMobile\":\"\",\"receiverName\":\"谢锦华\",\"country\":\"\",\"province\":\"江苏省\",\"city\":\"扬州市\",\"area\":\"邗江区\",\"town\":\"\",\"address\":\"江苏省扬州市邗江区公道镇明富鞋厂\",\"payOrderNo\":\"\",\"payType\":\"JH_Other\",\"shouldPayType\":\"\",\"zip\":\"\",\"phone\":\"\",\"mobile\":\"17306299061\",\"email\":\"\",\"customerRemark\":\"\",\"sellerRemark\":\"\",\"postFee\":0,\"goodsFee\":23.8,\"totalAmount\":23.8,\"calcTotalMoney\":23.8,\"realPayMoney\":23.8,\"favourableMoney\":0,\"platDiscountMoney\":0,\"taxAmount\":0,\"tariffAmount\":0,\"addedValueAmount\":0,\"consumptionDutyAmount\":0,\"commissionValue\":0,\"payTime\":\"1970-01-01 08:00:00\",\"sendType\":\"JH_Other\",\"sendStyle\":\"\",\"codServiceFee\":0,\"sellerFlag\":\"JH_None\",\"cardType\":\"JH_01\",\"idCard\":\"\",\"idCardTrueName\":\"\",\"idCardImgs\":\"\",\"whseCode\":\"\",\"hwgFlag\":\"0\",\"isHwgFlag\":\"0\",\"deliveryType\":\"\",\"shopId\":\"\",\"isNeedInvoice\":\"0\",\"needInvoice\":\"0\",\"invoiceType\":\"\",\"invoiceTitle\":\"\",\"invoiceContent\":\"\",\"businessType\":\"\",\"taxPayerIdent\":\"\",\"registeredAddress\":\"\",\"registeredPhone\":\"\",\"depositBank\":\"\",\"bankAccount\":\"\",\"fetchTime\":\"0001-01-01 00:00:00\",\"fetchTimeDesc\":\"\",\"orderSource\":\"\",\"customAttr\":\"\",\"transportDay\":\"\",\"goodInfos\":[{\"pictureURL\":\"\",\"productId\":\"1572043\",\"subOrderNo\":\"\",\"tradeGoodsNo\":\"*********\",\"tradeGoodsName\":\"Cipango 【2件/49.9元】婴幼儿衣服夏季新款可爱三角连体哈衣 新生宝宝外出爬爬服\",\"tradeGoodsSpec\":\"\",\"goodsCount\":1,\"price\":23.8,\"refundCount\":0,\"discountMoney\":0,\"taxAmount\":0,\"tariffAmount\":0,\"addedValueAmount\":0,\"consumptionDutyAmount\":0,\"refundStatus\":\"JH_07\",\"status\":\"JH_99\",\"remark\":\"\",\"outId\":\"\",\"outSkuId\":\"********\",\"platGoodsId\":\"\",\"platSkuId\":\"1572043\",\"subGoods\":[],\"isGift\":\"0\",\"isHwgFlag\":\"0\",\"deliveryType\":\"\",\"payOrderId\":\"\",\"packageOrderId\":\"\",\"activityAmount\":0,\"specialAmount\":0,\"couponAmount\":0,\"productItemId\":\"\",\"outItemId\":\"\",\"goodsCount2\":0,\"isPlatStorageOrder\":\"0\",\"pictureUrl\":\"\",\"partMjzDiscount\":0,\"storeCode\":\"\",\"isDaiXiaoGoods\":\"0\",\"goodsOrderAttr\":\"\",\"goodsDistributorPayment\":0,\"goodsBuyerPayment\":0,\"goodsRefundFee\":0}],\"couponDetails\":[],\"userLevel\":\"\",\"reminderCount\":\"\",\"verifyCode\":\"\",\"sellerOrderId\":\"\",\"qq\":\"\",\"orderFlag\":\"\",\"tradeAttr\":\"\",\"orderType\":\"\",\"isStoreOrder\":\"0\",\"storeOrder\":\"0\",\"isYunStoreOrder\":\"0\",\"yunStoreOrder\":\"0\",\"sendDate\":\"\",\"leftSendDate\":\"0001-01-01 00:00:00\",\"sortingCode\":\"\",\"partner\":\"\",\"fDeliGoodGlag\":\"\",\"ckyName\":\"\",\"createDate\":\"\",\"shouldPayMoney\":0,\"resellerId\":\"\",\"resellerShopName\":\"\",\"resellerMobile\":\"\",\"activityAmount\":0,\"specialAmount\":0,\"couponAmount\":\"0\",\"currencyCode\":\"\",\"isPreSaleOrder\":\"0\",\"preSaleOrder\":\"0\",\"shipTypeName\":\"\",\"bondedId\":\"\",\"bondedName\":\"\",\"logisticNo\":\"\",\"logisticName\":\"\",\"payMethod\":\"\",\"fxtId\":\"\",\"clerkName\":\"\",\"clerkPhone\":\"\",\"transactionId\":\"\",\"daiXiao\":\"0\",\"isDaiXiao\":\"0\",\"brandSale\":\"0\",\"isBrandSale\":\"0\",\"forceWlb\":\"0\",\"isForceWlb\":\"0\",\"isWlbOrder\":\"0\",\"wlbOrder\":\"0\",\"isShip\":\"0\",\"ship\":\"0\",\"encrypt\":\"0\",\"isEncrypt\":\"0\",\"fulfillmentChannel\":\"\",\"shipmentServiceLevelCategory\":\"\",\"isShippedByAmazonTFM\":\"0\",\"shippedByAmazonTFM\":\"0\",\"isJZOrder\":\"0\",\"jZOrder\":\"0\",\"tradeAttrJson\":\"\",\"endTime\":\"\",\"productNum\":0,\"serviceOrders\":[],\"logisticsInfos\":[]}],\"nexttoken\":\"\",\"code\":\"10000\",\"msg\":\"SUCCESS\",\"subcode\":\"\",\"submessage\":\"\",\"polyapitotalms\":\"359\",\"polyapirequestid\":\"071733452616\",\"businessShopID\":777993558747257989,\"businessMemberName\":\"jackyun_dev\",\"apiPlat\":82}";
        PolyAPIBusinessGetOrderResponseBizData data = JsonUtils.deJson(jsonStr, PolyAPIBusinessGetOrderResponseBizData.class);
        this.orderOriginalService.orderProcess(new OrderOriginalProcessCommand(data.getBusinessMemberName(), data));
    }

    // endregion

    // region 店铺同步测试

    @Test
    public void synShopConfTest() throws Exception {
        init();
        DomainUtils.bindMemberUser(memName);
        String jsonStr = "{\"authRemainTime\":\"2020-07-23T00:00:00\",\"authSellNickname\":\"differsoft88\",\"authSessionkey\":\"62015081a55a80eegi0948cb6b4cc12774e4109e3ad5623397870843\",\"authStatus\":0,\"isAutoDownload\":true,\"memName\":\"jackyun_dev\",\"polyToken\":\"18300e2695da453b94f5fabf6601f671\",\"refundConf\":\"{\\\"isAutoDownload\\\":0,\\\"isAutoPost\\\":0,\\\"isAutoSync\\\":0,\\\"trigger\\\":[]}\",\"shopConf\":\"{\\\"goodsStockSync\\\":{\\\"isSyncStockDailyFactory\\\":\\\"0\\\",\\\"safeQuantity\\\":0,\\\"quantityRule\\\":[\\\"ACTIVE\\\"],\\\"isAllowDelisting\\\":\\\"0\\\",\\\"isAllowAutoShelves\\\":\\\"0\\\",\\\"syncStockType\\\":0,\\\"emergencyQuantity\\\":0,\\\"isIntegerUp\\\":\\\"0\\\",\\\"isAutoSync\\\":\\\"1\\\",\\\"percent\\\":100,\\\"goodsRule\\\":[],\\\"availableWarehouse\\\":[\\\"515651372352373120\\\"]},\\\"salechannelId\\\":\\\"515655025751818880\\\",\\\"memberName\\\":\\\"\\\",\\\"shopName\\\":\\\"笛佛淘宝店\\\",\\\"orderPost\\\":{\\\"onlyPostCrossBorder\\\":\\\"0\\\",\\\"isAutoMergeAddress\\\":0,\\\"isPostInvalid\\\":\\\"1\\\",\\\"isSupplyDelivery\\\":\\\"0\\\",\\\"notPostForRefund\\\":\\\"0\\\",\\\"autoPostOption\\\":{\\\"autoPostInterval\\\":0,\\\"timeSpan\\\":[{\\\"autoPostBegin\\\":\\\"00:00\\\",\\\"autoPostEnd\\\":\\\"23:59\\\"}],\\\"isAutoPost\\\":\\\"1\\\"}},\\\"orderRefundExtra\\\":{\\\"isAutoAuditRefundOrder\\\":\\\"0\\\",\\\"isAutoDownLoadRefundOrder\\\":\\\"0\\\",\\\"trigger\\\":[],\\\"isAutoSync\\\":\\\"0\\\"},\\\"deliverySync\\\":{\\\"trackDetailDelay\\\":\\\"24\\\",\\\"trigger\\\":[\\\"DELIVERY_COMPLETE\\\"],\\\"isAutoSync\\\":\\\"1\\\",\\\"statusDetect\\\":{\\\"isDetectRefund\\\":\\\"1\\\"},\\\"hasJzOrderFlag\\\":\\\"0\\\",\\\"splitToWholeFlag\\\":\\\"0\\\",\\\"warehouseSettingSwitch\\\":\\\"0\\\"},\\\"shopType\\\":1,\\\"tradeType\\\":[\\\"NORMAL\\\"],\\\"orderDownload\\\":{\\\"autoDownloadOption\\\":{\\\"autoDownloadTimeInterval\\\":0,\\\"timeSpan\\\":[{\\\"autoDownloadBegin\\\":\\\"00:00\\\",\\\"autoDownloadEnd\\\":\\\"23:59\\\"}],\\\"isAutoDownload\\\":\\\"1\\\"},\\\"dontDownTitleOrOuterIdGoodsSwitch\\\":\\\"0\\\",\\\"platCouponSwitch\\\":\\\"0\\\",\\\"shopCouponSwitch\\\":\\\"0\\\",\\\"ignoreBarcodeSwitch\\\":\\\"0\\\",\\\"isTransformSimple\\\":\\\"0\\\",\\\"ignoreBarcode\\\":\\\"\\\",\\\"flagExclude\\\":[],\\\"onlyDownTitleOrOuterIdGoodsSwitch\\\":\\\"0\\\",\\\"downloadMarkOption\\\":{\\\"isWriteSellerMemo\\\":\\\"0\\\",\\\"reWriteRange\\\":1}}}\",\"shopId\":515655025751818880,\"shopName\":\"笛佛淘宝店\",\"shopType\":1}";
        AddOrUpdateShopConfCommand command = JsonUtils.deJson(jsonStr, AddOrUpdateShopConfCommand.class);
        shopConfService.addOrderupdateShopConf(command);
        System.out.println("店铺配置同步成功");
    }

    // endregion

    // region 店铺同步测试

    @Test
    public void synNewShopConfTest() throws Exception {
        init();
        DomainUtils.bindMemberUser(memName);
        String jsonStr = "{\"authSessionkey\":\"\",\"authStatus\":0,\"isAutoDownload\":true,\"memName\":\"101068\",\"polyToken\":\"ed2a6a8f64dc42c98959420bea41229d\",\"refundConf\":\"{\\\"trigger\\\":[]}\",\"selfUseAuth\":\"{\\\"items\\\":[{\\\"ckey\\\":\\\"tpId\\\",\\\"ekey\\\":\\\"tpId\\\",\\\"value\\\":\\\"923dd079554e2b18\\\"},{\\\"ckey\\\":\\\"调接口的公钥\\\",\\\"ekey\\\":\\\"publicKey\\\",\\\"value\\\":\\\"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCVGTFE6w3GzXBFVLT8kHUsh/0+Zxzo4ZbDlK8qHc4aTh/fnNpNe/Nsg07+HX0ll7WB7dwQAke6OTTUl7rHmgfnSOnzyBMCM4Y1ZjHZiAzleFiXwG1XwFWq88CpYbLVXi9p/BFhHTiX5combCMzPLXSokGTIhHmRCjtH8rcSIDNcQIDAQAB\\\"},{\\\"ckey\\\":\\\"调接口的私钥\\\",\\\"ekey\\\":\\\"privateKey\\\",\\\"value\\\":\\\"MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJUZMUTrDcbNcEVUtPyQdSyH/T5nHOjhlsOUryodzhpOH9+c2k1782yDTv4dfSWXtYHt3BACR7o5NNSXuseaB+dI6fPIEwIzhjVmMdmIDOV4WJfAbVfAVarzwKlhstVeL2n8EWEdOJflyiZsIzM8tdKiQZMiEeZEKO0fytxIgM1xAgMBAAECgYEAhtx9qzftgyfp6FxBRL4prnjnAN3l0WPTqd7APhkthq+ptuq6ryFxwnJXLhVgUHOUEw0iYzycwn3iInWH1emmQRVJoYxuI0H0XvCfdIkUxRwad0v/lvf1AV/MWrV+fyBOZZyr6nStGDc/+hY6JDi7uZy5gvjlV+IOd3uncgJ8A50CQQDFBVGySxsMyMj4AtX69o0qRTDzyP73bOcE7HQQCk5gKKodsZsArzDDtAxqmNOM0hrbIuqLFHnPJWBQ3x+kDcXHAkEAwbtYFhWjbWA888FKn6oXOqg0Wi3S9FzbAgJDd5z+e8E0J31JT6Yav8/0KEJ/fmqTkJJMSHolCPUnaG7c4XhzBwJAAwrV/B7UyBnBoLLKgxmTjHeNDfaomUGBP742/JhsJWf7WIIJlrUd16fMGUlK2v3TlFaxsHwcdWpqVlVbs7jSuQJAZwKVeNCyoDK+1iFd633GcjMD+ztIYxSIgw3vD8fTbLwzXhf5BjfdG0aYnVuWbQKASivwEc2YqaWo+ZeRLPYduwJARyNGAQJO1PA1BUC6F1BJbzCWq/yoZCrCoTTKBi9KBidlRvUWZSRU07AD5heEIKjC6aVCh12M/DtT4NqSWF5fBA==\\\"}]}\",\"shopConf\":\"{\\\"goodsStockSync\\\":{\\\"isSyncStockDailyFactory\\\":\\\"0\\\"},\\\"channelType\\\":0,\\\"deliverySync\\\":{\\\"hasJzOrderFlag\\\":0,\\\"isAutoSync\\\":1,\\\"splitToWholeFlag\\\":0,\\\"statusDetect\\\":{\\\"isDetectRefund\\\":1},\\\"trackDetailDelay\\\":24,\\\"trigger\\\":[\\\"GET_POST\\\"],\\\"warehouseSettingSwitch\\\":0},\\\"flagMapping\\\":[],\\\"memberName\\\":\\\"\\\",\\\"orderDownload\\\":{\\\"autoDownloadOption\\\":{\\\"autoDownloadTimeInterval\\\":600,\\\"isAutoDownload\\\":1,\\\"timeSpan\\\":[{\\\"autoDownloadBegin\\\":\\\"00:00\\\",\\\"autoDownloadEnd\\\":\\\"23:59\\\"}]},\\\"customizedBarcode\\\":{\\\"extractOption\\\":[],\\\"extractTag\\\":\\\"\\\",\\\"isBarcodeCustomized\\\":0,\\\"templateName\\\":\\\"\\\"},\\\"customizedDownload\\\":{\\\"isCustomizedDownload\\\":0,\\\"rules\\\":[]},\\\"dontDownTitleOrOuterIdGoodsSwitch\\\":0,\\\"downloadMarkOption\\\":{\\\"isWriteSellerMemo\\\":0,\\\"reWriteRange\\\":1,\\\"writeRule\\\":{\\\"flag\\\":\\\"0\\\",\\\"memo\\\":\\\"\\\"}},\\\"flagExclude\\\":[],\\\"ignoreBarcode\\\":\\\"\\\",\\\"ignoreBarcodeSwitch\\\":0,\\\"isTransformSimple\\\":0,\\\"onlyDownTitleOrOuterIdGoodsSwitch\\\":0,\\\"platCouponSwitch\\\":0,\\\"shopCouponSwitch\\\":0},\\\"orderPost\\\":{\\\"autoMergeOption\\\":{\\\"allowMaxDaySpan\\\":2,\\\"autoMergeDelay\\\":600,\\\"isAutoMerge\\\":0},\\\"autoPostOption\\\":{\\\"autoPostInterval\\\":0,\\\"isAutoPost\\\":1,\\\"timeSpan\\\":[{\\\"autoPostBegin\\\":\\\"00:00\\\",\\\"autoPostEnd\\\":\\\"23:59\\\"}]},\\\"isAutoMergeAddress\\\":0,\\\"isPostInvalid\\\":0,\\\"isSupplyDelivery\\\":0,\\\"notPostForRefund\\\":0,\\\"onlyPostCrossBorder\\\":0},\\\"salechannelId\\\":736868564053197568,\\\"shopName\\\":\\\"七次方（一条电商）\\\",\\\"shopType\\\":144,\\\"shopTypeCode\\\":\\\"\\\",\\\"tradeType\\\":[]}\",\"shopId\":736868564053197568,\"shopName\":\"七次方（一条电商）\",\"shopType\":144}";
        Map<String, Object> map = new HashMap<>(1);
        map.put("method", "differ.omsapi.business.syncshopconfiguration");
        map.put("bizContent", jsonStr);
        map.put("memberName", memName);
        map.put("contextId", 638192067277685632L);
        JackYunResponse<?> response = UserGroupFeignClient.doPost(FeignGateway.OMSAPI_BUSINESS_SERVICE_NAME, "/omsapi-business/open/call", map, Object.class);
        System.out.println("店铺配置同步成功" + JSON.toJSONString(response));
    }

    // endregion

    // region 店铺授权测试

    @Test
    public void synAuthorizeTest() throws Exception {
        init();
        DomainUtils.bindMemberUser(memName);
        String jsonStr = "{\"ishasnextpage\":\"0\",\"numtotalorder\":\"1\",\"orders\":[{\"IsDecryptError\":0,\"activityAmount\":0,\"addedvalueAmount\":0,\"address\":\"辽宁大连市中山区港湾壹号C座2102室-丹东街42\",\"area\":\"中山区\",\"bankAccount\":\"\",\"bfdeligoodglag\":\"否\",\"bondedId\":\"\",\"bondedName\":\"\",\"cardType\":\"JH_01\",\"city\":\"大连市\",\"ckyname\":\"\",\"clerkName\":\"\",\"clerkPhone\":\"\",\"codserviceFee\":0,\"collageTime\":\"\",\"commissionvalue\":0,\"consumptionDutyAmount\":0,\"country\":\"\",\"couponAmount\":0,\"coupondetails\":[{\"couponNum\":0,\"couponType\":\"JH_DiscountCoupon\",\"price\":17.00,\"skuId\":\"***********\",\"type\":\"30-单品促销优惠\"},{\"couponNum\":0,\"couponType\":\"JH_ShopCoupon\",\"price\":5.00,\"skuId\":\"\",\"type\":\"100-店铺优惠\"}],\"createDate\":\"2019-04-26 12:37:31\",\"currencyCode\":\"\",\"customerId\":\"zhang_12\",\"customerRemark\":\"\",\"customerTrueName\":\"晓华\",\"deliveryType\":\"\",\"depositBank\":\"\",\"email\":\"\",\"favourableMoney\":22.00,\"fetchTime\":\"0001-01-01 00:00:00\",\"fulfillmentChannel\":\"\",\"fxtId\":\"\",\"goodinfos\":[{\"activityAmount\":0,\"addedValueAmount\":0,\"consumptiondutyAmount\":0,\"couponAmount\":0,\"deliveryType\":\"\",\"discountMoney\":0,\"goodsBarcode\":\"\",\"goodsBuyerPayment\":0.0,\"goodsCount\":1,\"goodsCount2\":0,\"ishwgflag\":\"0\",\"isplatstorageorder\":\"0\",\"outitemid\":\"411043\",\"outskuid\":\"411043\",\"packageOrderId\":\"\",\"payOrderId\":\"\",\"platGoodsId\":\"***********\",\"platSkuId\":\"***********\",\"price\":69.50,\"productId\":\"***********\",\"productItemId\":\"\",\"refundStatus\":\"JH_99\",\"remark\":\"\",\"specialAmount\":0,\"status\":\"JH_99\",\"suborderNo\":\"***********\",\"sysGoodsId\":0,\"sysSpecId\":0,\"tariffAmount\":0,\"taxAmount\":0,\"tradeGoodsName\":\"【粤供优品】十里花香头道 鲜榨花生油 非转基因食用油 900ML \",\"tradeGoodsNo\":\"411043\",\"tradeGoodsSpec\":\"900ML\"}],\"goodsFee\":69.50,\"idCard\":\"\",\"idCardImgs\":\"\",\"idCardTrueName\":\"\",\"invoiceContent\":\"明细\",\"invoiceTitle\":\"个人\",\"invoiceType\":\"普通发票\",\"isbrandsale\":0,\"isdaixiao\":0,\"isencrypt\":0,\"isforcewlb\":0,\"ishwgflag\":\"0\",\"isjzorder\":0,\"ispresaleorder\":0,\"isship\":0,\"isshippedbyamazontfm\":\"0\",\"isstoreorder\":0,\"leftSendDate\":\"0001-01-01 00:00:00\",\"logisticName\":\"\",\"logisticNo\":\"3529490195945\",\"mobile\":\"13904085937\",\"nick\":\"zhang_12\",\"orderFlag\":\"\",\"orderSource\":\"\",\"orderType\":\"22\",\"partner\":\"*大连市-大连华乐营业部\",\"payMethod\":\"\",\"payTime\":\"2019-04-26 12:37:43\",\"payType\":\"JH_Online\",\"payorderNo\":\"94330178724\",\"phone\":\"13904085937\",\"platDiscountMoney\":0,\"platorderNo\":\"20190517002\",\"postFee\":6.00,\"province\":\"辽宁\",\"qq\":\"\",\"realPayMoney\":53.50,\"receiverName\":\"晓华\",\"registeredAddress\":\"\",\"registeredPhone\":\"\",\"resellerId\":\"\",\"resellerMobile\":\"\",\"resellerShopName\":\"\",\"salechannelId\":610916143429590144,\"shopId\":610916143429590144,\"sellerFlag\":\"JH_Gray\",\"sellerRemark\":\"\",\"sendDate\":\"任意时间\",\"sendStyle\":\"4-在线支付\",\"sendType\":\"JH_ExpressSend\",\"shipTypeName\":\"快递\",\"shipmentServiceLevelCategory\":\"\",\"salechannelId\":\"610916143429590144\",\"shopType\":\"SOP\",\"shouldPayMoney\":53.50,\"shouldPayType\":\"4-在线支付\",\"sortingCode\":\"\",\"specialAmount\":0,\"tariffAmount\":0,\"taxAmount\":0,\"taxPayerIdent\":\"\",\"totalAmount\":53.50,\"town\":\"\",\"tradeAttrJson\":\"\",\"tradeStatus\":\"JH_04\",\"tradeStatusDescription\":\"FINISHED_L\",\"tradeTime\":\"2019-04-26 12:37:31\",\"transactionId\":\"\",\"userName\":\"zhang_12\",\"whseCode\":\"0\",\"zip\":\"\"}],\"nexttoken\":\"\",\"code\":\"10000\",\"msg\":\"SUCCESS\",\"subcode\":\"\",\"submessage\":\"\",\"polyapitotalms\":\"359\",\"polyapirequestid\":\"071733452616\",\"businessShopID\":610916143429590144,\"businessMemberName\":\"jackyun_dev\",\"apiPlat\":198}";
        UpdateShopConfCommand command = JsonUtils.deJson(jsonStr, UpdateShopConfCommand.class);
        shopConfService.updateByShopId(command);
        System.out.println("店铺授权同步成功");
    }

    // endregion

    // region 网店售后单创建接口测试

    @Test
    public void createRefundToOmsOnlineTest() {
        init();
        DomainUtils.bindMemberUser(memName);
        String jsonStr = "{\"apiPlat\":\"52\",\"businessMemberName\":\"jackyun_dev\",\"businessShopID\":536057542756539520,\"delayTime\":0,\"firstToMQTime\":null,\"isHasNextPage\":false,\"isWriteBigContent\":false,\"isWriteBusinessLog\":false,\"lastNotifyTime\":null,\"numNotifyTimes\":0,\"workerNO\":0,\"totalCount\":19,\"refunds\":[{\"refundNo\":\"17746351161130595\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"353494753129893882\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":51,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-21 11:03:17\",\"updateTime\":\"2019-05-21 11:03:18\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"不想买了，已与卖家协商一致\",\"desc\":\"\",\"productName\":\"运动凉鞋女ins潮夏季2019新款仙女风平底时尚百搭夏孕妇防滑外穿\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"17724621471720964\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"353488577358845016\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":58,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-21 10:48:07\",\"updateTime\":\"2019-05-21 10:48:07\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"网红真皮基础小白鞋女2019春款春季新款百搭学生板鞋平底单鞋女鞋\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26593123215052285\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"353073121558534766\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":75,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-21 10:04:47\",\"updateTime\":\"2019-05-21 10:04:47\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"不想买了，已与卖家协商一致\",\"desc\":\"\",\"productName\":\"2019春季网面新款韩版原宿厚底百搭透气增高女鞋气垫运动休闲女\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"17721932862741287\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"276914668894954500\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":55,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-21 10:00:12\",\"updateTime\":\"2019-05-21 10:00:12\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"网红两穿凉鞋女仙女风2019夏季新款拖鞋学生百搭厚底沙滩鞋凉拖鞋\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"16318790911898948\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"278522853885894889\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":80,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-21 09:44:53\",\"updateTime\":\"2019-05-21 09:44:53\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"2019新款亮片拼色老爹鞋女百搭ins超火女厚底真皮松糕鞋街头潮\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"16319046287738819\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"279407524991731988\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":85,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-21 09:39:25\",\"updateTime\":\"2019-05-21 09:39:25\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"老爹鞋女单鞋2019新款春季网红运动鞋女低帮休闲百搭智熏女鞋厚底\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26517921697234527\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"443657504570232745\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":75,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-21 08:42:57\",\"updateTime\":\"2019-05-21 08:42:57\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"镂空运动鞋女2019新款夏季包头凉鞋ins潮百搭厚底休闲透气老爹鞋\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26569952522581830\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"444876544152583018\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":75,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-21 08:42:10\",\"updateTime\":\"2019-05-21 08:42:11\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"不想买了，已与卖家协商一致\",\"desc\":\"\",\"productName\":\"镂空运动鞋女2019新款夏季包头凉鞋ins潮百搭厚底休闲透气老爹鞋\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26572768201234527\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"445311490484232745\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":75,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-21 08:40:06\",\"updateTime\":\"2019-05-21 08:40:06\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"镂空运动鞋女2019新款夏季包头凉鞋ins潮百搭厚底休闲透气老爹鞋\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26515266381043477\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"300162798047047734\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":70,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-20 22:16:29\",\"updateTime\":\"2019-05-20 22:16:29\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"韩版平底高帮鞋女春2019新款真皮圆头学生百搭ulzzang原宿板鞋潮\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"17722956261130772\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"298548589256137207\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":65,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-20 21:55:15\",\"updateTime\":\"2019-05-20 21:55:15\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"真皮网红小白鞋女2019夏款透气百搭韩版学生休闲网面白鞋夏季板鞋\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26549152352328250\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"443370273038325082\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":65,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-20 21:26:55\",\"updateTime\":\"2019-05-20 21:26:55\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"运动休闲凉鞋女夏2019新款韩版原宿风百搭chic平底ins网红学生潮\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26509442518328250\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"443562240566325082\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":75,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-20 21:21:33\",\"updateTime\":\"2019-05-20 21:21:34\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"不想买了/等不及/拍错\",\"desc\":\"\",\"productName\":\"2019春季厚底系带小白鞋运动女韩版ulzzang休闲单鞋ins超火老爹鞋\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26504802877602767\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"446806786877606727\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":70,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-20 21:07:42\",\"updateTime\":\"2019-05-20 21:07:43\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"2019新款凉鞋女夏平底简约学生休闲运动凉鞋原宿风松糕厚底沙滩鞋\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26502658964602767\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"300840462492606727\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":70,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-20 20:57:57\",\"updateTime\":\"2019-05-20 20:57:57\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"阿希哥同款凉鞋女夏2019新款时尚真皮运动网红ins百搭厚底女凉鞋\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"17721132621030064\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"299551660353036400\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":59.4,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-20 20:49:44\",\"updateTime\":\"2019-05-20 20:49:44\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"厚底凉鞋女夏季2019新款松糕百搭学院中跟韩版学生罗马露趾鞋子女\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26482689538866339\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"447079617188863963\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":63,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-20 18:38:38\",\"updateTime\":\"2019-05-20 18:38:38\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"不想买了，已与卖家协商一致\",\"desc\":\"\",\"productName\":\"小白鞋女2019春季新款潮百搭网红平底板鞋学生白鞋春款鞋子女真皮\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26485570364857154\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"450681569996855471\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":70,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-20 16:58:50\",\"updateTime\":\"2019-05-20 16:58:50\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"不想买了/等不及/拍错\",\"desc\":\"\",\"productName\":\"韩版平底高帮鞋女春2019新款真皮圆头学生百搭ulzzang原宿板鞋潮\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26477250287594149\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"450743107416594941\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":82.9,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-20 15:21:26\",\"updateTime\":\"2019-05-20 15:21:27\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"不想买了/等不及/拍错\",\"desc\":\"\",\"productName\":\"2019新款凉鞋女夏平底简约学生休闲运动凉鞋原宿风松糕厚底沙滩鞋\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false}],\"downloadOrderType\":\"2\"}";
        PolyAPIBusinessGetRefundOrderResponseBizData data = JsonUtils.deJson(jsonStr, PolyAPIBusinessGetRefundOrderResponseBizData.class);
        //转化为oms-online需要的实体参数
        OpenOmsOnlineRefundDto onlineRefundDto = new OpenOmsOnlineRefundDto();
        onlineRefundDto.setMemberName(data.getBusinessMemberName());
        onlineRefundDto.setOutShopID(String.valueOf(data.getBusinessShopID()));
        onlineRefundDto.setPlatId(data.getApiPlat().getPlatValue());
        onlineRefundDto.setRefunds(data.getRefunds());
        //数据封装
        Map<String, Object> map = new HashMap<>(1);
        map.put("memName", data.getBusinessMemberName());
        map.put("jsonStr", JsonUtils.toOMSOnlineJson(onlineRefundDto));
        DomainUtils.bindMemberUser(data.getBusinessMemberName());
        JackYunResponse<?> response = UserGroupFeignClient.doPost(FeignGateway.OMSONLINE_OPEN_SERVICE_NAME, "/oms-online/refund/receiveRefunds", map, Object.class);
        //如果失败则抛出异常。
        if (response.getCode() != 200) {
            throw new RuntimeException(response.getMsg());
        }
        System.out.println("网店售后单创建接口测试:" + JsonUtils.toJson(response));
    }

    // endregion

    // region 网店售后单创建接口测试

    @Test
    public void createRefundToOmsApiTest() {
        init();
        DomainUtils.bindMemberUser(memName);
        String jsonStr = "{\"businessShopID\":\"570272545927601280\",\"apiPlat\":1,\"totalcount\":\"4\",\"isautodownload\":\"1\",\"refunds\":[{\"refundno\":\"128115445436438866\",\"platorderno\":\"10197115982447\",\"totalamount\":\"89.00\",\"refundamount\":\"89.00\",\"payamount\":\"0.00\",\"buyernick\":\"[解密失败][本地不解密]~6RmLoWXDDQH8KBhCWfqgIQ==~1~\",\"sellernick\":\"differsoft88\",\"createtime\":\"2018-09-01 00:19:46\",\"updatetime\":\"2018-09-01 00:19:46\",\"orderstatus\":\"JH_02\",\"orderstatusdesc\":\"WAIT_BUYER_RETURN_GOODS\",\"refundstatus\":\"JH_02\",\"refundstatusdesc\":\"WAIT_BUYER_RETURN_GOODS\",\"hasgoodsreturn\":\"1\",\"reason\":\"7天无理由退换货\",\"desc\":\"7天无理由退货\",\"productnum\":\"1\",\"loadtime\":\"2018-09-01 00:19:46\",\"refundgoods\":[{\"platproductid\":\"***************\",\"subplatorderno\":\"***************\",\"outerid\":\"\",\"sku\":\"****************\",\"productname\":\"一叶子面膜补水保湿女收缩毛孔官网正品清洁提亮肤色学生套装美白\",\"refundamount\":\"\",\"reason\":\"7天无理由退换货\",\"productnum\":\"1\",\"price\":\"89.00\"}]}],\"businessMemberName\":\"jackyun_dev\",\"businessplat\":1,\"contextid\":\"\",\"operationuser\":\"jackyun_dev■3\",\"traceoperationflag\":false,\"apiusername\":\"jackyun_dev■3\",\"outaccount\":\"jackyun_dev\",\"isresend\":false}";
        PolyAPIBusinessGetRefundOrderResponseBizData data = JsonUtils.deJson(jsonStr, PolyAPIBusinessGetRefundOrderResponseBizData.class);
        //转化为oms-online需要的实体参数
        RefundOriginalCreateEventRequestBizData bizData = new RefundOriginalCreateEventRequestBizData();
        bizData.setMemName(data.getBusinessMemberName());
        bizData.setRefundBizData(data);
        //封装请求参数
        Map<String, Object> map = new HashMap<>(1);
        map.put("memberName", data.getBusinessMemberName());
        map.put("method", OMSAPIOpenAPITypeEnum.BUSINESS_REFUNDORIGINALCREATE.getMethod());
        map.put("bizContent", JsonUtils.toJson(bizData));
        map.put("contextId", data.getWorkerNO());

        DomainUtils.bindMemberUser(data.getBusinessMemberName());

        JackYunResponse<?> response = UserGroupFeignClient.doPost(FeignGateway.OMSAPI_BUSINESS_SERVICE_NAME, FeignGateway.OMSAPI_BUSINESS_OPEN_CALL, map, Object.class);

        //如果失败则抛出异常。
        if (response.getCode() != 200) {
            throw new RuntimeException(response.getMsg());
        }
        System.out.println("网店售后单创建接口测试:" + JsonUtils.toJson(response));
    }

    // endregion

    // region 网店售后单创建接口测试

    @Test
    public void createRefundToOmsApiSimpleTest() throws Exception {
        init();
        DomainUtils.bindMemberUser(memName);
        String jsonStr = "{\"apiPlat\":\"52\",\"businessMemberName\":\"jackyun_dev\",\"businessShopID\":632319074858706944,\"delayTime\":0,\"firstToMQTime\":null,\"isHasNextPage\":false,\"isWriteBigContent\":false,\"isWriteBusinessLog\":false,\"lastNotifyTime\":null,\"numNotifyTimes\":0,\"workerNO\":0,\"totalCount\":19,\"refunds\":[{\"refundNo\":\"17746351161130595\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"11231562730284811\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":51,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-21 11:03:17\",\"updateTime\":\"2019-05-21 11:03:18\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"不想买了，已与卖家协商一致\",\"desc\":\"\",\"productName\":\"运动凉鞋女ins潮夏季2019新款仙女风平底时尚百搭夏孕妇防滑外穿\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"17724621471720964\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"10022263825972539\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":58,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-21 10:48:07\",\"updateTime\":\"2019-05-21 10:48:07\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"网红真皮基础小白鞋女2019春款春季新款百搭学生板鞋平底单鞋女鞋\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26593123215052285\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"10981143904538726\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":75,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-21 10:04:47\",\"updateTime\":\"2019-05-21 10:04:47\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"不想买了，已与卖家协商一致\",\"desc\":\"\",\"productName\":\"2019春季网面新款韩版原宿厚底百搭透气增高女鞋气垫运动休闲女\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"17721932862741287\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"11072222308210318\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":55,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-21 10:00:12\",\"updateTime\":\"2019-05-21 10:00:12\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"网红两穿凉鞋女仙女风2019夏季新款拖鞋学生百搭厚底沙滩鞋凉拖鞋\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"16318790911898948\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"20687529611193064\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":80,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-21 09:44:53\",\"updateTime\":\"2019-05-21 09:44:53\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"2019新款亮片拼色老爹鞋女百搭ins超火女厚底真皮松糕鞋街头潮\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"16319046287738819\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"279407524991731988\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":85,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-21 09:39:25\",\"updateTime\":\"2019-05-21 09:39:25\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"老爹鞋女单鞋2019新款春季网红运动鞋女低帮休闲百搭智熏女鞋厚底\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26517921697234527\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"6720036046271249\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":75,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-21 08:42:57\",\"updateTime\":\"2019-05-21 08:42:57\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"镂空运动鞋女2019新款夏季包头凉鞋ins潮百搭厚底休闲透气老爹鞋\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26569952522581830\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"444876544152583018\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":75,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-21 08:42:10\",\"updateTime\":\"2019-05-21 08:42:11\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"不想买了，已与卖家协商一致\",\"desc\":\"\",\"productName\":\"镂空运动鞋女2019新款夏季包头凉鞋ins潮百搭厚底休闲透气老爹鞋\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26572768201234527\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"445311490484232745\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":75,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-21 08:40:06\",\"updateTime\":\"2019-05-21 08:40:06\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"镂空运动鞋女2019新款夏季包头凉鞋ins潮百搭厚底休闲透气老爹鞋\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26515266381043477\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"300162798047047734\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":70,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-20 22:16:29\",\"updateTime\":\"2019-05-20 22:16:29\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"韩版平底高帮鞋女春2019新款真皮圆头学生百搭ulzzang原宿板鞋潮\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"17722956261130772\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"298548589256137207\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":65,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-20 21:55:15\",\"updateTime\":\"2019-05-20 21:55:15\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"真皮网红小白鞋女2019夏款透气百搭韩版学生休闲网面白鞋夏季板鞋\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26549152352328250\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"443370273038325082\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":65,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-20 21:26:55\",\"updateTime\":\"2019-05-20 21:26:55\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"运动休闲凉鞋女夏2019新款韩版原宿风百搭chic平底ins网红学生潮\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26509442518328250\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"443562240566325082\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":75,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-20 21:21:33\",\"updateTime\":\"2019-05-20 21:21:34\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"不想买了/等不及/拍错\",\"desc\":\"\",\"productName\":\"2019春季厚底系带小白鞋运动女韩版ulzzang休闲单鞋ins超火老爹鞋\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26504802877602767\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"446806786877606727\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":70,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-20 21:07:42\",\"updateTime\":\"2019-05-20 21:07:43\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"2019新款凉鞋女夏平底简约学生休闲运动凉鞋原宿风松糕厚底沙滩鞋\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26502658964602767\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"300840462492606727\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":70,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-20 20:57:57\",\"updateTime\":\"2019-05-20 20:57:57\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"阿希哥同款凉鞋女夏2019新款时尚真皮运动网红ins百搭厚底女凉鞋\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"17721132621030064\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"299551660353036400\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":59.4,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-20 20:49:44\",\"updateTime\":\"2019-05-20 20:49:44\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"无理由退货\",\"desc\":\"\",\"productName\":\"厚底凉鞋女夏季2019新款松糕百搭学院中跟韩版学生罗马露趾鞋子女\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26482689538866339\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"447079617188863963\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":63,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-20 18:38:38\",\"updateTime\":\"2019-05-20 18:38:38\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"不想买了，已与卖家协商一致\",\"desc\":\"\",\"productName\":\"小白鞋女2019春季新款潮百搭网红平底板鞋学生白鞋春款鞋子女真皮\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26485570364857154\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"450681569996855471\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":70,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-20 16:58:50\",\"updateTime\":\"2019-05-20 16:58:50\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"不想买了/等不及/拍错\",\"desc\":\"\",\"productName\":\"韩版平底高帮鞋女春2019新款真皮圆头学生百搭ulzzang原宿板鞋潮\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false},{\"refundNo\":\"26477250287594149\",\"salechannelId\":607807572816240128,\"splitSellerFee\":\"\",\"splitTaobaoFee\":\"\",\"whseCode\":\"\",\"whseCodeDesc\":\"\",\"platOrderNo\":\"450743107416594941\",\"subPlatOrderNo\":\"\",\"totalAmount\":0,\"refundAmount\":82.9,\"payAmount\":0,\"buyerNick\":\"\",\"sellerNick\":\"\",\"createTime\":\"2019-05-20 15:21:26\",\"updateTime\":\"2019-05-20 15:21:27\",\"orderStatus\":\"JH_01\",\"orderStatusDesc\":\"\",\"refundStatus\":\"JH_01\",\"refundStatusDesc\":\"\",\"goodsStatus\":\"JH_98\",\"goodsStatusDesc\":\"\",\"hasGoodsReturn\":false,\"reason\":\"不想买了/等不及/拍错\",\"desc\":\"\",\"productName\":\"2019新款凉鞋女夏平底简约学生休闲运动凉鞋原宿风松糕厚底沙滩鞋\",\"productNum\":0,\"logisticName\":\"\",\"logisticNo\":\"\",\"sku\":\"\",\"price\":0,\"outerId\":\"\",\"platProductId\":\"\",\"logisticCode\":\"\",\"receiverName\":\"\",\"mobile\":\"\",\"telephone\":\"\",\"address\":\"\",\"refundGoods\":[],\"isMultiGoods\":false}],\"downloadOrderType\":\"2\"}";
        PolyAPIBusinessGetRefundOrderResponseBizData data = JsonUtils.deJson(jsonStr, PolyAPIBusinessGetRefundOrderResponseBizData.class);
        RefundOriginalProcessCommand bizData = new RefundOriginalProcessCommand(data, null);
        refundOriginalService.refundOrderProcess(bizData);

    }

    // endregion

    // region 公共缓存测试

    @Test
    public void ShopInfoTest() {
        String grouId = DomainUtils.getGroupNo(memName);
        BaseShopInfo shopInfo = BaseShopInfoProxy.get().getShopInfo(grouId, memName, 412255837236658432L);
        System.out.println("店铺基本信息:" + JsonUtils.toJson(shopInfo));
    }

    // endregion

    // region 查询客户档案

    @Test
    public void selectCustomerInfoTest() {
        DomainUtils.bindMemberUser(memName);
        List<FilterCustomerInfoDto> lstFilterCustomerInfoDto = new ArrayList<>();
        FilterCustomerInfoDto customerInfoDto = new FilterCustomerInfoDto();
        customerInfoDto.setChannelId(621044309158534272L);
        customerInfoDto.setChannelId(610916143429590100L);
        customerInfoDto.setCustomerAccount("zhang_12");
        lstFilterCustomerInfoDto.add(customerInfoDto);
        Map<String, Object> map = new HashMap<>(1);
        map.put("memName", DomainUtils.getMemberName());
        map.put("jsonStr", JsonUtils.toJson(lstFilterCustomerInfoDto));
        JackYunResponse<?> response = UserGroupFeignClient.doPost(FeignGateway.CRM_OPEN_SERVICE_NAME, "/crm/open/customer/search/customerSource", map, Object.class);
        System.out.println("查询结果：" + JsonUtils.toJson(response));
        List<CustomerInfo> lstCustomerInfo = JsonUtils.deJson(JsonUtils.toJson(response.getData()), new TypeReference<List<CustomerInfo>>() {
        });
        if (CollectionUtils.isEmpty(lstCustomerInfo)) {
            return;
        }
        Map<String, CustomerInfo> customerInfoParam = new HashMap<String, CustomerInfo>();
        for (CustomerInfo customerInfo : lstCustomerInfo) {
            String mapKey = ExtUtils.stringBuilderAppend(String.valueOf(customerInfo.getChannelId()), customerInfo.getCustomerAccount()).toString();
            customerInfoParam.put(mapKey, customerInfo);
        }
    }

    // endregion

    // region 新建客户档案

    @Test
    public void addCustomerInfoTest() {
        DomainUtils.bindMemberUser(memName);
        String jsonStr = "[{\"blackList\":0,\"channelId\":0,\"channelType\":[],\"city\":\"合肥市\",\"contacts\":\"胡灿灿\",\"contactsModelArr\":[{\"cardNo\":\"\",\"cardType\":0,\"city\":\"合肥市\",\"contactsAddress\":\"\",\"contactsMobile\":\"***********\",\"contactsName\":\"胡灿灿\",\"contactsPhone\":\"\",\"contactsRemarks\":\"\",\"contactsZip\":\"\",\"country\":\"\",\"detailedAddress\":\"安徽省合肥市蜀山区政务区龙图路岸上玫瑰北区NC2-807\",\"district\":\"蜀山区\",\"email\":\"\",\"isDelete\":0,\"state\":\"安徽省\",\"town\":\"\"}],\"country\":\"\",\"customerAccount\":\"\",\"customerId\":637578091560022145,\"customerSourceArr\":[{\"channelId\":610916143429590144,\"channelType\":\"POLYMALL\",\"channelTypeName\":\"菠萝派商城\",\"customerAccount\":\"zhang_12_0604\",\"customerId\":0,\"isDelete\":0,\"salesman\":\"\",\"sourceChannel\":\"菠萝派商场\"}],\"customerType\":\"\",\"debtAmount\":0,\"debtAmountMax\":0,\"detailedAddress\":\"安徽省合肥市蜀山区政务区龙图路岸上玫瑰北区NC2-807\",\"district\":\"蜀山区\",\"email\":\"\",\"gmtCreate\":null,\"gmtModified\":null,\"integralBalance\":0,\"isDelete\":0,\"nickname\":\"胡灿灿\",\"phone\":\"\",\"preStorageBalance\":0,\"receivablesAccountArr\":[],\"sourceChannelArr\":[],\"specialReminding\":\"\",\"state\":\"安徽省\",\"tagArr\":[],\"taxNumber\":\"\",\"town\":\"\",\"vipLevel\":\"\",\"zip\":\"\"}]";
        Map<String, Object> map = new HashMap<>(1);
        map.put("memName", memName);
        map.put("jsonStr", jsonStr);
        JackYunResponse<?> response = UserGroupFeignClient.doPost(FeignGateway.CRM_OPEN_SERVICE_NAME, FeignGateway.CRM_OPEN_BULK_ADD, map, Object.class);
        System.out.println("新建客户档案完成：" + JsonUtils.toJson(response));
    }

    // endregion

    // region 缓存键测试

    @Test
    public void keyValueDataProxyTest() throws Exception {
        String jsonStr = "{\"isAutoDownload\":false,\"memName\":\"jackyun_dev\",\"shopConf\":\"{\\\"deliverySync\\\":{\\\"hasJzOrderFlag\\\":0,\\\"splitToWholeFlag\\\":0,\\\"statusDetect\\\":{},\\\"trigger\\\":[]},\\\"flagMapping\\\":[],\\\"goodsStockSync\\\":{\\\"availableWarehouse\\\":[],\\\"goodsRule\\\":[],\\\"isAllowAutoShelves\\\":0,\\\"quantityRule\\\":[]},\\\"orderDownload\\\":{\\\"autoDownloadOption\\\":{\\\"isAutoDownload\\\":0,\\\"timeSpan\\\":[]},\\\"customizedBarcode\\\":{\\\"extractOption\\\":[]},\\\"customizedDownload\\\":{\\\"rules\\\":[]},\\\"flagExclude\\\":[]},\\\"orderPost\\\":{\\\"autoMergeOption\\\":{},\\\"autoPostOption\\\":{\\\"timeSpan\\\":[]}},\\\"tradeType\\\":[]}\",\"shopId\":722373140471186304,\"shopType\":71}";
        SyncShopConfigurationRequestBizData requestBizData = JsonUtils.deJson(jsonStr, SyncShopConfigurationRequestBizData.class);
        AddOrUpdateShopConfCommand shopConfCommand = new AddOrUpdateShopConfCommand();
        MapperUtils.map(requestBizData, shopConfCommand);
        //更新或插入店铺配置
        this.shopConfService.addOrderupdateShopConf(shopConfCommand);
    }
    // endregion 

    //region 缓存测试
    @Test
    public void matchGoodsCacheTest() {

//        List<GoodsAutoMatchResultEntity> lstHasAutoMatchResult = new ArrayList<>();
//        GoodsAutoMatchResultEntity autoMatchResultEntity = new GoodsAutoMatchResultEntity();
//        autoMatchResultEntity.setOnlineGoodsName("1515565");
//        lstHasAutoMatchResult.add(autoMatchResultEntity);
//        GoodsAutoMatchResultDataProxy.get().listPush(lstHasAutoMatchResult, DomainUtils.getUserId(), "refund");
        DomainUtils.bindMemberUser(memName);
        List<OrderOriginalGoodsMatchResultEntity> list = new ArrayList<>();
        OrderOriginalGoodsMatchResultEntity hashItems = new OrderOriginalGoodsMatchResultEntity();
        hashItems.setPlatGoodsId("111");
        hashItems.setPlatOuterSkuId("2222");
        list.add(hashItems);
        OrderOriginalGoodsMatchResultDataProxy.get().syncCache(hashItems);

    }
    // endregion

    // region 旗帜变更消息测试

    @Test
    public void orderFlagChangeMessage() {
        TmcTradeFlagChangePloy changePloy = new TmcTradeFlagChangePloy();
        changePloy.setSourceTradeNo("123456");
        changePloy.setOldFlagIds(new ArrayList<Long>(Arrays.asList(1L)));
        changePloy.setFlagValueIds(new ArrayList<>(Arrays.asList(2L)));
        TmcMessage tmcMessage = TmcMessage.make(123456789L, TmcTopicEnum.JACKYUN_TRADE_TRADEFLAGMODIFY.getValue(), JSON.toJSONString(changePloy));
        // 加入MQ队列。
        MQManager.get().sendMessageWithReTry(MQTypesEnum.ORDERORIGINALSTATUSMSGTOOMS, JsonUtils.toJson(tmcMessage), 0, 5, 1000, false);
        System.out.println("消息发送成功");
    }

    // endregion 

    // region 获取平台特性

    @Test
    public void getPlatFeatures() throws Exception {
        GetPlatFeaturesEvent event = new GetPlatFeaturesEvent();
        event.setMemberName(memName);
        GetPlatFeaturesRequestBizData requestBizData = new GetPlatFeaturesRequestBizData();
        requestBizData.setMemName(memName);
        requestBizData.setPlatValues(new ArrayList<>(Arrays.asList(PolyAPIPlatEnum.BUSINESS_TAOBAO, PolyAPIPlatEnum.BUSINESS_JD)));
        event.setRequestBizData(requestBizData);
        getPlatFeaturesEventHandler.handle(event);
        System.out.println("平台特性结果：" + JsonUtils.toJson(event.getResponseBizData()));
    }

    //endregion

    @Test
    public void refundLogTest() throws Exception {
        init();
        DomainUtils.bindMemberUser(memName);

        OrderOriginalGoodsMatchResultEntity entityorder = new OrderOriginalGoodsMatchResultEntity();
        entityorder.setMemName(memName);
        entityorder.setShopId(551356573812530304L);
        entityorder.setPlatOuterGoodsId("44444");
        entityorder.setPlatGoodsId("ccccc");

        OnlineGoodsMatchEntity onlineGoodsMatchEntity = MapperUtils.map(entityorder, () -> {
            return new OnlineGoodsMatchEntity();
        });
    }

    @Test
    public void tradeOrderAdditionTest() throws Exception {
        init();
        DomainUtils.bindMemberUser(memName);

        List<Long> sysTradeIdList = new ArrayList<>();
        List<TradeOrderAddition> tradeOrderAdditionList = new ArrayList<>();
        try {
            tradeOrderAdditionList = SpringResolveManager.resolve(DaoTradeOrderAdditionDBSwitchMapper.class).getTradeOrderAdditionBySysTradeIds(memName, sysTradeIdList);
        } catch (Exception e) {
            LogUtils.writeError(ApplicationTypeEnum.BUSINESS, "查询订单附加信息数据出错");
        }
    }

    /**
     * 补偿递交
     */
    @Test
    public void compensatePostTest() {
        init();
        DomainUtils.bindMemberUser(memName);
        try {
            CompensatePostEvent compensatePostEvent = new CompensatePostEvent();
            compensatePostEvent.setMemberNames(Collections.singletonList(memName));
            DomainEventServiceManager.get().publishSafe(compensatePostEvent);
        } catch (Exception e) {
            LogUtils.writeError(ApplicationTypeEnum.BUSINESS, "补偿递交失败");
        }
    }
}
