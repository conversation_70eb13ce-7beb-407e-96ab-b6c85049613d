package com.differ.jackyun.omsapidownload.service.worker.downloadorder.businessprofile.plugins;

import com.differ.jackyun.omsapibase.infrastructure.component.mq.data.worker.core.BaseCreateWorkerData;
import com.differ.jackyun.omsapibase.infrastructure.component.mq.data.worker.core.ext.IChildrenTask;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.enums.Business_DownloadOrderModeEnum;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.enums.Business_OrderStatusEnum;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.enums.Business_RefundStatuEnum;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.getorder.extra.BusinessGetOrderRequest_TimeTypeEnum;
import com.differ.jackyun.omsapidownload.service.worker.downloadorder.businessprofile.BaseDownloadOrderBusinessProfile;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 工作子任务执行业务相关(下载订单-未来集市)。
 *
 * <AUTHOR>
 * @since 2019-09-23 17:53:02
 */
@Component
@Scope("prototype")
public class DownloadOrderBusinessProfile_WeiLaiJiShi extends BaseDownloadOrderBusinessProfile {
    /**
     * 获取下载退货退款单的订单状态。
     *
     * @return 退货退款单的订单状态
     */
    @Override
    protected List<Business_RefundStatuEnum> getRefundOrderStatus() {
        // 平台只允许下载状态为JH_02的退款单。
        List<Business_RefundStatuEnum> lstDownloadRefundOrderStatus = new ArrayList<>(1);
        lstDownloadRefundOrderStatus.add(Business_RefundStatuEnum.JH_02);
        return lstDownloadRefundOrderStatus;
    }

    /**
     * 创建下载退货退款订单任务。
     *
     * @param createWorkerData 创建工作任务数据
     * @return 子任务集合
     */
    @Override
    protected List<IChildrenTask> createDownloadRefundOrderTask(BaseCreateWorkerData createWorkerData) {
        return this.createDownloadRefundOrderTaskProfile(createWorkerData);
    }

    /**
     * 获取平台订单交易状态。
     *
     * @param downloadOrderMode 下载订单模式枚举
     * @return 平台订单交易状态
     */
    @Override
    protected List<Business_OrderStatusEnum> getOrderStatus(Business_DownloadOrderModeEnum downloadOrderMode) {
        return this.getOrderStatusExt(downloadOrderMode);
    }

    /**
     * 获取下载订单时间类型。
     *
     * @return 时间类型
     */
    @Override
    protected String getTimeType() {
        return BusinessGetOrderRequest_TimeTypeEnum.JH_01.toString();
    }
}
