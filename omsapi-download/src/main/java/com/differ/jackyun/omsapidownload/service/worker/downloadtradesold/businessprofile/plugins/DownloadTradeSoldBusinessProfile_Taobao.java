package com.differ.jackyun.omsapidownload.service.worker.downloadtradesold.businessprofile.plugins;

import com.differ.jackyun.omsapibase.data.rdsorder.TBDecryptTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.component.mq.data.worker.core.ext.IChildrenTask;
import com.differ.jackyun.omsapibase.infrastructure.component.mq.data.worker.downloadtradesold.DownloadTradeSoldChildrenWorkerData;
import com.differ.jackyun.omsapibase.infrastructure.enums.ApplicationTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.ConfigKeyEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.LogTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.utils.*;
import com.differ.jackyun.omsapibase.infrastructure.utils.core.DiffTimeTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.utils.core.TimeFormatEnum;
import com.differ.jackyun.omsapidownload.infrastructure.utils.TaobaoCryptPublicUtils;
import com.differ.jackyun.omsapidownload.service.worker.downloadtradesold.businessprofile.BaseDownloadTradeSoldBusinessProfile;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 工作子任务执行业务相关(下载订单评价-淘宝)。
 *
 * <AUTHOR>
 * @since 2019-11-26 14:53:02
 */
@Component("DownloadTradeSoldBusinessProfile_Taobao")
@Scope("prototype")
public class DownloadTradeSoldBusinessProfile_Taobao extends BaseDownloadTradeSoldBusinessProfile {
    //region 常量

    /**
     * 解密次数。
     */
    private static final int DECRYPT_RETRY_NUMBER = 5;

    //endregion

    /**
     * 拆分下载评价交易数据工作任务。
     *
     * @param childrenTask 子工作任务
     * @return 拆分后的子工作任务
     */
    @Override
    protected List<IChildrenTask> splitChildrenTask(IChildrenTask childrenTask) {
        // 创建DownloadTradeSoldChildrenWorkerData对象。
        DownloadTradeSoldChildrenWorkerData.DownloadTradeSoldChildrenTask downloadTradeSoldChildrenTask
                = (DownloadTradeSoldChildrenWorkerData.DownloadTradeSoldChildrenTask) childrenTask;

        // 时间差(单位: 小时)。
        long differTime = CoreUtils.diffTime(downloadTradeSoldChildrenTask.getDownloadStartTime(), downloadTradeSoldChildrenTask.getDownloadEndTime(), DiffTimeTypeEnum.HOUR);
        // 开始和结束时间差值为0，则默认赋为1。
        if (differTime <= 0) {
            differTime = 1;
        }

        // 转换成天，获取可拆分多少个时间范围。
        int count = (int) differTime / 24;
        if (differTime % 24 > 0) {
            count += 1;
        }

        // 获取下载评价交易数据最终时间。
        LocalDateTime currentTime = downloadTradeSoldChildrenTask.getDownloadEndTime();
        // 下载评价交易数据工作任务集合。
        List<IChildrenTask> lstChildrenTask = new ArrayList<>();
        for (int i = 1; i <= count; i++) {
            LocalDateTime startTime = currentTime.minusDays(1);
            if (i == count) {
                startTime = startTime.isBefore(downloadTradeSoldChildrenTask.getDownloadStartTime())
                        ? downloadTradeSoldChildrenTask.getDownloadStartTime() : startTime;
            }

            // 下载评价交易数据工作任务集合。
            DownloadTradeSoldChildrenWorkerData.DownloadTradeSoldChildrenTask newDownloadTradeSoldChildrenTask =
                    new DownloadTradeSoldChildrenWorkerData.DownloadTradeSoldChildrenTask();
            newDownloadTradeSoldChildrenTask.setMemberName(downloadTradeSoldChildrenTask.getMemberName());
            newDownloadTradeSoldChildrenTask.setShopID(downloadTradeSoldChildrenTask.getShopID());
            newDownloadTradeSoldChildrenTask.setShopName(downloadTradeSoldChildrenTask.getShopName());
            newDownloadTradeSoldChildrenTask.setSessionKey(downloadTradeSoldChildrenTask.getSessionKey());
            newDownloadTradeSoldChildrenTask.setPlatValue(downloadTradeSoldChildrenTask.getPlatValue());
            newDownloadTradeSoldChildrenTask.setDownloadStartTime(startTime);
            newDownloadTradeSoldChildrenTask.setDownloadEndTime(currentTime);
            newDownloadTradeSoldChildrenTask.setIsAutomatic(downloadTradeSoldChildrenTask.getIsAutomatic());
            newDownloadTradeSoldChildrenTask.setIsWriteProgressRemark(true);
            newDownloadTradeSoldChildrenTask.setToken(downloadTradeSoldChildrenTask.getToken());
            // 生成子任务批次号。
            newDownloadTradeSoldChildrenTask.setTaskNo(CoreUtils.createUUIDText());
            newDownloadTradeSoldChildrenTask.setTaskName(String.format("下载评价交易数据(%s~%s)",
                    DateTimeUtils.timeToString(newDownloadTradeSoldChildrenTask.getDownloadStartTime(), TimeFormatEnum.SHORT_TIME_PATTERN_LINE),
                    DateTimeUtils.timeToString(newDownloadTradeSoldChildrenTask.getDownloadEndTime(), TimeFormatEnum.SHORT_TIME_PATTERN_LINE)));
            newDownloadTradeSoldChildrenTask.setPageSize(downloadTradeSoldChildrenTask.getPageSize());
            lstChildrenTask.add(newDownloadTradeSoldChildrenTask);
            // 重置
            currentTime = startTime;
        }

        return lstChildrenTask;
    }

    /**
     * 解密淘宝卖家交易数据加密字段。
     *
     * @param text                          加密字段
     * @param decryptType                   解密类型
     * @param sessionKey                    店铺sessionKey
     * @param downloadTradeSoldChildrenTask
     * @return 交易数据加密字段明文
     */
    @Override
    protected String decryptTradeSold(String text, TBDecryptTypeEnum decryptType, String sessionKey, DownloadTradeSoldChildrenWorkerData.DownloadTradeSoldChildrenTask downloadTradeSoldChildrenTask) {
        if (!ExtUtils.toBoolean(YunConfigUtils.getDBConfig(ConfigKeyEnum.WORKER_DOWNLOAD_TAOBAO_DECRYPT))) {
            return super.decryptTradeSold(text, decryptType, sessionKey, downloadTradeSoldChildrenTask);
        }

        if (ExtUtils.isNullOrEmpty(text)) {
            return super.decryptTradeSold(text, decryptType, sessionKey, downloadTradeSoldChildrenTask);
        }

        // 测试环境无法解析加密字段。
        if (Boolean.TRUE.equals(LocalConfig.get().getIsTest())) {
            return text;
        }

        // 若解密失败，将会重试4次。
        for (int i = 0; i < DECRYPT_RETRY_NUMBER; i++) {
            try {
                return TaobaoCryptPublicUtils.tbDecryptSDK(decryptType, text, sessionKey, downloadTradeSoldChildrenTask.getMemberName(), downloadTradeSoldChildrenTask.getShopID());
            } catch (Exception ex) {
                LogUtils.write(LogTypeEnum.ERROR, ApplicationTypeEnum.TASK, "[下载卖家交易数据]" + decryptType.getCaption() + "解密失败:" + CoreUtils.exceptionToString(ex));
            }
        }

        return text;
    }
}
