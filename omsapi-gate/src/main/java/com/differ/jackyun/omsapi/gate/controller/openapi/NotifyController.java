package com.differ.jackyun.omsapi.gate.controller.openapi;

import com.differ.jackyun.omsapi.user.biz.domain.core.DomainUtil;
import com.differ.jackyun.omsapi.user.biz.infrastructure.client.feign.api.internal.PlatNotifyFeignClient;
import com.differ.jackyun.omsapi.user.biz.infrastructure.common.LogAdapter;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.constants.ParamConstant;
import com.differ.jackyun.omsapibase.infrastructure.openapi.core.OmsApiNotifyResponse;
import com.differ.jackyun.omsapibase.infrastructure.utils.ExtUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 网关服务接口
 *
 * <AUTHOR> chuff
 * @since 2021-12-16  16:06:40
 */
@RestController
@RequestMapping("/openapi/do")
public class NotifyController {

    @Autowired
    private PlatNotifyFeignClient feignClient;

    /**
     * 消息转发。（聚合使用）
     * 必要参数：outusername、method
     *
     * @param parameters 参数体
     * @return 响应
     */
    @PostMapping("/notify")
    public OmsApiNotifyResponse<Object> notify(@RequestBody Map<String, Object> parameters) {
        return notifyOnFeign(parameters);
    }

    /**
     * 消息转发。（聚合使用）
     * 必要参数：outusername、method
     *
     * @param parameters 参数体
     * @return 响应
     */
    @PostMapping("/notify/param")
    public OmsApiNotifyResponse<Object> notifyParam(@RequestParam Map<String, Object> parameters) {
        return notifyOnFeign(parameters);
    }

    /**
     * 按会员转发。
     * 必要参数：membername、method
     *
     * @param parameters 参数体
     * @return 响应
     */
    protected OmsApiNotifyResponse<Object> notifyOnFeign(Map<String, Object> parameters) {
        Object member = ObjectUtils.firstNonNull(parameters.get(ParamConstant.OUT_USER_NAME), parameters.get(ParamConstant.MEMBER_NAME));
        // 检测会员名
        if (member == null) {
            return OmsApiNotifyResponse.onFail("会员名不可为空");
        }
        String memberName = member.toString();
        if (StringUtils.isBlank(memberName)) {
            return OmsApiNotifyResponse.onFail("会员名不可为空字符");
        }

        // 调用集群服务
        try {
            LogAdapter.writeBusinessLog("网关转发消息", () -> ExtUtils.stringBuilderAppend("url：固定转发/openapi/do/notify", ",吉客号：", memberName));
            return DomainUtil.doWithMemberFunc(memberName, memberInfo -> feignClient.notify(memberName, parameters));
        } catch (Exception ex) {
            return OmsApiNotifyResponse.onFail(ex.getMessage());
        }
    }
}
