package com.differ.jackyun.omsapisend.service.worker.send.businessprofile.plugins;

import com.differ.jackyun.omsapibase.infrastructure.component.mq.data.worker.core.ext.SendOrderData;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.batchsend.PolyAPIBusinessSendRequestBizData;
import com.differ.jackyun.omsapisend.service.worker.send.businessprofile.BaseSplitSendBusinessProfileByProduct;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 工作子任务执行业务相关(订单发货-<PERSON>an<PERSON>iang)
 *
 * <AUTHOR>
 * @Date 2021/11/04 11:10
 **/

@Component
@Scope("prototype")
public class SendBusinessProfile_DianJiang extends BaseSplitSendBusinessProfileByProduct {

    /**
     * 将菠萝派返回的子订单号放入到发货数据中
     *
     * @param lstProductData 发货商品数据(初始数据 1商品-n物流)
     * @return 已封装好子订单号的内容
     */
    @Override
    protected List<PolyAPIBusinessSendRequestBizData.PolyAPIBusinessGoodsInfo> getProductDataByUnSplit(List<SendOrderData.InitProductData> lstProductData) {

        // 没有商品信息时，直接返回
        if (lstProductData == null || lstProductData.isEmpty()) {
            return super.getProductDataByUnSplit(lstProductData);
        }

        List<PolyAPIBusinessSendRequestBizData.PolyAPIBusinessGoodsInfo> result = new ArrayList<>(lstProductData.size());
        // 遍历添加子订单号。
        lstProductData.forEach(p -> {
            PolyAPIBusinessSendRequestBizData.PolyAPIBusinessGoodsInfo productInfo = new PolyAPIBusinessSendRequestBizData.PolyAPIBusinessGoodsInfo();
            productInfo.setPlatProductId(p.getPlatGoodsId());
            productInfo.setCount(p.getSellCount().intValue());
            productInfo.setSubOrderNo(p.getSubTradeNo());
            result.add(productInfo);
        });

        return result;
    }

}
