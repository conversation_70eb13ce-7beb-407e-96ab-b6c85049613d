package com.differ.jackyun.omsapi.user.biz.domain.apicall.plugins.authorization;

import com.differ.jackyun.omsapi.user.biz.domain.apicall.AbstractApiCall;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.addqimenuser.PolyAPIBusinessAddQiMenUserRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.addqimenuser.PolyAPIBusinessAddQiMenUserResponseBizData;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.core.PolyAPITypeEnum;
import org.springframework.stereotype.Component;

/**
 * 添加奇门订单链路用户
 *
 * <AUTHOR>
 * @date 2024-05-20 14:14
 */
@Component
public class AddQiMenUserApiCall extends AbstractApiCall<PolyAPIBusinessAddQiMenUserRequestBizData, PolyAPIBusinessAddQiMenUserResponseBizData> {

    /**
     * 获取接口类型
     *
     * @return 接口类型
     */
    @Override
    protected PolyAPITypeEnum getApiType() {
        return PolyAPITypeEnum.BUSINESS_QIMENUSERADD;
    }

    /**
     * 获取响应业务数据类型
     *
     * @return 类型
     */
    @Override
    protected Class<? extends PolyAPIBusinessAddQiMenUserResponseBizData> getResponseBizType() {
        return PolyAPIBusinessAddQiMenUserResponseBizData.class;
    }
}