package com.differ.jackyun.omsapi.user.biz.domain.authorization.biz.module.display.remind;

import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.AuthKeyDisplay;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.AuthProcessorInitParamWithConfig;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.common.KeyValuePair;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.config.PlatAuthConfig;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.handle.FormatKeyDisplayParam;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.result.AuthContentResult;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.result.AuthErrorContent;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.handler.common.impl.AuthDisplayHandler;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.handler.custom.params.impl.CustomParamsHandler;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.processor.AuthFactory;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.processor.BaseAuthPlatProcessor;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.utils.AuthContextUtils;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.utils.AuthLogUtils;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.utils.PlatAuthConfigUtils;
import com.differ.jackyun.omsapi.user.biz.domain.shopconfig.data.ApiShopConfigComposite;
import com.differ.jackyun.omsapi.user.biz.infrastructure.common.LogAdapter;
import com.differ.jackyun.omsapibase.data.open.erp.BaseShopInfo;
import com.differ.jackyun.omsapibase.infrastructure.enums.LogTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.core.PolyAPIPlatEnum;
import com.differ.jackyun.omsapibase.infrastructure.utils.ExtUtils;
import com.differ.jackyun.omsapibase.infrastructure.utils.JsonUtils;

import java.util.List;

/**
 * 店铺配置授权展示信息
 *
 * <AUTHOR>
 * @date 2024-09-20 13:44
 */
public class ShopConfigAuthDisplayHandler {

    // region 公共方法

    /**
     * 生成授权展示信息
     *
     * @param memberName  会员名
     * @param shopId      店铺 ID
     * @param channelInfo 渠道信息
     * @param shopConfig  店铺配置
     * @param userId      操作人 ID
     * @param userName    操作人名称
     * @return 结果
     */
    public AuthKeyDisplay buildAuthDisplay(
            String memberName,
            Long shopId,
            String userId,
            String userName,
            BaseShopInfo channelInfo,
            ApiShopConfigComposite shopConfig
    ) {

        // 结果集
        AuthKeyDisplay ret = new AuthKeyDisplay();
        try {

            // 解析平台
            PolyAPIPlatEnum plat = AuthContextUtils.resolvePlat(channelInfo, shopConfig);
            if (plat == null) {
                return ret;
            }

            // 查询平台授权配置
            AuthContentResult<PlatAuthConfig, AuthErrorContent> authConfigRet = PlatAuthConfigUtils.getPlatAuthConfig(plat);
            if (authConfigRet.isFailed()) {
                LogAdapter.writeSystemLog(memberName, "生成授权展示信息", () -> ExtUtils.stringBuilderAppend(String.format("生成授权展示信息失败，结果：%s", JsonUtils.toJson(authConfigRet))), LogTypeEnum.ERROR);
                return ret;
            }

            // 初始化参数
            AuthProcessorInitParamWithConfig initParam = new AuthProcessorInitParamWithConfig();
            initParam.setMemberName(memberName);
            initParam.setShopId(shopId);
            initParam.setUserId(userId);
            initParam.setUserName(userName);
            initParam.setChannelInfo(channelInfo);
            initParam.setShopConfig(shopConfig);
            initParam.setPlatAuthConfig(authConfigRet.getSuccessContent());

            // 创建并初始化平台处理器
            AuthContentResult<BaseAuthPlatProcessor, AuthErrorContent> platProcessorRet = AuthFactory.createAndInit(initParam);
            if (platProcessorRet.isFailed()) {
                LogAdapter.writeSystemLog(memberName, AuthLogUtils.LC_AUTH_DISPLAY, () -> ExtUtils.stringBuilderAppend(String.format("生成授权展示信息失败，结果：%s", JsonUtils.toJson(platProcessorRet))), LogTypeEnum.ERROR);
                return ret;
            }

            // 平台处理器
            BaseAuthPlatProcessor platProcessor = platProcessorRet.getSuccessContent();

            // 封装授权展示信息
            AuthKeyDisplay authDisplay = AuthDisplayHandler.getHandler(platProcessor).formatKeyDisplayForShopConfig(new FormatKeyDisplayParam(memberName, shopId));

            // 获取推送配置
            List<KeyValuePair> pushConfigs = CustomParamsHandler.getHandler(platProcessor).getPushConfigs();

            // 添加到结果集
            authDisplay.getItems().forEach(ret::addItem);
            pushConfigs.forEach(t -> ret.addItem(t.getKey(), t.getValue()));
        } catch (Exception e) {
            LogAdapter.writeSystemLog(memberName, AuthLogUtils.LC_AUTH_DISPLAY, e);
        }

        return ret;
    }

    // endregion
}
