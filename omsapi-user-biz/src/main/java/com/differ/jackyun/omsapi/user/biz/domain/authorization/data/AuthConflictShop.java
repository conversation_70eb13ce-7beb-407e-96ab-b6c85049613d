package com.differ.jackyun.omsapi.user.biz.domain.authorization.data;

import com.differ.jackyun.framework.component.pagehelper.util.StringUtil;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.utils.AuthConflictUtils;
import com.differ.jackyun.omsapi.user.biz.domain.shopconfig.data.ApiShopConfigComposite;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.shop.core.ApiShopBaseInfo;
import com.differ.jackyun.omsapibase.data.shopconf.enums.ShopAuthStatusEnum;
import com.differ.jackyun.omsapibase.infrastructure.openapi.authorization.PlatAuthorizationCustomBizData;
import com.differ.jackyun.omsapibase.infrastructure.utils.CollectionsUtil;
import org.apache.commons.collections.MapUtils;

import java.util.*;
import java.util.function.Function;

/**
 * 授权冲突店铺
 *
 * <AUTHOR>
 * @date 2024-03-21 10:54
 */
public class AuthConflictShop {

    /**
     * 构造方法
     */
    public AuthConflictShop() {
        this.shopItems = new HashMap<>();
    }

    /**
     * 店铺集合
     */
    private Map<String, Map<Long, ShopItem>> shopItems;

    /**
     * 添加店铺
     *
     * @param memberName 会员名
     * @param composite  店铺配置组合
     */
    public void addShop(String memberName, ApiShopConfigComposite composite) {

        // 校验入参
        if (StringUtil.isEmpty(memberName) || composite == null) {
            return;
        }

        // 店铺基础信息
        ApiShopBaseInfo shopBase = composite.getShopBaseInfo();
        if (shopBase == null || shopBase.getShopId() == null) {
            return;
        }

        // 初始化键值
        if (!this.shopItems.containsKey(memberName)) {
            this.shopItems.put(memberName, new HashMap<>());
        }

        // 数据转换
        ShopItem shopItem = AuthConflictUtils.convert(composite);

        // 只添加已授权店铺
        if (!ShopAuthStatusEnum.AUTHORIZED.equals(shopItem.getAuthStatus())) {
            return;
        }

        // 添加数据
        this.shopItems.get(memberName).put(shopBase.getShopId(), shopItem);
    }

    /**
     * 添加店铺
     *
     * @param memberName 会员名
     * @param shopItems  店铺项
     */
    public void addShops(String memberName, List<ShopItem> shopItems) {
        if (StringUtil.isEmpty(memberName) || CollectionsUtil.isBlank(shopItems)) {
            return;
        }

        if (!this.shopItems.containsKey(memberName)) {
            this.shopItems.put(memberName, new HashMap<>());
        }

        for (ShopItem shopItem : shopItems) {
            if (shopItem.getShopId() == null) {
                continue;
            }

            // 只添加已授权店铺
            if (!ShopAuthStatusEnum.AUTHORIZED.equals(shopItem.getAuthStatus())) {
                continue;
            }

            this.shopItems.get(memberName).put(shopItem.getShopId(), shopItem);
        }
    }

    /**
     * 移除店铺
     *
     * @param func 匹配方法
     */
    public void removeShop(Function<ShopItem, Boolean> func) {
        if (MapUtils.isEmpty(this.shopItems)) {
            return;
        }

        // 遍历店铺
        for (Map.Entry<String, Map<Long, ShopItem>> entry : this.shopItems.entrySet()) {
            Map<Long, ShopItem> shopItems = entry.getValue();
            if (MapUtils.isEmpty(shopItems)) {
                continue;
            }

            // 匹配需要移除的店铺
            Set<Long> removeShopIds = new HashSet<>();
            shopItems.forEach(((shopId, shopItem) -> {
                if (Boolean.TRUE.equals(func.apply(shopItem))) {
                    removeShopIds.add(shopItem.shopId);
                }
            }));

            // 移除店铺
            if (!removeShopIds.isEmpty()) {
                removeShopIds.forEach(shopItems::remove);
            }
        }
    }

    // region getter & setter

    public Map<String, Map<Long, ShopItem>> getShopItems() {
        return shopItems;
    }

    public void setShopItems(Map<String, Map<Long, ShopItem>> shopItems) {
        this.shopItems = shopItems;
    }

    // endregion

    /**
     * 店铺项
     */
    public static class ShopItem {

        /**
         * 店铺 ID
         */
        private Long shopId;

        /**
         * 店铺 Token
         */
        private String shopToken;

        /**
         * 店铺名称
         */
        private String shopName;

        /**
         * 授权状态 0 已授权 -1 未授权 -2 授权失效 -3 已冻结
         */
        private ShopAuthStatusEnum authStatus;

        /**
         * 卖家昵称
         */
        private String authSellNick;

        /**
         * 平台店铺 ID
         */
        private String platShopId;

        /**
         * 自用型平台授权参数实体（解密后）
         */
        private PlatAuthorizationCustomBizData selfUseAuthItem;

        /**
         * 交易类型
         */
        private String[] tradeTypes;

        /**
         * 创建实例
         *
         * @param shopId    店铺 ID
         * @param shopToken 店铺 Token
         * @param shopName  店铺名称
         * @return 结果
         */
        public static ShopItem create(Long shopId, String shopToken, String shopName) {
            ShopItem shopItem = new ShopItem();
            shopItem.setShopId(shopId);
            shopItem.setShopToken(shopToken);
            shopItem.setShopName(shopName);
            return shopItem;
        }

        // region getter & setter

        public Long getShopId() {
            return shopId;
        }

        public void setShopId(Long shopId) {
            this.shopId = shopId;
        }

        public String getShopToken() {
            return shopToken;
        }

        public void setShopToken(String shopToken) {
            this.shopToken = shopToken;
        }

        public String getShopName() {
            return shopName;
        }

        public void setShopName(String shopName) {
            this.shopName = shopName;
        }

        public ShopAuthStatusEnum getAuthStatus() {
            return authStatus;
        }

        public void setAuthStatus(ShopAuthStatusEnum authStatus) {
            this.authStatus = authStatus;
        }

        public String getAuthSellNick() {
            return authSellNick;
        }

        public void setAuthSellNick(String authSellNick) {
            this.authSellNick = authSellNick;
        }

        public String getPlatShopId() {
            return platShopId;
        }

        public void setPlatShopId(String platShopId) {
            this.platShopId = platShopId;
        }

        public PlatAuthorizationCustomBizData getSelfUseAuthItem() {
            return selfUseAuthItem;
        }

        public void setSelfUseAuthItem(PlatAuthorizationCustomBizData selfUseAuthItem) {
            this.selfUseAuthItem = selfUseAuthItem;
        }

        public String[] getTradeTypes() {
            return tradeTypes;
        }

        public void setTradeTypes(String[] tradeTypes) {
            this.tradeTypes = tradeTypes;
        }

        // endregion
    }
}
