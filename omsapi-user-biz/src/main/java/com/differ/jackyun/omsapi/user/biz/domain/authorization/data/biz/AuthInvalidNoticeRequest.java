package com.differ.jackyun.omsapi.user.biz.domain.authorization.data.biz;

/**
 * 授权失效通知请求
 *
 * <AUTHOR>
 * @date 2024-10-16 17:24
 */
public class AuthInvalidNoticeRequest extends BaseShopAuthNoticeRequest {

    /**
     * 子编码
     */
    private String subCode;

    /**
     * 子消息
     */
    private String subMessage;

    /**
     * 过期时间
     */
    private String sessionKeyExpireTime;

    // region getter & setter

    public String getSubCode() {
        return subCode;
    }

    public void setSubCode(String subCode) {
        this.subCode = subCode;
    }

    public String getSubMessage() {
        return subMessage;
    }

    public void setSubMessage(String subMessage) {
        this.subMessage = subMessage;
    }

    public String getSessionKeyExpireTime() {
        return sessionKeyExpireTime;
    }

    public void setSessionKeyExpireTime(String sessionKeyExpireTime) {
        this.sessionKeyExpireTime = sessionKeyExpireTime;
    }

    // endregion
}
