package com.differ.jackyun.omsapi.user.biz.domain.authorization.data.flow;

import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.common.BaseAuthResponse;

/**
 * 授权流程响应-处理授权冲突
 *
 * <AUTHOR>
 * @date 2024-04-30 17:07
 */
public class ProcessAuthConflictResponse extends BaseAuthResponse {

    /**
     * 是否结束授权流程
     */
    private Byte endAuthFlow;

    // region getter & setter

    public Byte getEndAuthFlow() {
        return endAuthFlow;
    }

    public void setEndAuthFlow(Byte endAuthFlow) {
        this.endAuthFlow = endAuthFlow;
    }

    // endregion
}
