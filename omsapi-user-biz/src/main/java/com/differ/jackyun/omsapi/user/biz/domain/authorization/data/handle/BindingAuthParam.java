package com.differ.jackyun.omsapi.user.biz.domain.authorization.data.handle;

import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.info.AuthModeAuthInfo;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.common.CustomConfig;

/**
 * 参数-绑定授权
 *
 * <AUTHOR>
 * @date 2024-03-04 23:42
 */
public class BindingAuthParam extends BaseAuthHandleParam {

    /**
     * 构造方法
     *
     * @param memberName 会员名
     * @param shopId     店铺 ID
     */
    public BindingAuthParam(String memberName, Long shopId) {
        super(memberName, shopId);
    }

    /**
     * 自定义参数
     */
    private CustomConfig customParams;

    /**
     * 授权信息
     */
    private AuthModeAuthInfo authInfo;

    // region getter & setter

    public CustomConfig getCustomParams() {
        return customParams;
    }

    public void setCustomParams(CustomConfig customParams) {
        this.customParams = customParams;
    }

    public AuthModeAuthInfo getAuthInfo() {
        return authInfo;
    }

    public void setAuthInfo(AuthModeAuthInfo authInfo) {
        this.authInfo = authInfo;
    }

    // endregion
}
