package com.differ.jackyun.omsapi.user.biz.domain.authorization.data.result;

/**
 * 授权错误内容（菠萝派）
 *
 * <AUTHOR>
 * @date 2024-06-07 10:56
 */
public class PolyApiAuthErrorContent extends AuthErrorContent {

    /**
     * 标题
     */
    private String title;

    /**
     * 编码
     */
    private String subCode;

    /**
     * 错误信息
     */
    private String subMessage;

    /**
     * 菠萝派 ID
     */
    private String polyId;

    // region getter & setter

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubCode() {
        return subCode;
    }

    public void setSubCode(String subCode) {
        this.subCode = subCode;
    }

    public String getSubMessage() {
        return subMessage;
    }

    public void setSubMessage(String subMessage) {
        this.subMessage = subMessage;
    }

    public String getPolyId() {
        return polyId;
    }

    public void setPolyId(String polyId) {
        this.polyId = polyId;
    }

    // endregion
}
