package com.differ.jackyun.omsapi.user.biz.domain.authorization.handler.other.biz.sub;

import com.differ.jackyun.framework.component.basic.dto.JackYunResponse;
import com.differ.jackyun.framework.component.pagehelper.util.StringUtil;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.AuthConflictShopWarning;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.constants.AuthConstants;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.handle.*;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.result.AuthContentResult;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.result.AuthErrorContent;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.handler.AbstractAuthHandler;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.handler.cancel.auth.ICancelAuthHandler;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.handler.cancel.auth.impl.CancelAuthHandler;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.handler.other.biz.impl.CopyAuthInfoHandler;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.processor.BaseAuthPlatProcessor;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.sub.domain.AuthCrossClusterOperation;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.sub.domain.AuthPersistOperation;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.utils.AuthLogUtils;
import com.differ.jackyun.omsapi.user.biz.infrastructure.client.feign.api.internal.ApiShopAuthFeignClient;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.authorization.request.CopyShopAuthInfoRequestDTO;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.common.CommonSimpleResponse;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.enums.authorization.AuthErrorCode;
import com.differ.jackyun.omsapibase.infrastructure.enums.SiteTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.utils.ExtUtils;
import com.differ.jackyun.omsapibase.infrastructure.utils.JsonUtils;
import com.differ.jackyun.omsapibase.infrastructure.utils.SpringResolveManager;

/**
 * 授权处理-授权冲突（取消当前店铺授权）
 *
 * <AUTHOR>
 * @date 2024-05-09 15:38
 */
public class AuthConflictByCancelCurrentHandler extends AbstractAuthHandler {

    // region 构造方法

    /**
     * 构造方法
     *
     * @param platProcessor 平台特殊处理
     */
    public AuthConflictByCancelCurrentHandler(BaseAuthPlatProcessor platProcessor) {
        super(platProcessor);
    }

    // endregion

    // region 公共方法

    /**
     * 处理授权冲突
     *
     * @param param 参数
     * @return 结果
     */
    public AuthContentResult<ProcessAuthConflictResult, AuthErrorContent> processAuthConflict(ProcessAuthConflictParam param) {
        try {

            // 1.复制授权信息（当前会员）
            for (AuthConflictShopWarning.ConflictShop conflictShop : param.getCurrentConflictShops()) {
                this.copyShopAuthInfo(param.getMemberName(), param.getShopId(), conflictShop);
            }

            // 2. 复制授权信息（其它会员）
            for (AuthConflictShopWarning.ConflictShop conflictShop : param.getOtherConflictShops()) {
                this.copyShopAuthInfo(param.getMemberName(), param.getShopId(), conflictShop);
            }

            // 3. 取消当前店铺授权
            CancelAuthParam cancelAuthParam = new CancelAuthParam(param.getMemberName(), param.getShopId());
            ICancelAuthHandler cancelAuthHandler = CancelAuthHandler.getHandler(this.platProcessor);
            AuthContentResult<CancelAuthResult, AuthErrorContent> cancelAuthRet = cancelAuthHandler.cancelAuth(cancelAuthParam);
            if (cancelAuthRet.isFailed()) {
                return AuthContentResult.failed(cancelAuthRet);
            }

            // 4. 记录店铺授权操作日志
            AuthPersistOperation.singleton().addShopAuthLog(this.context, "授权冲突，已选择取消当前店铺授权，刷新被冲突店铺授权信息");

            // 返回成功
            return AuthContentResult.success(ProcessAuthConflictResult.endAuth());
        } catch (Exception e) {
            AuthLogUtils.logHandleException(param.getMemberName(), "授权冲突处理（取消当前店铺授权）", e);
            return AuthContentResult.failed(AuthErrorCode.SYSTEM_ERROR);
        }
    }

    // endregion

    // region 私有方法

    /**
     * 复制授权信息
     *
     * @param currentMemberName 当前会员
     * @param currentShopId     当前店铺
     * @param conflictShop      冲突店铺
     */
    private void copyShopAuthInfo(String currentMemberName, Long currentShopId, AuthConflictShopWarning.ConflictShop conflictShop) {
        try {

            // 校验目标店铺 ID 和 Token
            if (StringUtil.isEmpty(conflictShop.getShopId()) || StringUtil.isEmpty(conflictShop.getPolyToken())) {
                AuthLogUtils.logWarning(currentMemberName, () -> ExtUtils.stringBuilderAppend(String.format("复制授权信息失败，目标店铺 ID 或 Token 为空，冲突店铺：%s", JsonUtils.toJson(conflictShop))));
                return;
            }

            // 封装参数
            CopyAuthInfoParam copyAuthInfoParam = new CopyAuthInfoParam(currentMemberName, currentShopId);
            copyAuthInfoParam.setTargetMemberName(conflictShop.getMemberName());
            copyAuthInfoParam.setTargetShopId(Long.parseLong(conflictShop.getShopId()));
            copyAuthInfoParam.setTargetShopToken(conflictShop.getPolyToken());

            // 复制授权信息
            AuthContentResult<CopyAuthInfoResult, AuthErrorContent> copyRet = CopyAuthInfoHandler.getHandler(this.platProcessor).copyAuthInfo(copyAuthInfoParam);
            if (copyRet.isFailed()) {
                AuthLogUtils.logWarning(currentMemberName, () -> ExtUtils.stringBuilderAppend(String.format("复制授权信息失败，冲突店铺：%s", JsonUtils.toJson(conflictShop))));
            }
        } catch (Exception e) {
            AuthLogUtils.logHandleException(currentMemberName, "授权冲突处理（取消当前店铺授权）", e);
        }
    }

    // endregion
}