package com.differ.jackyun.omsapi.user.biz.domain.authorization.processor.plat;

import com.differ.jackyun.omsapi.component.util.anno.Out;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.AuthContext;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.AuthPersistComposite;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.common.CustomConfig;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.config.PlatAuthConfig;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.handle.BindingAuthParam;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.result.AuthContentResult;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.result.AuthErrorContent;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.processor.BaseAuthPlatProcessor;

/**
 * 平台特殊处理-美客多
 *
 * https://s.jkyun.biz/IjVfyOf 授权平台特殊-美客多（1185）
 *
 * <AUTHOR>
 * @date 2024-09-11 10:07
 */
public class MercadolibreAuthProcessor extends BaseAuthPlatProcessor {

    // region 构造方法

    /**
     * 构造方法
     *
     * @param context 授权上下文
     */
    public MercadolibreAuthProcessor(AuthContext context) {
        super(context);
    }

    // endregion

    // region 变量 & 常量

    /**
     * 自定义参数：Client ID
     */
    public static final String CPK_CLIENT_ID = "client_id";

    /**
     * 自定义参数：Client Secret
     */
    public static final String CPK_CLIENT_SECRET = "client_secret";

    // endregion

    // region 重写基类方法

    /**
     * 特殊处理平台授权配置
     *
     * @param platAuthConfig 平台授权配置
     * @param customParams   自定义参数
     * @return 结果
     */
    @Override
    public AuthContentResult<Object, AuthErrorContent> specialPlatAuthConfig(@Out PlatAuthConfig platAuthConfig, CustomConfig customParams) {

        // 设置 App Key
        customParams.ifPresent(CPK_CLIENT_ID, platAuthConfig.getAppConfig()::setAppKey);

        // 设置 App Secret
        customParams.ifPresent(CPK_CLIENT_SECRET, platAuthConfig.getAppConfig()::setAppSecret);

        // 返回成功
        return AuthContentResult.success();
    }

    /**
     * 特殊处理持久化数据
     *
     * @param param            参数
     * @param persistComposite 持久化数据集合
     * @return 结果
     */
    @Override
    public AuthContentResult<Object, AuthErrorContent> specialPersistData(BindingAuthParam param,@Out AuthPersistComposite persistComposite) {

        // 设置为长期有效
        persistComposite.getShopAuth().setAuthRemainTime(null);

        // 返回成功
        return AuthContentResult.success();
    }

    // endregion
}