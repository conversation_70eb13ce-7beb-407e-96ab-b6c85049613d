package com.differ.jackyun.omsapi.user.biz.domain.devops.plugins;

import com.differ.jackyun.framework.component.basic.dto.JackYunResponse;
import com.differ.jackyun.omsapi.user.biz.domain.core.DomainUtil;
import com.differ.jackyun.omsapi.user.biz.domain.devops.AbstractMemberOrShopLevelFunc;
import com.differ.jackyun.omsapi.user.biz.domain.devops.data.MemberOrShopLevelSubmitParam;
import com.differ.jackyun.omsapi.user.biz.domain.devops.data.MemberOrShopLevelTask;
import com.differ.jackyun.omsapi.user.biz.infrastructure.client.feign.api.internal.ApiStockFeignClient;
import com.differ.jackyun.omsapi.user.biz.infrastructure.common.LogAdapter;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.shop.response.ApiShopExtendMigrationResponseDTO;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.syncstock.request.ApiGoodsStockExtraRuleFlushRequestDTO;
import com.differ.jackyun.omsapibase.domain.core.DomainUtils;
import com.differ.jackyun.omsapibase.infrastructure.enums.LogTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.utils.ExtUtils;
import com.differ.jackyun.omsapibase.infrastructure.utils.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description 刷新商品级库存例外规则
 * <AUTHOR>
 * @Date 2023/6/20 14:18
 */
@Component
public class FlushGoodsStockExtraDevOpsFunc extends AbstractMemberOrShopLevelFunc {

    // region 变量

    /**
     * 日志标题
     */
    private static final String LOG_CAPTION = "刷新商品级库存例外规则";

    /**
     * Feign Client
     */
    @Autowired
    private ApiStockFeignClient stockFeignClient;

    // endregion

    // region 重写方法

    /**
     * 生成商品级库存例外规则任务业务数据
     *
     * @param param 提交参数
     * @param <T>   泛型
     * @return 结果
     */
    @Override
    protected <T extends MemberOrShopLevelSubmitParam> Object generateTaskBizData(T param) {
        return null;
    }

    /**
     * 执行单个任务执行
     *
     * @param task 任务
     */
    @Override
    protected void executeSingleTask(MemberOrShopLevelTask task) {

        // 会员名
        String memberName = task.getMemberName();

        // 集群号
        String groupNo = DomainUtils.getGroupNoOptimize(memberName);

        // 封装请求
        ApiGoodsStockExtraRuleFlushRequestDTO request = new ApiGoodsStockExtraRuleFlushRequestDTO();
        request.setMemberName(memberName);
        String requestStr = JsonUtils.toJson(request);

        // 执行请求
        JackYunResponse<ApiShopExtendMigrationResponseDTO> response = DomainUtil.doWithMemberFunc(memberName, memberInfo ->
                this.stockFeignClient.flushGoodsStockExtraRule(memberName, groupNo, requestStr));

        // 记录日志
        LogAdapter.writeSystemLog(memberName, LOG_CAPTION, () ->
                ExtUtils.stringBuilderAppend(String.format("执行完成，任务数据：%s，请求：%s，响应：%s",
                        JsonUtils.toJson(task), requestStr, JsonUtils.toJson(response))), LogTypeEnum.TRACE);
    }

    // endregion
}
