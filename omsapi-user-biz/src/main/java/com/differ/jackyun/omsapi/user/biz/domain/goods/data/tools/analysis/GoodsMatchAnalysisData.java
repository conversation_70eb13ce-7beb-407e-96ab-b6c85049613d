package com.differ.jackyun.omsapi.user.biz.domain.goods.data.tools.analysis;

import com.differ.jackyun.omsapibase.data.goods.bizgoods.ListOfBizGoodsSkuEntity;

/**
 * 网店商品匹配分析数据
 * <AUTHOR>
 * @since 2025-02-13 15:08
 **/
public class GoodsMatchAnalysisData extends BizMatchAnalysisData{

    // region 变量

    /**
     * 平台商品规格信息
     */
    private ListOfBizGoodsSkuEntity bizGoodsSkuEntity;

    // endregion

    // region getter/setter

    public ListOfBizGoodsSkuEntity getBizGoodsSkuEntity() {
        return bizGoodsSkuEntity;
    }

    public void setBizGoodsSkuEntity(ListOfBizGoodsSkuEntity bizGoodsSkuEntity) {
        this.bizGoodsSkuEntity = bizGoodsSkuEntity;
    }


    // endregion

}
