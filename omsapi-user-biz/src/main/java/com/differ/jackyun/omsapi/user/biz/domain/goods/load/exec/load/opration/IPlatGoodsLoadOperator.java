package com.differ.jackyun.omsapi.user.biz.domain.goods.load.exec.load.opration;

import com.differ.jackyun.omsapi.user.biz.domain.apicall.data.ApiCallResponse;
import com.differ.jackyun.omsapi.user.biz.domain.goods.load.data.child.task.GoodsLoadChildTask;
import com.differ.jackyun.omsapi.user.biz.domain.goods.load.data.context.GoodsLoadExecTaskContext;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.downloadproduct.PolyAPIBusinessDownloadProductResponseBizData;

/**
 * @Decription 网店商品下载-平台操作器
 * <AUTHOR>
 * @Date 2024-05-24 10:13
 **/
public interface IPlatGoodsLoadOperator {

    /**
     * 下载
     *
     * @param context   上下文
     * @param childTask 子任务
     * @return 返回结果
     */
    ApiCallResponse<PolyAPIBusinessDownloadProductResponseBizData> doLoad(final GoodsLoadExecTaskContext context, GoodsLoadChildTask childTask);
}
