package com.differ.jackyun.omsapi.user.biz.domain.goods.match.processor;

import com.differ.jackyun.omsapi.user.biz.domain.goods.match.data.*;

import java.util.Map;

/**
 * @Description 匹配的处理接口(匹配有商品匹配 ， 订单匹配 ， 售后匹配)
 * <AUTHOR>
 * @Date 2023/6/12 16:44
 */
public interface MatchProcessor {



    /**
     * 统一数据格式转换，内部做来源对象校验
     *
     * @param matchContext 匹配上下文
     * @param sourceGoods  源数据
     * @return 转换结果
     */
    MatchDataConvertResult convert(MatchContext matchContext, AbstractToMatchSourceGoods... sourceGoods);

    /**
     * 生成主GUID,对应商品匹配表的主GUID
     *
     * @param matchContext
     * @param sourceGoods
     * @return
     */
    String getMainGuid(BaseMatchContext matchContext, AbstractToMatchSourceGoods sourceGoods);

    /**
     * 生成主GUID,对应商品匹配表的主GUID
     *
     * @param matchContext
     * @param sourceGoods
     * @return
     */
    Map<AbstractToMatchSourceGoods, String> getMainGuids(BaseMatchContext matchContext, AbstractToMatchSourceGoods... sourceGoods);

    /**
     * 生成源GUID，对应数据源的业务GUID
     *
     * @param matchContext
     * @param sourceGoods
     * @return
     */
    String getSourceGuid(BaseMatchContext matchContext, AbstractToMatchSourceGoods sourceGoods);

    /**
     * 生成源GUID，对应数据源的业务GUID
     *
     * @param matchContext
     * @param sourceGoods
     * @return
     */
    Map<AbstractToMatchSourceGoods, String> getSourceGuids(BaseMatchContext matchContext, AbstractToMatchSourceGoods... sourceGoods);

    /**
     * 执行匹配业务逻辑
     *
     * @param matchContext
     * @param sourceGoods
     * @return
     */
    Map<AbstractToMatchSourceGoods, GoodsMatchResult> doMatch(MatchContext matchContext, AbstractToMatchSourceGoods... sourceGoods);

    /**
     * 执行匹配业务逻辑
     *
     * @param matchContext
     * @param sourceGoods
     * @return
     */
    Map<FormattedMatchGoods, GoodsMatchResult> doMatch(MatchContext matchContext, FormattedMatchGoods... sourceGoods);
}
