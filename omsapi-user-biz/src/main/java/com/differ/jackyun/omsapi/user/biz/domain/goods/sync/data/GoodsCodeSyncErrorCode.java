package com.differ.jackyun.omsapi.user.biz.domain.goods.sync.data;

import com.differ.jackyun.omsapibase.infrastructure.enums.core.INameValueEnum;

/**
 * 商品信息反写错误码
 *
 * <AUTHOR>
 * @since 2023-11-07 23:03
 */
public enum GoodsCodeSyncErrorCode implements INameValueEnum {

    /**
     * 数据错误
     */
    DATA_INVALID("0001", "数据错误"),

    /**
     * 商品编码或条码为空
     */
    CODE_EMPTY("00012", "商品编码或条码为空"),

    /**
     * 平台不支持
     */
    PLAT_NOT_SUPPORT("0003", "平台不支持"),

    /**
     * 非手动匹配，不进行回写
     */
    NOT_MANUAL_MATCH_SUPPORT("0004", "仅允许对手动匹配的sku进行回写"),

    ;

    GoodsCodeSyncErrorCode(String value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 值
     */
    private String value;

    /**
     * 描述
     */
    private String name;

    /**
     * 获取值。
     *
     * @return 值
     */
    @Override
    public String getValue() {
        return this.value;
    }

    /**
     * 获取标题。
     *
     * @return 标题
     */
    @Override
    public String getCaption() {
        return this.name;
    }

}
