package com.differ.jackyun.omsapi.user.biz.domain.load.v2.order.processor.mode;

import com.differ.jackyun.omsapi.user.biz.domain.apicall.data.ApiCallResponse;
import com.differ.jackyun.omsapi.user.biz.domain.load.v2.order.data.ChildLoadTask;
import com.differ.jackyun.omsapi.user.biz.domain.load.v2.order.data.LoadRunStatusEnum;
import com.differ.jackyun.omsapi.user.biz.domain.load.v2.order.data.OrderLoadContext;
import com.differ.jackyun.omsapi.user.biz.infrastructure.cache.local.plugins.AlarmIntervalLocalCache;
import com.differ.jackyun.omsapi.user.biz.infrastructure.common.LogAdapter;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.constants.LoadConstant;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.load.order.AbstractLoadArgs;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.enums.AlarmIntervalTypeEnum;
import com.differ.jackyun.omsapi.user.biz.infrastructure.utils.MonitorAlarmNoticeUtils;
import com.differ.jackyun.omsapi.user.biz.tasks.kafka.plugins.MonitorStatisticsKafkaProducer;
import com.differ.jackyun.omsapibase.infrastructure.enums.ConfigKeyEnum;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.getorder.PolyAPIBusinessGetOrderResponseBizData;
import com.differ.jackyun.omsapibase.infrastructure.utils.*;
import org.apache.commons.lang3.math.NumberUtils;

/**
 * @Description 增量分页下载
 * <AUTHOR> xgy
 * @Date 2021/12/24 12:08
 */
public class IncrementDescLoadMode extends AbstractIncrementPageMode {

    @Override
    public String modeName() {
        return "增量分页倒序下载";
    }

    /**
     * 保存第一页的操作,设置第一页的页码并且处理保存第一页的操作，正序和倒序时不同
     *
     * @param context       保存订单上下文
     * @param args          下载订单参数
     * @param childLoadTask 下载订单子任务
     * @param response      订单查询结果
     * @param numTotalOrder 总订单数
     * @return 返回总页数
     */
    @Override
    protected int dealFirstPageAndGetTotal(OrderLoadContext context, AbstractLoadArgs args, ChildLoadTask childLoadTask, ApiCallResponse<PolyAPIBusinessGetOrderResponseBizData> response, int numTotalOrder) {
        // 设置总记录
        childLoadTask.setDownloadTotalRecord(numTotalOrder);
        int totalPage = CoreUtils.calculateTotalPage(childLoadTask.getDownloadTotalRecord(), childLoadTask.getPageSize());
        if (context.getShopBaseInfo() == null || context.getShopBaseInfo().getShopType() == null) {
            return totalPage;
        }

        // 页码超限报警
        if (!YunConfigUtils.contains(context.getShopBaseInfo().getShopType().toString(), ConfigKeyEnum.LOAD_PAGE_INDEX_OVERLOAD_ALARM_EXCEPT_PLATS)) {
            int pageLimit = NumberUtils.toInt(YunConfigUtils.getDBConfig(ConfigKeyEnum.WORKER_DOWNLOADORDER_ALARM_PAGE_INDEX), 100);
            if (totalPage >= pageLimit) {
                String alarmText = String.format("会员：%s，%s，平台：%s，店铺名：%s，店铺id：%d，店铺修改时间：%s，触发报警页码：%d，当前任务总页数：%d，页大小：%d，触发方式：%s，下载订单类型：%s，下载订单状态：%s，下载订单时间范围：%s~%s",
                        context.getMemberName(), this.modeName(), context.getShopBaseInfo().getShopType().displayName(), context.getShopBaseInfo().getShopName(),
                        context.getShopId(), context.getShopBaseInfo().getGmtModified(), pageLimit, totalPage, childLoadTask.getPageSize(), args.getTriggerType().getCaption(),
                        childLoadTask.getDownloadOrderType().getCaption(), childLoadTask.getOrderStatus(), childLoadTask.getDownloadStartTime(), childLoadTask.getDownloadEndTime());
                AlarmIntervalLocalCache.get().alarmInterval(AlarmIntervalTypeEnum.LOAD_PAGE_INDEX_OVERLOAD, context.getMemberName(), alarmText);
                // 发送监控报警数据到kafka
                SpringResolveManager.resolve(MonitorStatisticsKafkaProducer.class).monitorAlarmSend(AlarmIntervalTypeEnum.LOAD_PAGE_INDEX_OVERLOAD, alarmText, context.getMemberName(), context.getShopBaseInfo().getShopType().getPlatValue(), totalPage);
            }
        }
        // 倒序，从第最后页开始
        childLoadTask.setPageIndex(totalPage);
        // 总页数为1页时，暂存菠萝派接口返回数据到子任务
        if (totalPage == 1 && childLoadTask.isSaveDirectOnTotalOne()) {
            // 直接保存入库
            boolean saveResult = savePage(context, args, childLoadTask, response);
            if (saveResult) {
                childLoadTask.setPageIndex(0);
            }
            // 更新任务缓存
            saveChildTaskPageStatus(context, args, childLoadTask);
            LogAdapter.writeBusinessLog(context.getMemberName(), LoadConstant.LOAD_TRACE_CAPTIONS, () -> ExtUtils.stringBuilderAppend("下载子任务,查总页数1，直接保存", saveResult ? "成功" : "失败", JsonUtils.toJson(childLoadTask)));
        }
        return totalPage;
    }


    /**
     * 迭代实现子任务是否有下一页
     *
     * @param context
     * @param args
     * @param childrenTask
     * @return
     */
    @Override
    protected boolean hasNextPage(OrderLoadContext context, AbstractLoadArgs args, ChildLoadTask childrenTask) {
        // 首页索引基于1，倒序有下一页：当前页>=1
        return childrenTask.getRunStatus() == LoadRunStatusEnum.RUNNING && childrenTask.getPageIndex() >= LoadConstant.MIN_PAGE_INDEX;
    }

    /**
     * 移到子任务的下一页
     *
     * @param context
     * @param args
     * @param childLoadTask
     */
    @Override
    protected void moveNextPage(OrderLoadContext context, AbstractLoadArgs args, ChildLoadTask childLoadTask) {
        // 循环条件：默认倒序的页码递减
        childLoadTask.setPageIndex(childLoadTask.getPageIndex() - 1);
    }

}
