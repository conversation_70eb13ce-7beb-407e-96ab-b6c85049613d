package com.differ.jackyun.omsapi.user.biz.domain.load.v2.order.processor.plat;

import com.differ.jackyun.omsapi.user.biz.domain.core.ApiShopCoreUtils;
import com.differ.jackyun.omsapi.user.biz.domain.load.v2.order.data.OrderLoadContext;
import com.differ.jackyun.omsapi.user.biz.domain.load.v2.order.processor.BaseLoadProcessor;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.load.order.AbstractLoadArgs;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.load.order.LoadArgsByOrderNo;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.enums.ApiShopTypeEnumV2;
import com.differ.jackyun.omsapibase.data.shopconf.extra.ShopCommonConfig;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.platfeatures.extra.PlatInfo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 慧买卖下载订单特殊处理
 *
 * <AUTHOR>
 * @since 2022-05-30 15:51:42
 */
public class HMMLoadOrderProcessor extends BaseLoadProcessor {

    /**
     * 获取店铺类型
     *
     * @return 店铺类型集合
     */
    @Override
    protected List<ApiShopTypeEnumV2> getApiShopType(OrderLoadContext context, PlatInfo platFeatures, AbstractLoadArgs args) {
        // 获取店铺通用设置
        ShopCommonConfig shopCommonConfig = ApiShopCoreUtils.getShopConfig(context.getMemberName(), context.getShopId(), ShopCommonConfig.class);
        List<ApiShopTypeEnumV2> lstApiShopType = new ArrayList<>();
        //按单号下载时,如果店铺有配置，则返回店铺配置的，如果店铺没有配置，则返回无类型
        if (LoadArgsByOrderNo.class.isAssignableFrom(args.getClass())) {
            if(null == shopCommonConfig || null == shopCommonConfig.getTradeType() || shopCommonConfig.getTradeType().length == 0) {
                return Collections.singletonList(ApiShopTypeEnumV2.DEFAULT);
            }
            lstApiShopType.addAll(ApiShopTypeEnumV2.getApiShopType(context.getShopBaseInfo().getShopType(), Arrays.asList(shopCommonConfig.getTradeType())));
            return lstApiShopType;
        }

        //按时间段下载时，如果店铺有配置，则返回店铺配置的,如果没有配置的话，则返回默认所有的配置
        if (null == shopCommonConfig || null == shopCommonConfig.getTradeType() || shopCommonConfig.getTradeType().length == 0) {
            lstApiShopType.addAll(ApiShopTypeEnumV2.getApiShopType(context.getShopBaseInfo().getShopType()));
        } else {
            lstApiShopType.addAll(ApiShopTypeEnumV2.getApiShopType(context.getShopBaseInfo().getShopType(), Arrays.asList(shopCommonConfig.getTradeType())));
        }

        return lstApiShopType;
    }
}
