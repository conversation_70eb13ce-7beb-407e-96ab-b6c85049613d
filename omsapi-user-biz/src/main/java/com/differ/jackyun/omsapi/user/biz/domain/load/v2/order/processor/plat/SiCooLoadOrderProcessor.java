package com.differ.jackyun.omsapi.user.biz.domain.load.v2.order.processor.plat;

import com.differ.jackyun.omsapi.user.biz.domain.core.ApiShopCoreUtils;
import com.differ.jackyun.omsapi.user.biz.domain.load.v2.order.data.OrderLoadContext;
import com.differ.jackyun.omsapi.user.biz.domain.load.v2.order.data.ChildLoadTask;
import com.differ.jackyun.omsapi.user.biz.domain.load.v2.order.processor.BaseLoadProcessor;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.load.order.AbstractLoadArgs;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.enums.ApiShopTypeEnumV2;
import com.differ.jackyun.omsapibase.data.shopconf.extra.ShopCommonConfig;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.getorder.extra.BusinessGetOrderRequest_OrderTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.platfeatures.extra.PlatInfo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description 寺库下载订单特殊处理
 * <AUTHOR>
 * @since 2022/6/6 11:57
 */
public class SiCooLoadOrderProcessor extends BaseLoadProcessor {

    @Override
    public List<ChildLoadTask> createAllChildTask(OrderLoadContext context, AbstractLoadArgs args) {
        List<ChildLoadTask> childrenTasks = new ArrayList<>();
        // 获取平台特性
        PlatInfo platFeatures = getPlatFeatures(context);
        if (null == platFeatures) {
            log.error(String.format("%s平台特性为空", context.getShopBaseInfo().getShopType()));
            return new ArrayList<>();
        }

        // 循环订单类型和订单状态来创建子任务
        List<BusinessGetOrderRequest_OrderTypeEnum> orderTypeList = new ArrayList<>();
        orderTypeList.add(BusinessGetOrderRequest_OrderTypeEnum.JH_001);
        // 获取店铺通用设置
        ShopCommonConfig shopCommonConfig = ApiShopCoreUtils.getShopConfig(context.getMemberName(), context.getShopId(), ShopCommonConfig.class);
        if (null != shopCommonConfig && null != shopCommonConfig.getTradeType()
                && Arrays.asList(shopCommonConfig.getTradeType()).contains(BusinessGetOrderRequest_OrderTypeEnum.JH_005.toString())) {
            orderTypeList.add(BusinessGetOrderRequest_OrderTypeEnum.JH_005);
        }

        for (BusinessGetOrderRequest_OrderTypeEnum orderType : orderTypeList) {
            // 获取待下载的订单状态
            List<String> allOrderStatus = getOrderStatus(context, ApiShopTypeEnumV2.DEFAULT, platFeatures, args);
            for (String orderStatus : allOrderStatus) {
                // 创建子任务
                ChildLoadTask basiChildTask = createBasiChildTask(context, args, ApiShopTypeEnumV2.DEFAULT, orderStatus, platFeatures);
                basiChildTask.setOrderType(orderType);
                basiChildTask.setExtra(orderType.toString());
                childrenTasks.add(basiChildTask);
            }
        }

        return childrenTasks;
    }
}
