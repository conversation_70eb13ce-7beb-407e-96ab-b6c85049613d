package com.differ.jackyun.omsapi.user.biz.domain.match.processor.strategy.match;

import com.differ.jackyun.omsapi.user.biz.domain.match.data.*;
import com.differ.jackyun.omsapi.user.biz.domain.match.processor.strategy.IMatchStrategy;
import com.differ.jackyun.omsapibase.data.goodsmatch.MatchCodeQueryItem;
import com.differ.jackyun.omsapibase.data.goodsmatch.MatchGoodsResult;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 条码匹配策略
 *
 * <AUTHOR> chuff
 * @Date 2021-05-14 11:35
 * @Decription
 **/
@Component("BarcodeMatchStrategy")
public class BarcodeMatchStrategy extends BaseMatchStrategy implements IMatchStrategy {

    //region 变量

    //endregion

    //region 接口方法实现

    /**
     * 商品匹配
     *
     * @param goodsItems 匹配数据集合
     * @return 匹配结果
     */
    @Override
    public MatchGoodsResult match(final MatchContext context, Map<String, MatchCodeQueryItem> goodsItems) {
        return null;
    }
    //endregion

}
