package com.differ.jackyun.omsapi.user.biz.domain.match.processor.visitor.plats;

import com.alibaba.fastjson.JSONObject;
import com.differ.jackyun.omsapi.user.biz.domain.match.data.MatchContext;
import com.differ.jackyun.omsapi.user.biz.domain.match.data.MatchPlatConvertResult;
import com.differ.jackyun.omsapi.user.biz.domain.match.processor.visitor.BaseRefreshGoodsMatchVisitor;
import com.differ.jackyun.omsapi.user.biz.domain.match.utils.GoodsMatchUtils;
import com.differ.jackyun.omsapibase.data.constant.OmsApiConstant;
import com.differ.jackyun.omsapibase.data.goods.bizgoods.ListOfBizGoodsSkuEntity;
import com.differ.jackyun.omsapibase.data.goodsmatch.ApiPlatGoodsMatchExtraEntity;
import com.differ.jackyun.omsapibase.infrastructure.utils.JsonUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * shopee刷新匹配访问者
 *
 * <AUTHOR>
 * @date 2023-09-07 13:41
 */
public class ShopeeRefreshGoodsMatchVisitor extends BaseRefreshGoodsMatchVisitor {

    public ShopeeRefreshGoodsMatchVisitor(MatchContext matchContext, List<ListOfBizGoodsSkuEntity> goodsItems) {
        super(matchContext, goodsItems);
    }

    @Override
    public MatchPlatConvertResult convertPlatMatchData(int index) {
        MatchPlatConvertResult matchPlatConvertResult = super.convertPlatMatchData(index);
        ListOfBizGoodsSkuEntity platGoodsItem = this.goodsItems.get(index);
        //扩展数据
        ApiPlatGoodsMatchExtraEntity extraEntity = matchPlatConvertResult.getExtraEntity();
        if (extraEntity == null) {
            extraEntity = new ApiPlatGoodsMatchExtraEntity();
            matchPlatConvertResult.setExtraEntity(extraEntity);
        }
        JSONObject skuExtra = JsonUtils.deJson(platGoodsItem.getSkuExtra());
        if (StringUtils.isNotEmpty(GoodsMatchUtils.getExtraValue(null, skuExtra, OmsApiConstant.WHSECODE))) {
            extraEntity.setWarehouseCode(GoodsMatchUtils.getExtraValue(null, skuExtra, OmsApiConstant.WHSECODE));
        }
        return matchPlatConvertResult;
    }
}
