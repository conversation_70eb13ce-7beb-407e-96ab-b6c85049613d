package com.differ.jackyun.omsapi.user.biz.domain.notify.sub.domain.tmallukura.goods.sync.plugins;

import com.differ.jackyun.omsapi.user.biz.domain.apicall.data.ApiCallHeader;
import com.differ.jackyun.omsapi.user.biz.domain.apicall.data.goods.request.PolyApiAssemblyGoodsSyncRequestBizData;
import com.differ.jackyun.omsapi.user.biz.domain.apicall.data.goods.response.PolyApiAssemblyGoodsSyncResponseBizData;
import com.differ.jackyun.omsapi.user.biz.domain.apicall.plugins.goods.AssemblyGoodsSyncApiCall;
import com.differ.jackyun.omsapi.user.biz.domain.apicall.plugins.goods.SingleGoodsSyncApiCall;
import com.differ.jackyun.omsapi.user.biz.domain.notify.sub.domain.tmallukura.goods.sync.BaseTuGoodsSyncProcess;
import com.differ.jackyun.omsapi.user.biz.domain.notify.sub.domain.tmallukura.goods.sync.result.impl.TUAssemblyGoodsSyncResultProcess;
import com.differ.jackyun.omsapi.user.biz.domain.notify.utils.TuGoodsSyncUtils;
import com.differ.jackyun.omsapi.user.biz.infrastructure.client.feign.ErpFeignClient;
import com.differ.jackyun.omsapi.user.biz.infrastructure.common.LogAdapter;
import com.differ.jackyun.omsapi.user.biz.infrastructure.condition.ConditionalOnSite;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.tmallukura.TuErpAssemblyDto;
import com.differ.jackyun.omsapi.user.biz.infrastructure.utils.TUCommonUtils;
import com.differ.jackyun.omsapibase.dao.jackyunmultidb.goods.ApiTUSysGoodsSyncResultDBSwitchMapper;
import com.differ.jackyun.omsapibase.data.common.YesOrNo;
import com.differ.jackyun.omsapibase.data.constant.OmsApiConstant;
import com.differ.jackyun.omsapibase.data.goods.tmallukura.TUSyncTypeEnum;
import com.differ.jackyun.omsapibase.domain.dataproxy.stock.AssemblyInfoProxy;
import com.differ.jackyun.omsapibase.infrastructure.constant.SiteTypeCodeConst;
import com.differ.jackyun.omsapibase.infrastructure.enums.LogTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.utils.CollectionsUtil;
import com.differ.jackyun.omsapibase.infrastructure.utils.CoreUtils;
import com.differ.jackyun.omsapibase.infrastructure.utils.ExtUtils;
import com.differ.jackyun.omsapibase.infrastructure.utils.YunConfigUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 天猫优仓货品同步-组合装同步
 *
 * <AUTHOR>
 * @date 2022/8/3 15:24
 */
@ConditionalOnSite(sites = {SiteTypeCodeConst.OMS_API_BUSINESS})
@Component("TuGoodsSyncAssemblyGoods")
public class TuGoodsSyncAssemblyGoods extends BaseTuGoodsSyncProcess {

    /**
     * 优仓结果mapper
     */
    @Autowired
    private ApiTUSysGoodsSyncResultDBSwitchMapper resultDBSwitchMapper;

    /**
     * erp feign
     */
    @Autowired
    private ErpFeignClient erpFeignClient;

    /**
     * 请求聚合同步(组合装)
     */
    @Autowired
    private AssemblyGoodsSyncApiCall assemblyGoodsSyncApiCall;
    /**
     * 请求聚合同步(单品)
     */
    @Autowired
    private SingleGoodsSyncApiCall singleGoodsSyncApiCall;

    @Autowired
    private AssemblyInfoProxy assemblyInfoProxy;

    /**
     * 天猫优仓货品同步请求结果执行器
     */
    @Autowired
    private TUAssemblyGoodsSyncResultProcess assemblyGoodsSyncResultProcess;

    /**
     * 手动处理流程
     *
     * @param memberName 会员名
     * @param shopId     店铺id
     */
    @Override
    public void sysGoodsManualProcess(String memberName, Long shopId, List<Long> skuIdList) {
        try {
            if (memberName == null || shopId == null || CollectionsUtil.isBlank(skuIdList)) {
                return;
            }

            // 未开启货品初始化业务，直接拦截
            if (!YunConfigUtils.enableTuGoodsInit(memberName)) {
                return;
            }

            // 通过系统规格id执行同步
            this.doProcessBySkuIdList(memberName, shopId, skuIdList);
        } catch (Exception ex) {
            LogAdapter.writeSystemLog(TUCommonUtils.EXCEPTION_LOG_TITLE, () ->
                    ExtUtils.stringBuilderAppend("天猫优仓组合装同步手动触发异常：", CoreUtils.exceptionToString(ex)), LogTypeEnum.ERROR);
        }
    }

    /**
     * 自动处理流程
     *
     * @param memberName 会员名
     * @param shopId     店铺id
     */
    @Override
    public void process(String memberName, Long shopId) {
        try {

            // 查询待执行的数据
            List<Long> assemblySkuIdList = TuGoodsSyncUtils.queryWaitExecuteSysGoods(memberName, shopId, YesOrNo.YES.getCode());

            // 已没有待执行的数据 判断有没有执行中的数据,并处理对应的会员key
            if (CollectionsUtil.isBlank(assemblySkuIdList)) {
                TuGoodsSyncUtils.memberCacheClean(memberName, shopId, YesOrNo.YES.getCode(), TUSyncTypeEnum.ASSEMBLY_GOODS_SYNC);
                return;
            }

            // 通过系统规格id执行同步
            this.doProcessBySkuIdList(memberName, shopId, assemblySkuIdList);
        } catch (Exception ex) {
            LogAdapter.writeSystemLog(TUCommonUtils.EXCEPTION_LOG_TITLE, () ->
                    ExtUtils.stringBuilderAppend("天猫优仓组合装同步执行异常：", CoreUtils.exceptionToString(ex)), LogTypeEnum.ERROR);
        }
    }

    // region 私有方法

    /**
     * 通过系统规格id执行同步
     *
     * @param memberName        会员名
     * @param shopId            店铺id
     * @param assemblySkuIdList 系统组合装规格Id
     */
    private void doProcessBySkuIdList(String memberName, Long shopId, List<Long> assemblySkuIdList) {

        // 请求ERP获取组合装信息
        List<TuErpAssemblyDto> assemblyInfoList = TuGoodsSyncUtils.getAssemblyRates(memberName, assemblySkuIdList);

        // 组合装商品同步
        this.assemblyGoodsSync(memberName, shopId, assemblyInfoList);
    }

    /**
     * 组合装商品同步
     *
     * @param memberName        会员名
     * @param shopId            店铺id
     * @param assemblySkuIdList 组合装详情
     */
    private void assemblyGoodsSync(String memberName, Long shopId, List<TuErpAssemblyDto> assemblySkuIdList) {
        // 将数据转换成请求数据
        List<PolyApiAssemblyGoodsSyncRequestBizData.AssemblyInfo> assemblyGoodsRequest = this.convertAssemblyGoodsData(assemblySkuIdList);
        if (CollectionsUtil.isBlank(assemblyGoodsRequest)) {
            return;
        }

        // 请求聚合并获取对应的结果 key -> requestId
        this.doPolyRequestAndGetRes(memberName, shopId, assemblyGoodsRequest);
    }

    /**
     * 请求聚合并获取对应的结果
     *
     * @param memberName   会员名
     * @param shopId       店铺Id
     * @param requestGoods 请求数据
     * @return 响应结果
     */
    private Map<String, List<PolyApiAssemblyGoodsSyncResponseBizData.ResGoodsInfo>> doPolyRequestAndGetRes(String memberName, Long shopId,
                                                                                                           List<PolyApiAssemblyGoodsSyncRequestBizData.AssemblyInfo> requestGoods) {
        // 20个一组拆分
        List<List<PolyApiAssemblyGoodsSyncRequestBizData.AssemblyInfo>> splitGoodsList = CollectionsUtil.subList(requestGoods, 20);
        if (CollectionsUtil.isBlank(splitGoodsList)) {
            return new HashMap<>(0);
        }

        // 返回的响应结果
        Map<String, List<PolyApiAssemblyGoodsSyncResponseBizData.ResGoodsInfo>> result = new HashMap<>();

        splitGoodsList.forEach(splitGoodsRequest -> {
            PolyApiAssemblyGoodsSyncRequestBizData requestBizData = new PolyApiAssemblyGoodsSyncRequestBizData();
            requestBizData.setCombinedInfos(splitGoodsRequest);
            requestBizData.setSubPlat(OmsApiConstant.JH_AOXIANG_AGENT);

            // 创建菠萝派日志id，长度与菠萝派保持一致，31位
            String polyContextId = CoreUtils.createUUIDText().substring(0, 31);
            // 预置同步结果、日志数据
            assemblyGoodsSyncResultProcess.initResultAndLog(memberName, shopId, polyContextId, requestGoods, YesOrNo.NO.getCode());
            // 封装请求头
            ApiCallHeader apiCallHeader = new ApiCallHeader(memberName, shopId, polyContextId);
            // 请求聚合
            assemblyGoodsSyncApiCall.call(apiCallHeader, requestBizData);

        });
        return result;
    }

    /**
     * 转换数据
     *
     * @param assemblyList 组合装信息
     * @return 请求数据
     */
    private List<PolyApiAssemblyGoodsSyncRequestBizData.AssemblyInfo> convertAssemblyGoodsData(List<TuErpAssemblyDto> assemblyList) {
        List<PolyApiAssemblyGoodsSyncRequestBizData.AssemblyInfo> result = new ArrayList<>();

        // 循环
        for (TuErpAssemblyDto assembly : assemblyList) {
            PolyApiAssemblyGoodsSyncRequestBizData.AssemblyInfo assemblyRequest = new PolyApiAssemblyGoodsSyncRequestBizData.AssemblyInfo();

            // 组合装信息赋值
            assemblyRequest.setErpStorageId(String.valueOf(assembly.getSkuId()));
            assemblyRequest.setName(assembly.getPackageGoodsName());
            assemblyRequest.setCode(assembly.getPackageGoodsNo());

            // 单品处理
            List<PolyApiAssemblyGoodsSyncRequestBizData.SingleInfo> singleList = new ArrayList<>();
            for (TuErpAssemblyDto.GoodsPackageInfoView single : assembly.getSubList()) {
                PolyApiAssemblyGoodsSyncRequestBizData.SingleInfo singleRequest = new PolyApiAssemblyGoodsSyncRequestBizData.SingleInfo();
                singleRequest.setErpStorageId(String.valueOf(single.getSkuId()));
                singleRequest.setQty(String.valueOf(single.getGoodsAmount()));
                singleList.add(singleRequest);
            }

            assemblyRequest.setGoods(singleList);
            result.add(assemblyRequest);
        }
        return result;
    }

    // endregion
}
