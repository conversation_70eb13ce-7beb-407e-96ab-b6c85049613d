package com.differ.jackyun.omsapi.user.biz.domain.postorder.processor.converter.packdata.tradeorder;

import com.alibaba.fastjson.TypeReference;
import com.differ.jackyun.omsapi.user.biz.domain.postorder.data.PostOrderContext;
import com.differ.jackyun.omsapi.user.biz.domain.postorder.data.TradeOriginalInfoPackage;
import com.differ.jackyun.omsapi.user.biz.domain.postorder.processor.converter.AbstractPostOrderConverter;
import com.differ.jackyun.omsapibase.data.open.oms.ExtraDto;
import com.differ.jackyun.omsapibase.data.open.oms.TradeOrderDto;
import com.differ.jackyun.omsapibase.data.open.oms.TradeOrderGoodsDto;
import com.differ.jackyun.omsapibase.data.open.omsonline.TradeOnlineGoods;
import com.differ.jackyun.omsapibase.data.open.omsonline.TradeOnlineGoodsExtraDto;
import com.differ.jackyun.omsapibase.infrastructure.utils.JsonUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 网店订单递交封装（定制信息）
 * @since 2025/1/9 15:38
 */
@Component
public class PostOrderCustomerInfoConverter extends AbstractPostOrderConverter {

    @Override
    public void packageOrder(PostOrderContext context, TradeOriginalInfoPackage originalInfo, TradeOrderDto tradeOrderDto) {
        ExtraDto extraDto = originalInfo.getTradeOnlineExtraDto();
        if (extraDto == null || StringUtils.isBlank(extraDto.getPlatOrderOriginalFieldMap())) {
            return;
        }

        Map<String, String> map = JsonUtils.deJson(extraDto.getPlatOrderOriginalFieldMap(), new TypeReference<Map<String, String>>(){});
        if (MapUtils.isEmpty(map)) {
            return;
        }

        tradeOrderDto.setPlatTradeDataMap(map);
    }

    /**
     * 封装销售单货品
     *
     * @param context          上下文
     * @param originalInfo     原始信息
     * @param tradeOnlineGoods 网店订单商品
     * @param tradeOrderDto    销售单DTO
     * @param tradeOrderGoods  销售单货品
     */
    @Override
    public void packageOrderGoods (PostOrderContext context, TradeOriginalInfoPackage originalInfo, TradeOnlineGoods tradeOnlineGoods, TradeOrderDto tradeOrderDto, TradeOrderGoodsDto tradeOrderGoods) {
        TradeOnlineGoodsExtraDto extraDto = tradeOnlineGoods.getSerialExtraDto();
        if (extraDto == null || StringUtils.isBlank(extraDto.getPlatGoodsOriginalFieldMap())) {
            return;
        }

        Map<String, String> map = JsonUtils.deJson(extraDto.getPlatGoodsOriginalFieldMap(), new TypeReference<Map<String, String>>(){});
        if (MapUtils.isEmpty(map)) {
            return;
        }

        tradeOrderGoods.setPlatGoodsDataMap(map);
    }
}
