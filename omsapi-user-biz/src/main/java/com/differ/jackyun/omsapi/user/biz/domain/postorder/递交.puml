'可访问性说明
'private -
'protect #
'package private ~
'public +
'其他参见plantuml类图说明：https://plantuml.com/zh/class-diagram

'库存同步类图设计
@startuml

'接口定义开始

interface IPostOrderProcessor <<递交处理接口>> {
    void postOrder() : 网店订单递交
}

interface IPostOrderConvert <<递交数据转换接口>> {
    List<ErrorCode> ignoreError() : 忽略的校验错误码
    boolean commonPostVerify() : 通用递交校验
    boolean onlyNormalPostVerify() : 校验数据
    void packageOrder() ： 封装订单数据
    void packageOrderGoods() ： 封装订单商品数据
    void packageOrderRelation() ： 封装订单关联数据
    void packageOrderAfter() : 封装订单数据后置
}

'类定义开始
class PostOrder <<递交领域事件>> {
}

abstract class BasePostOrderProcessor <<递交处理基类>> {
}

class PostOrderProcessTemplate <<递交处理模板>> {
    List<IPostOrderConvert> postOrderVerificationChain : 递交数据校验链
    List<IPostOrderConvert> postOrderPackageChain : 递交数据封装链
    IPostOrderConvert platSpecialConvert : 平台特殊转换
    void init() : 初始化
    void PostOrder() : 递交
}

class TaoBaoPostOrderProcessor <<递交处理-淘宝>> {
}

class PostOrderStatusVerification <<递交数据校验-订单状态>> {
}

class PostOrderGoodsVerification <<递交数据校验-订单商品>> {
}

class PostOrderPayPackage <<递交数据封装-支付相关>> {
}

'继承关系定义

'库存同步发起

IPostOrderProcessor <|.. BasePostOrderProcessor : 接口实现
IPostOrderConvert <|.. BasePostOrderProcessor : 接口实现

BasePostOrderProcessor <|-- TaoBaoPostOrderProcessor : 继承

IPostOrderConvert <|.. PostOrderStatusVerification : 接口实现
IPostOrderConvert <|.. PostOrderGoodsVerification : 接口实现
IPostOrderConvert <|.. PostOrderPayPackage : 接口实现

'其它关系定义
PostOrder ..> IPostOrderProcessor : 依赖（局部变量，工厂创建）
BasePostOrderProcessor --> PostOrderProcessTemplate : 关联（成员变量）
PostOrderProcessTemplate o--> IPostOrderConvert : 聚合（成员集合变量）

@enduml