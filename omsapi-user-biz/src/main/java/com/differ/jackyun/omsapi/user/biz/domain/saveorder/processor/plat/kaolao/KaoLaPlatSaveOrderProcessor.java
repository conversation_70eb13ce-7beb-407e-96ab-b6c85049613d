package com.differ.jackyun.omsapi.user.biz.domain.saveorder.processor.plat.kaolao;

import com.differ.jackyun.omsapi.user.biz.domain.saveorder.data.*;
import com.differ.jackyun.omsapi.user.biz.domain.saveorder.processor.BasePlatSaveOrderProcessor;
import com.differ.jackyun.omsapi.user.biz.domain.saveorder.processor.ISaveOrderProcessor;
import com.differ.jackyun.omsapi.user.biz.domain.saveorder.processor.composite.converter.EncryptOrderSpecialConverter;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.constants.ScopeTypeConstant;
import com.differ.jackyun.omsapibase.data.common.YesOrNo;
import com.differ.jackyun.omsapibase.data.open.oms.ExtraDto;
import com.differ.jackyun.omsapibase.data.open.omsonline.TradeOnline;
import com.differ.jackyun.omsapibase.data.open.omsonline.TradeOnlineGoods;
import com.differ.jackyun.omsapibase.data.shopconf.extra.ShopConfigExtra;
import com.differ.jackyun.omsapibase.infrastructure.enums.ConfigKeyEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.order.OrderOriginalFlagEnum;
import com.differ.jackyun.omsapibase.infrastructure.jackyunfrontapi.orderlist.OnlineCurStatus;
import com.differ.jackyun.omsapibase.infrastructure.jackyunfrontapi.orderlist.OnlineTradeStatus;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.getorder.extra.BusinessGetOrderResponseOrderItem;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.getorder.extra.BusinessGetOrderResponseOrderItemGoodInfo;
import com.differ.jackyun.omsapibase.infrastructure.utils.ExtUtils;
import com.differ.jackyun.omsapibase.infrastructure.utils.YunConfigUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 考拉-保存订单平台特殊处理
 * <AUTHOR>
 * @Date 2021/6/25 8:51
 **/
@Component("KaoLaPlatSaveOrderProcessor")
@Scope(ScopeTypeConstant.PROTOTYPE)
public class KaoLaPlatSaveOrderProcessor extends BasePlatSaveOrderProcessor {

    /**
     * 模板初始化特殊处理
     *
     * @return
     */
    @Override
    public ISaveOrderProcessor init() {
        super.init();
        // 密文处理
        template.getConvertComposite().addChildAfterExistsType(new KaoLaEncryptOrderSpecialConverter(), EncryptOrderSpecialConverter.class);
        return this;
    }

    @Override
    public ConvertOrderResult convertOrderItem(OrderSaveContext context, SourceOrderItem sourceOrderItem, TargetOrderItem targetOrderItem) {
        // region 变更订单处理状态、设置税额、设置外仓发货标记

        BusinessGetOrderResponseOrderItem orderItem = sourceOrderItem.getPlatOrderItem();

        TradeOnline tradeOnline = targetOrderItem.getTradeOnline();
        TradeOnline dbTradeOnline = sourceOrderItem.getExistsDBOrderItem() == null ? null : sourceOrderItem.getExistsDBOrderItem().getTradeOnline();

        /** 考虑交易关闭变为等待卖家发货，处理状态变为待转入审核 */
        if (null != dbTradeOnline && OnlineCurStatus.CLOSE.getValue().equals(dbTradeOnline.getCurStatus()) && OnlineTradeStatus.WAIT_SELLER_SEND_GOODS.getCode().equals(tradeOnline.getTradeStatus())) {
            tradeOnline.setCurStatus(OnlineCurStatus.WAIT_TRANSFER_TO_AUDIT.getValue());
        }

        /** 考拉税费返回在商品级，汇总放到网店订单税费 */
        BigDecimal taxAmount = BigDecimal.ZERO;
        int totalGoodsCount = orderItem.getGoodInfos().size();
        for (int i = 0; i < totalGoodsCount; i++) {
            taxAmount = taxAmount.add(ExtUtils.getNonEmpty(orderItem.getGoodInfos().get(i).getTaxAmount(), BigDecimal.ZERO));
        }
        tradeOnline.setTaxFee(taxAmount);

        /** 设置外仓发货标记 */
        ShopConfigExtra shopConfig = context.getShopConfig() == null ? null : context.getShopConfig().getShopConfObj();
        if (!targetOrderItem.getTradeOnlineFlags().stream().anyMatch(tradeOnlineFlag -> tradeOnlineFlag.getFlagId().longValue() == OrderOriginalFlagEnum.OUTSIDE_WAREHOUSE.getValue().longValue())
                && shopConfig != null && shopConfig.getOrderAutoFlowExtra() != null
                && YesOrNo.YES.getCode().equals(shopConfig.getOrderAutoFlowExtra().getAutoFlowSwitch(shopConfig.getDeliverySync()))
                && shopConfig.getOrderAutoFlowExtra().getAutoFlowWarehouseCodeList(shopConfig.getDeliverySync()) != null) {

            if (!StringUtils.isEmpty(orderItem.getWhseCode()) && shopConfig.getOrderAutoFlowExtra().getAutoFlowWarehouseCodeList(shopConfig.getDeliverySync()).stream().anyMatch(wareHouseCode -> orderItem.getWhseCode().equals(wareHouseCode))) {
                targetOrderItem.addFlag(OrderOriginalFlagEnum.convert2TradeOnlineFlag(OrderOriginalFlagEnum.OUTSIDE_WAREHOUSE));
            }
        }

        // endregion

        // 处理扩展信息
        this.convertOrderExtraItem(orderItem, targetOrderItem);

        // 保存终端网店单号到terminal_no字段
        if (YunConfigUtils.contains(context.getShopConfig().getShopType().toString(), ConfigKeyEnum.POST_ORDERNUMBER_TO_CUSTOMERTRADENO)) {
            tradeOnline.setTerminalNo(orderItem.getFxtId());
        }

        return super.convertOrderItem(context, sourceOrderItem, targetOrderItem);
    }

    @Override
    public ConvertGoodsResult convertGoodsItem(OrderSaveContext context, SourceOrderItem sourceOrderItem, SourceGoodsItem sourceGoodsItem, TargetOrderItem targetOrderItem, TradeOnlineGoods targetGoodsItem) {
        // region 设置商品实付金额

        /** 考拉货品单价存在四舍五入的情况，导致根据单价*数量计算的总金额有问题，所以直接用平台返回的商品实付金额 */
        BusinessGetOrderResponseOrderItemGoodInfo goodInfo = sourceGoodsItem.getPlatGoodsInfo();
        if (goodInfo.getGoodsBuyerPayment() != null && goodInfo.getGoodsBuyerPayment().compareTo(BigDecimal.ZERO) > 0) {
            targetGoodsItem.setSellTotal(goodInfo.getGoodsBuyerPayment());
        }

        // endregion
        return super.convertGoodsItem(context, sourceOrderItem, sourceGoodsItem, targetOrderItem, targetGoodsItem);
    }

    /**
     * 处理扩展信息
     *
     * @param order           聚合返回报文
     * @param targetOrderItem 目标保存订单数据
     */
    private void convertOrderExtraItem(BusinessGetOrderResponseOrderItem order, TargetOrderItem targetOrderItem) {

        TradeOnlineExtraItem tradeOnlineExtraItem = targetOrderItem.getTradeOnlineExtraItem();

        ExtraDto extraDto = tradeOnlineExtraItem.getExtraDto();
        extraDto.setFxtId(order.getFxtId());

        tradeOnlineExtraItem.setNeedSave(true);
    }
}
