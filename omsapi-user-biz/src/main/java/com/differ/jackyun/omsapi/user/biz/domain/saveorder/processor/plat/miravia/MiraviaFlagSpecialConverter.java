package com.differ.jackyun.omsapi.user.biz.domain.saveorder.processor.plat.miravia;

import com.differ.jackyun.omsapi.user.biz.domain.saveorder.data.*;
import com.differ.jackyun.omsapi.user.biz.domain.saveorder.processor.composite.converter.FlagSpecialConverter;
import com.differ.jackyun.omsapibase.data.open.omsonline.TradeOnlineGoods;
import com.differ.jackyun.omsapibase.infrastructure.enums.order.OrderOriginalFlagEnum;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.getorder.extra.BusinessGetOrderResponseOrderItemGoodInfo;
import com.differ.jackyun.omsapibase.infrastructure.utils.CollectionsUtil;

import java.util.List;

/**
 * Miravia标记特殊处理
 *
 * <AUTHOR>
 * @date 2023-12-05 18:09
 */
public class MiraviaFlagSpecialConverter extends FlagSpecialConverter {

    @Override
    public boolean supportGoodsConvert() {
        return true;
    }

    /**
     * 订单商品特殊处理
     *
     * @param context         订单保存上下文
     * @param sourceOrderItem 原始订单数据
     * @param sourceGoodsItem 原始商品数据
     * @param targetOrderItem 待入库订单数据
     * @param targetGoodsItem 待入库商品数据
     * @return
     */
    @Override
    public ConvertGoodsResult convertGoodsItem(final OrderSaveContext context, final SourceOrderItem sourceOrderItem, final SourceGoodsItem sourceGoodsItem, final TargetOrderItem targetOrderItem, TradeOnlineGoods targetGoodsItem) {

        ConvertGoodsResult result = super.convertGoodsItem(context, sourceOrderItem, sourceGoodsItem, targetOrderItem, targetGoodsItem);

        List<BusinessGetOrderResponseOrderItemGoodInfo.GoodsTag> goodsTags = sourceGoodsItem.getPlatGoodsInfo().getGoodsTags();

        // 判断是否全款预售订单，任一商品为全款预售，则标记订单为全款预售订单
        boolean isFullPreOrder = false;
        if (CollectionsUtil.isNotBlank(goodsTags)) {
            isFullPreOrder = goodsTags.stream().anyMatch(goodsTag -> "PreSale".equals(goodsTag.getKey()) || "PreOrder".equals(goodsTag.getKey()));
        }

        if (isFullPreOrder) {
            targetOrderItem.addFlagIfNoExists(OrderOriginalFlagEnum.convert2TradeOnlineFlag(OrderOriginalFlagEnum.FULL_YS));
        }

        return result;
    }
}