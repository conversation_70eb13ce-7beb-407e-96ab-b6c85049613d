package com.differ.jackyun.omsapi.user.biz.domain.sendorder.processor.plat;

import com.differ.jackyun.omsapi.user.biz.domain.sendorder.data.SendEnhancedPackage;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.data.SendOrderContext;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.data.SendOrderDataBase;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.processor.BaseSendProcessor;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.strategy.extra.ISendExtraStrategy;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.strategy.extra.plugins.SendWithTradeOrderInfoStrategy;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.constants.SaveOrderConstant;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.enums.OrderSendErrorCode;
import com.differ.jackyun.omsapibase.data.constant.OmsApiConstant;
import com.differ.jackyun.omsapibase.data.open.omsonline.TradeOnlineGoods;
import com.differ.jackyun.omsapibase.data.open.omsonline.TradeOnlineGoodsExtraDto;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.utils.TradeOrderUtils;
import com.differ.jackyun.omsapibase.data.common.YesOrNo;
import com.differ.jackyun.omsapibase.data.shopconf.extra.DeliverySyncExtra;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.batchsend.PolyAPIBusinessSendRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.utils.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Set;

import java.util.List;

/**
 * 抖音小时达
 *
 * <AUTHOR>
 * @since 2023-12-13 16:58:42
 */
@Lazy
@Component("DouYinXiaoShiDaSendProcessor")
public class DouYinXiaoShiDaSendProcessor extends BaseSendProcessor {

    /**
     * 订单级别发货请求参数封装
     *
     * @param context         上下文
     * @param metadata        元数据
     * @param enhancedPackage 增强数据封装
     * @param requestOrder    订单请求
     */
    @Override
    public void specialPackOrder(SendOrderContext context, SendOrderDataBase metadata, SendEnhancedPackage enhancedPackage, PolyAPIBusinessSendRequestBizData.PolyAPIBusinessOrderInfo requestOrder) {

        if (null != metadata.getTradeOnlineExtraDto() && StringUtils.isNotBlank(metadata.getTradeOnlineExtraDto().getShopId())) {
            requestOrder.setShopId(metadata.getTradeOnlineExtraDto().getShopId());
        }
        //发货回传平台地址id
        setAddressInfo(context, metadata, requestOrder);
    }

    /**
     * 设置平台发货地址信息（根据仓库id查询匹配关系）
     *
     * @param context      上下文
     * @param metadata     发货基础数据
     * @param requestOrder 订单请求
     */
    private void setAddressInfo(SendOrderContext context, SendOrderDataBase metadata, PolyAPIBusinessSendRequestBizData.PolyAPIBusinessOrderInfo requestOrder) {
        // 未开启配置
        DeliverySyncExtra deliveryConfig = context.getDeliveryConfig();
        if (!YesOrNo.YES.getCode().equals(deliveryConfig.getDeliveryMappingWarehouseSwitch())) {
            return;
        }
        // 未配置仓库
        List<DeliverySyncExtra.DeliveryMappingWareHouse> deliveryMappingWareHouseList = deliveryConfig.getDeliveryMappingWareHouses();
        if (CollectionUtils.isEmpty(deliveryMappingWareHouseList)) {
            return;
        }

        // 获取关联销售单的仓库id
        List<Long> relateOrderWarehouse = TradeOrderUtils.getTradeOrderRelateWarehouse(context, metadata, requestOrder);
        if (CollectionUtils.isEmpty(relateOrderWarehouse)) {
            return;
        }

        // 根据销售单那边的仓库id，去店铺配置缓存中查询对应的发货地址
        String locationId = deliveryMappingWareHouseList.stream().filter(deliveryMappingWareHouse -> relateOrderWarehouse.contains(deliveryMappingWareHouse.getWarehouseId()))
                .map(DeliverySyncExtra.DeliveryMappingWareHouse::getLocationId).findFirst().orElse(null);
        requestOrder.setSendAddressId(locationId);
    }

    /**
     * 订单商品特殊参数封装
     *
     * @param context           上下文
     * @param metadata          元数据
     * @param enhancedPackage   增强数据封装
     * @param tradeOnlineGoods  网店订单商品
     * @param requestOrderGoods 订单商品请求
     */
    @Override
    public void specialPackOrderGoods(SendOrderContext context, SendOrderDataBase metadata, SendEnhancedPackage enhancedPackage, TradeOnlineGoods tradeOnlineGoods, PolyAPIBusinessSendRequestBizData.PolyAPIBusinessGoodsInfo requestOrderGoods) {

        // 商品扩展信息
        TradeOnlineGoodsExtraDto goodsExtraDto = JsonUtils.deJson(tradeOnlineGoods.getExtra(), TradeOnlineGoodsExtraDto.class);
        // 商品平台标记列表，保存订单时设置的
        Set<String> platFlagCommon = goodsExtraDto.getPlatFlagCommon();

        requestOrderGoods.setBarCode(OmsApiConstant.EMPTY_STR);

        // 设置69码
        if (platFlagCommon.contains(SaveOrderConstant.JH_CODE_69)) {
            if (StringUtils.isBlank(goodsExtraDto.getBarcode())) {
                enhancedPackage.forbidSend(OrderSendErrorCode.CODE_69_NUMBER_ERROP_WHEN_SEND);
                return;
            }
            requestOrderGoods.setBarCode(goodsExtraDto.getBarcode());
        }
    }

    @Override
    protected List<ISendExtraStrategy> getExtraStrategies(SendOrderContext context, List<SendOrderDataBase> sendOrders) {
        return Collections.singletonList(new SendWithTradeOrderInfoStrategy());
    }
}
