package com.differ.jackyun.omsapi.user.biz.domain.sendorder.processor.plat;

import com.differ.jackyun.omsapi.user.biz.domain.sendorder.data.SendEnhancedPackage;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.data.SendOrderContext;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.data.SendOrderDataBase;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.data.sendback.SendBackData_DaLing;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.data.sendback.SendBackData_SFXDJ;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.processor.BaseSendProcessor;
import com.differ.jackyun.omsapibase.data.open.oms.ExtraDto;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.batchsend.PolyAPIBusinessSendRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.utils.JsonUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 顺丰大当家发货处理
 *
 * <AUTHOR>
 * @since 2022-06-27 14:06
 */
@Lazy
@Component("SFDDJSendProcessor")
public class SFDDJSendProcessor extends BaseSendProcessor {

    /**
     * 订单请求特殊处理
     *
     * @param context         上下文
     * @param metadata        元数据
     * @param enhancedPackage 增强数据封装
     * @param requestOrder    订单请求
     */
    @Override
    public void specialPackOrder(
            SendOrderContext context,
            SendOrderDataBase metadata,
            SendEnhancedPackage enhancedPackage,
            PolyAPIBusinessSendRequestBizData.PolyAPIBusinessOrderInfo requestOrder
    ) {
        if (metadata.getTradeOnlineExtra() == null || metadata.getTradeOnlineExtra().getExtraData() == null) {
            return;
        }
        ExtraDto extraDto = JsonUtils.deJson(metadata.getTradeOnlineExtra().getExtraData(), ExtraDto.class);
        if (extraDto == null) {
            return;
        }
        SendBackData_SFXDJ sendBackData = JsonUtils.deJson(extraDto.getSendBackData(), SendBackData_SFXDJ.class);
        if (sendBackData == null) {
            return;
        }
        requestOrder.setShopType(sendBackData.getShopType());
    }

}
