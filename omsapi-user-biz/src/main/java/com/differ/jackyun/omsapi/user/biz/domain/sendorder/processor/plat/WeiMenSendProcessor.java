package com.differ.jackyun.omsapi.user.biz.domain.sendorder.processor.plat;

import com.differ.jackyun.omsapi.user.biz.domain.sendorder.data.SendEnhancedPackage;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.data.SendOrderContext;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.data.SendOrderDataBase;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.data.sendback.SendBackData_WeiMob;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.processor.BaseSendProcessor;
import com.differ.jackyun.omsapibase.data.open.oms.ExtraDto;
import com.differ.jackyun.omsapibase.infrastructure.component.mq.data.worker.core.ext.ShopTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.component.mq.data.worker.core.ext.ShopTypeMappingEnum;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.batchsend.PolyAPIBusinessSendRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.core.PolyAPIPlatEnum;
import com.differ.jackyun.omsapibase.infrastructure.utils.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description 微盟发货处理
 * <AUTHOR>
 * @since 2022/6/27 17:30
 */
@Lazy
@Component("WeiMenSendProcessor")
public class WeiMenSendProcessor extends BaseSendProcessor {

    @Override
    public void specialPackOrder(SendOrderContext context, SendOrderDataBase metadata, SendEnhancedPackage enhancedPackage, PolyAPIBusinessSendRequestBizData.PolyAPIBusinessOrderInfo requestOrder) {
        if (null != metadata.getTradeOnlineExtra() && StringUtils.isNotBlank(metadata.getTradeOnlineExtra().getExtraData())) {
            ExtraDto extraDto = JsonUtils.deJson(metadata.getTradeOnlineExtra().getExtraData(), ExtraDto.class);
            if (extraDto != null && StringUtils.isNotBlank(extraDto.getSendBackData())) {
                SendBackData_WeiMob sendBackData = JsonUtils.deJson(extraDto.getSendBackData(), SendBackData_WeiMob.class);
                if (sendBackData != null) {
                    // 查询微盟平台所有的店铺类型枚举。
                    List<ShopTypeEnum> lstShopType = ShopTypeMappingEnum.getPolyShopType(PolyAPIPlatEnum.BUSINESS_WMWP);
                    // 根据回传参数中店铺类型名称查找店铺类型值。
                    lstShopType.forEach(shopTyp -> {
                        if (shopTyp.getCaption().equalsIgnoreCase(sendBackData.getShopType())) {
                            requestOrder.setShopType(shopTyp.toString());
                        }
                    });
                }
            }
        }
    }
}
