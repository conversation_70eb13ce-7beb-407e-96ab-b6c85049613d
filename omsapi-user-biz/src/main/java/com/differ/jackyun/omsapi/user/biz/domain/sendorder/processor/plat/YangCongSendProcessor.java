package com.differ.jackyun.omsapi.user.biz.domain.sendorder.processor.plat;

import com.differ.jackyun.omsapi.user.biz.domain.sendorder.data.SendEnhancedPackage;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.data.SendOrderContext;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.data.SendOrderDataBase;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.processor.BaseSendProcessor;
import com.differ.jackyun.omsapibase.data.open.omsonline.TradeOnlineGoods;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.batchsend.PolyAPIBusinessSendRequestBizData;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 洋葱发货处理
 * @since 2022/8/4 14:14
 */
@Lazy
@Component("YangCongSendProcessor")
public class YangCongSendProcessor extends BaseSendProcessor {

    /**
     * 发货前校验或特殊处理
     *
     * @param context         上下文
     * @param metadata        元数据
     * @param enhancedPackage 增强数据封装
     */
    @Override
    protected void verify(SendOrderContext context, SendOrderDataBase metadata, SendEnhancedPackage enhancedPackage) {
        // 整单发货传商品信息
        enhancedPackage.getSpecialRule().setNeedGoodsWhenWholeSend(true);
    }

    @Override
    public void specialPackOrderGoods(SendOrderContext context, SendOrderDataBase metadata, SendEnhancedPackage enhancedPackage, TradeOnlineGoods tradeOnlineGoods, PolyAPIBusinessSendRequestBizData.PolyAPIBusinessGoodsInfo requestOrderGoods) {
        requestOrderGoods.setSkuId(tradeOnlineGoods.getPlatSkuId());
    }
}
