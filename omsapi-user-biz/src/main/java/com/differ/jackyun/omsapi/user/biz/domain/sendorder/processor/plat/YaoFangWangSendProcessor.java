package com.differ.jackyun.omsapi.user.biz.domain.sendorder.processor.plat;

import com.differ.jackyun.omsapi.user.biz.domain.sendorder.data.SendEnhancedPackage;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.data.SendOrderContext;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.data.SendOrderDataBase;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.processor.BaseSendProcessor;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.strategy.extra.ISendExtraStrategy;
import com.differ.jackyun.omsapi.user.biz.domain.sendorder.strategy.extra.plugins.SendWithBatchInfoStrategy;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.enums.OrderSendErrorCode;
import com.differ.jackyun.omsapibase.data.open.omsonline.TradeOnlineGoods;
import com.differ.jackyun.omsapibase.data.open.omsonline.TradeOrderGoodsBatchInfo;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.batchsend.PolyAPIBusinessSendRequestBizData;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 药房网发货处理策略
 * @since 2022/7/25 17:18
 */
@Lazy
@Component("YaoFangWangSendProcessor")
public class YaoFangWangSendProcessor extends BaseSendProcessor {

    /**
     * 获取发货额外策略（普通发货用）
     *
     * @param context 发货上下文
     * @param sendOrders 发货元数据列表
     * @return 发货额外策略
     */
    @Override
    protected List<ISendExtraStrategy> getExtraStrategies(SendOrderContext context, List<SendOrderDataBase> sendOrders) {
        return Collections.singletonList(new SendWithBatchInfoStrategy());
    }

    /**
     * 发货前校验或特殊处理
     *
     * @param context         上下文
     * @param metadata        元数据
     * @param enhancedPackage 增强数据封装
     */
    @Override
    protected void verify(SendOrderContext context, SendOrderDataBase metadata, SendEnhancedPackage enhancedPackage) {
        // 整单发货传商品信息
        enhancedPackage.getSpecialRule().setNeedGoodsWhenWholeSend(true);

        List<TradeOrderGoodsBatchInfo> tradeOrderGoodsRelateDeliveryList = metadata.getOrderGoodsBatchInfos();
        // 未查询到对应的生产日期返回
        if (CollectionUtils.isEmpty(tradeOrderGoodsRelateDeliveryList)) {
            enhancedPackage.forbidSend(OrderSendErrorCode.GOODS_MISSING_PRODUCT_DATA);
            return;
        }

        // 构建对应的goodsId+specId的map
        Map<String, List<TradeOrderGoodsBatchInfo>> goodsIdAndSpecIdMap = tradeOrderGoodsRelateDeliveryList.stream()
                .collect(Collectors.groupingBy(TradeOrderGoodsBatchInfo::splicingGoodsIdAndSpecId));

        // 判断查询的关联关系中是否有该货品，并且该货品的信息是否完全
        for (TradeOnlineGoods tradeOnlineGoods : metadata.getTradeOnlineGoodsList()) {
            String tempKey = StringUtils.join(tradeOnlineGoods.getSysGoodsId(), "_", tradeOnlineGoods.getSysSpecId());
            if (!goodsIdAndSpecIdMap.containsKey(tempKey) || doNotNeedSend(goodsIdAndSpecIdMap.get(tempKey))) {
                enhancedPackage.forbidSend(OrderSendErrorCode.GOODS_MISSING_PRODUCT_DATA);
                return;
            }
        }
    }

    @Override
    public void specialPackOrderGoods(SendOrderContext context, SendOrderDataBase metadata, SendEnhancedPackage enhancedPackage, TradeOnlineGoods tradeOnlineGoods, PolyAPIBusinessSendRequestBizData.PolyAPIBusinessGoodsInfo requestOrderGoods) {
        requestOrderGoods.setPlatProductId(tradeOnlineGoods.getPlatGoodsId());
    }

    private boolean doNotNeedSend(List<TradeOrderGoodsBatchInfo> tradeOrderGoodsRelateDeliverys) {
        for (TradeOrderGoodsBatchInfo tradeOrderGoodsRelateDelivery : tradeOrderGoodsRelateDeliverys) {
            if (null == tradeOrderGoodsRelateDelivery.getProductDate()) {
                return true;
            }
        }
        return false;
    }
}
