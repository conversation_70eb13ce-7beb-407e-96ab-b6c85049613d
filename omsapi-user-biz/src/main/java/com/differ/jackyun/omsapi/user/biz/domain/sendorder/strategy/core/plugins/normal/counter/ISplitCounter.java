package com.differ.jackyun.omsapi.user.biz.domain.sendorder.strategy.core.plugins.normal.counter;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 拆单计数器基类
 *
 * <AUTHOR>
 * @date 2022-04-17 23:45
 */
public interface ISplitCounter {

    /**
     * 计算发货数量
     *
     * @param subSourceTradeId 网店订单商品Id
     * @param stockCount       出库数量
     * @return 发货数量
     */
    BigDecimal calculateSendCount(Long subSourceTradeId, BigDecimal stockCount);

    /**
     * 是否全部商品将发货
     *
     * @return 结果
     */
    boolean isAllWillSend();

    /**
     * 获取剩余未发商品
     *
     * @return 结果（Key：网店订单商品Id, Value：剩余数量）
     */
    Map<Long, BigDecimal> getLeftoverUnSends();

    /**
     * 商品是否已发货完成
     *
     * @param sourceSubTradeId 网店订单商品id
     * @return 商品是否已发货完成
     */
    boolean isGoodsHasSend(Long sourceSubTradeId);

    /**
     * 获取商品剩余可发数量
     *
     * @param sourceSubTradeId 网店订单商品id
     * @return 商品剩余可发数量
     */
    BigDecimal getLeftCanSend(Long sourceSubTradeId);

    /**
     * 发出指定商品所有剩余数量
     *
     * @param subSourceTradeId 网店订单商品Id
     * @return 发出数量
     */
    BigDecimal sendAllLeft(Long subSourceTradeId);
}
