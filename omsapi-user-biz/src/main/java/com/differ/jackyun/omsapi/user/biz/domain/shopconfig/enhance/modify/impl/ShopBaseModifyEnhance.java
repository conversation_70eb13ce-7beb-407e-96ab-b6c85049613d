package com.differ.jackyun.omsapi.user.biz.domain.shopconfig.enhance.modify.impl;

import com.differ.jackyun.omsapi.user.biz.domain.shopconfig.data.ApiShopBaseComposite;
import com.differ.jackyun.omsapi.user.biz.domain.shopconfig.data.ShopConfigCommonResult;
import com.differ.jackyun.omsapi.user.biz.domain.shopconfig.data.enhance.ShopConfigPersistExtraParam;
import com.differ.jackyun.omsapi.user.biz.domain.shopconfig.data.enhance.ShopModifyResult;
import com.differ.jackyun.omsapi.user.biz.domain.shopconfig.enhance.modify.AbstractShopModify;
import com.differ.jackyun.omsapi.user.biz.domain.shopconfig.enhance.modify.IShopBaseModifyEnhance;
import com.differ.jackyun.omsapi.user.biz.domain.shopconfig.enhance.operation.impl.ShopBaseQueryOperation;
import com.differ.jackyun.omsapi.user.biz.domain.shopconfig.enhance.operation.impl.ShopBaseSaveEnhanceOperation;
import com.differ.jackyun.omsapi.user.biz.infrastructure.common.LogAdapter;
import com.differ.jackyun.omsapi.user.biz.infrastructure.compare.utils.ObjectExtUtils;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.shop.core.ApiShopAuthInfo;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.shop.core.ApiShopBaseInfo;
import com.differ.jackyun.omsapibase.infrastructure.enums.LogTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.utils.CoreUtils;
import com.differ.jackyun.omsapibase.infrastructure.utils.ExtUtils;
import com.differ.jackyun.omsapibase.infrastructure.utils.JsonUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 店铺基础信息修改增强
 *
 * <AUTHOR>
 * @date 2024-06-27 16:38
 */
public class ShopBaseModifyEnhance implements IShopBaseModifyEnhance {

    // region 变量 & 常量

    /**
     * 日志标题
     */
    private static final String LOG_CAP = "修改店铺配置";

    // endregion

    // region 公共方法

    /**
     * 构建实例
     *
     * @return 结果
     */
    public static IShopBaseModifyEnhance build() {
        return new ShopBaseModifyEnhance();
    }

    // endregion

    // region 接口实现

    /**
     * 修改店铺基础信息
     *
     * @param memberName 会员名
     * @param shopId     店铺 ID
     * @param modifyFunc 修改方法
     * @return 结果
     */
    @Override
    public ShopConfigCommonResult modifyBase(String memberName, Long shopId, AbstractShopModify<ApiShopBaseInfo> modifyFunc) {
        try {

            // 查询源配置
            ApiShopBaseInfo sourceConfig = ShopBaseQueryOperation.build().queryShopBase(memberName, shopId);
            if (sourceConfig == null) {
                return ShopConfigCommonResult.createFailed("未查询到店铺配置");
            }

            // 复制目标配置
            ApiShopBaseInfo targetConfig = ObjectExtUtils.serializableDeepClone(sourceConfig, sourceConfig.getClass());

            // 修改配置
            ShopModifyResult modifyRet = modifyFunc.modify(shopId, sourceConfig, targetConfig);
            if (modifyRet == null || !modifyRet.isSuccess()) {

                // 修改不成功：直接返回
                return ShopConfigCommonResult.createSuccess();
            }

            // 保存配置
            return ShopBaseSaveEnhanceOperation.build().saveShopBase(memberName, shopId, sourceConfig, targetConfig, modifyRet.getExtraParam());
        } catch (Exception e) {
            LogAdapter.writeSystemLog(memberName, LOG_CAP, () -> ExtUtils.stringBuilderAppend(String.format("修改店铺基础信息失败，会员：%s，店铺 ID：%s，异常：%s",
                    memberName, shopId, CoreUtils.exceptionToString(e))), LogTypeEnum.ERROR);

            return ShopConfigCommonResult.createFailed("修改店铺配置失败");
        }
    }

    /**
     * 修改店铺基础信息
     *
     * @param memberName 会员名
     * @param shopIds    店铺 ID
     * @param modifyFunc 修改方法
     * @return 结果
     */
    @Override
    public ShopConfigCommonResult modifyBase(String memberName, List<Long> shopIds, AbstractShopModify<ApiShopBaseInfo> modifyFunc) {
        try {

            // 查询源配置
            Map<Long, ApiShopBaseInfo> sourceConfigs = ShopBaseQueryOperation.build().queryShopBase(memberName, shopIds);

            // 目标配置
            Map<Long, ApiShopBaseInfo> targetConfigs = new HashMap<>();

            // 扩展参数
            Map<Long, ShopConfigPersistExtraParam> extraParams = new HashMap<>();
            for (Map.Entry<Long, ApiShopBaseInfo> entry : sourceConfigs.entrySet()) {

                // 店铺 ID
                Long shopId = entry.getKey();

                // 源店铺配置
                ApiShopBaseInfo sourceConfig = entry.getValue();

                // 复制目标配置
                ApiShopBaseInfo targetConfig = ObjectExtUtils.serializableDeepClone(sourceConfig, sourceConfig.getClass());

                // 修改配置
                ShopModifyResult modifyRet = modifyFunc.modify(shopId, sourceConfig, targetConfig);
                if (modifyRet == null || !modifyRet.isSuccess()) {
                    continue;
                }

                // 添加目标配置
                targetConfigs.put(shopId, targetConfig);

                // 添加扩展参数
                extraParams.put(shopId, modifyRet.getExtraParam());
            }

            // 保存配置
            return ShopBaseSaveEnhanceOperation.build().saveShopBase(memberName, sourceConfigs, targetConfigs, extraParams);
        } catch (Exception e) {
            LogAdapter.writeSystemLog(memberName, LOG_CAP, () -> ExtUtils.stringBuilderAppend(String.format("修改店铺基础信息失败，会员：%s，店铺 ID：%s，异常：%s",
                    memberName, JsonUtils.toJson(shopIds), CoreUtils.exceptionToString(e))), LogTypeEnum.ERROR);

            return ShopConfigCommonResult.createFailed("修改店铺配置失败");
        }
    }

    /**
     * 修改店铺授权信息
     *
     * @param memberName 会员名
     * @param shopId     店铺 ID
     * @param modifyFunc 修改方法
     * @return 结果
     */
    @Override
    public ShopConfigCommonResult modifyAuth(String memberName, Long shopId, AbstractShopModify<ApiShopAuthInfo> modifyFunc) {
        try {

            // 查询源配置
            ApiShopAuthInfo sourceConfig = ShopBaseQueryOperation.build().queryShopAuth(memberName, shopId);
            if (sourceConfig == null) {
                return ShopConfigCommonResult.createFailed("未查询到店铺配置");
            }

            // 复制目标配置
            ApiShopAuthInfo targetConfig = ObjectExtUtils.serializableDeepClone(sourceConfig, sourceConfig.getClass());

            // 修改配置
            ShopModifyResult modifyRet = modifyFunc.modify(shopId, sourceConfig, targetConfig);
            if (modifyRet == null || !modifyRet.isSuccess()) {

                // 修改不成功：直接返回
                return ShopConfigCommonResult.createSuccess();
            }

            // 保存配置
            return ShopBaseSaveEnhanceOperation.build().saveShopAuth(memberName, shopId, sourceConfig, targetConfig, modifyRet.getExtraParam());
        } catch (Exception e) {
            LogAdapter.writeSystemLog(memberName, LOG_CAP, () -> ExtUtils.stringBuilderAppend(String.format("修改店铺授权信息失败，会员：%s，店铺 ID：%s，异常：%s",
                    memberName, shopId, CoreUtils.exceptionToString(e))), LogTypeEnum.ERROR);

            return ShopConfigCommonResult.createFailed("修改店铺配置失败");
        }
    }

    /**
     * 修改店铺授权信息
     *
     * @param memberName 会员名
     * @param shopIds    店铺 ID
     * @param modifyFunc 修改方法
     * @return 结果
     */
    @Override
    public ShopConfigCommonResult modifyAuth(String memberName, List<Long> shopIds, AbstractShopModify<ApiShopAuthInfo> modifyFunc) {
        try {

            // 查询源配置
            Map<Long, ApiShopAuthInfo> sourceConfigs = ShopBaseQueryOperation.build().queryShopAuth(memberName, shopIds);

            // 目标配置
            Map<Long, ApiShopAuthInfo> targetConfigs = new HashMap<>();

            // 扩展参数
            Map<Long, ShopConfigPersistExtraParam> extraParams = new HashMap<>();
            for (Map.Entry<Long, ApiShopAuthInfo> entry : sourceConfigs.entrySet()) {

                // 店铺 ID
                Long shopId = entry.getKey();

                // 源店铺配置
                ApiShopAuthInfo sourceConfig = entry.getValue();

                // 复制目标配置
                ApiShopAuthInfo targetConfig = ObjectExtUtils.serializableDeepClone(sourceConfig, sourceConfig.getClass());

                // 修改配置
                ShopModifyResult modifyRet = modifyFunc.modify(shopId, sourceConfig, targetConfig);
                if (modifyRet == null || !modifyRet.isSuccess()) {
                    continue;
                }

                // 添加目标配置
                targetConfigs.put(shopId, targetConfig);

                // 添加扩展参数
                extraParams.put(shopId, modifyRet.getExtraParam());
            }

            // 保存配置
            return ShopBaseSaveEnhanceOperation.build().saveShopAuth(memberName, sourceConfigs, targetConfigs, extraParams);
        } catch (Exception e) {
            LogAdapter.writeSystemLog(memberName, LOG_CAP, () -> ExtUtils.stringBuilderAppend(String.format("修改店铺授权信息失败，会员：%s，店铺 ID：%s，异常：%s",
                    memberName, JsonUtils.toJson(shopIds), CoreUtils.exceptionToString(e))), LogTypeEnum.ERROR);

            return ShopConfigCommonResult.createFailed("修改店铺配置失败");
        }
    }

    /**
     * 修改店铺基础信息和授权信息
     *
     * @param memberName 会员名
     * @param shopId     店铺 ID
     * @param modifyFunc 修改方法
     * @return 结果
     */
    @Override
    public ShopConfigCommonResult modifyBaseAndAuth(String memberName, Long shopId, AbstractShopModify<ApiShopBaseComposite> modifyFunc) {
        try {

            // 查询源配置
            ApiShopBaseComposite sourceConfig = ShopBaseQueryOperation.build().queryShopBaseAndAuth(memberName, shopId);
            if (sourceConfig == null) {
                return ShopConfigCommonResult.createFailed("未查询到店铺配置");
            }

            // 复制目标配置
            ApiShopBaseComposite targetConfig = ObjectExtUtils.serializableDeepClone(sourceConfig, sourceConfig.getClass());

            // 修改配置
            ShopModifyResult modifyRet = modifyFunc.modify(shopId, sourceConfig, targetConfig);
            if (modifyRet == null || !modifyRet.isSuccess()) {

                // 修改不成功：直接返回
                return ShopConfigCommonResult.createSuccess();
            }

            // 保存配置
            return ShopBaseSaveEnhanceOperation.build().saveShopBaseAndAuth(memberName, shopId, sourceConfig, targetConfig, modifyRet.getExtraParam());
        } catch (Exception e) {
            LogAdapter.writeSystemLog(memberName, LOG_CAP, () -> ExtUtils.stringBuilderAppend(String.format("修改店铺基础信息和授权信息失败，会员：%s，店铺 ID：%s，异常：%s",
                    memberName, shopId, CoreUtils.exceptionToString(e))), LogTypeEnum.ERROR);

            return ShopConfigCommonResult.createFailed("修改店铺配置失败");
        }
    }

    /**
     * 修改店铺基础信息和授权信息
     *
     * @param memberName 会员名
     * @param shopIds    店铺 ID
     * @param modifyFunc 修改方法
     * @return 结果
     */
    @Override
    public ShopConfigCommonResult modifyBaseAndAuth(String memberName, List<Long> shopIds, AbstractShopModify<ApiShopBaseComposite> modifyFunc) {
        try {

            // 查询源配置
            Map<Long, ApiShopBaseComposite> sourceConfigs = ShopBaseQueryOperation.build().queryShopBaseAndAuth(memberName, shopIds);

            // 目标配置
            Map<Long, ApiShopBaseComposite> targetConfigs = new HashMap<>();

            // 扩展参数
            Map<Long, ShopConfigPersistExtraParam> extraParams = new HashMap<>();
            for (Map.Entry<Long, ApiShopBaseComposite> entry : sourceConfigs.entrySet()) {

                // 店铺 ID
                Long shopId = entry.getKey();

                // 源店铺配置
                ApiShopBaseComposite sourceConfig = entry.getValue();

                // 复制目标配置
                ApiShopBaseComposite targetConfig = ObjectExtUtils.serializableDeepClone(sourceConfig, sourceConfig.getClass());

                // 修改配置
                ShopModifyResult modifyRet = modifyFunc.modify(shopId, sourceConfig, targetConfig);
                if (modifyRet == null || !modifyRet.isSuccess()) {
                    continue;
                }

                // 添加目标配置
                targetConfigs.put(shopId, targetConfig);

                // 添加扩展参数
                extraParams.put(shopId, modifyRet.getExtraParam());
            }

            // 保存配置
            return ShopBaseSaveEnhanceOperation.build().saveShopBaseAndAuth(memberName, sourceConfigs, targetConfigs, extraParams);
        } catch (Exception e) {
            LogAdapter.writeSystemLog(memberName, LOG_CAP, () -> ExtUtils.stringBuilderAppend(String.format("修改店铺基础信息和授权信息失败，会员：%s，店铺 ID：%s，异常：%s",
                    memberName, JsonUtils.toJson(shopIds), CoreUtils.exceptionToString(e))), LogTypeEnum.ERROR);

            return ShopConfigCommonResult.createFailed("修改店铺配置失败");
        }
    }

    // endregion
}
