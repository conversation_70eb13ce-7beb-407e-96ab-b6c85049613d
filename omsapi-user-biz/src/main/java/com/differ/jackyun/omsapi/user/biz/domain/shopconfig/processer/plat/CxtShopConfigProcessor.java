package com.differ.jackyun.omsapi.user.biz.domain.shopconfig.processer.plat;

import com.differ.jackyun.omsapi.user.biz.domain.shopconfig.data.ApiShopConfigComposite;
import com.differ.jackyun.omsapi.user.biz.domain.shopconfig.data.ShopConfigContext;
import com.differ.jackyun.omsapi.user.biz.domain.shopconfig.data.ShopConfigVerifyResult;
import com.differ.jackyun.omsapi.user.biz.domain.shopconfig.data.ShopGrayConfig;
import com.differ.jackyun.omsapi.user.biz.domain.shopconfig.processer.AbstractShopConfigProcessor;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.enums.ApiShopTypeEnumV2;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.enums.ShopConfigErrorCode;
import org.elasticsearch.common.util.CollectionUtils;

/**
 * 店铺配置处理-诚信通
 *
 * <AUTHOR>
 * @date 2022-07-07 17:00
 */
public class CxtShopConfigProcessor extends AbstractShopConfigProcessor {

    // region 变量

    /**
     * 默认下载类型
     */
    private final String[] defaultTradeType = new String[]{ApiShopTypeEnumV2.CXT_GN.getApiShopType()};

    // endregion

    // region 重写基类方法

    /**
     * 定义初始配置
     *
     * @param context    上下文
     * @param shopConfig 店铺配置
     */
    @Override
    public void defineInitialConfig(ShopConfigContext context, ApiShopConfigComposite shopConfig) {

        // 默认下载类型
        shopConfig.getCommonConfig().setTradeType(defaultTradeType);
    }

    /**
     * 查询后处理
     *
     * @param context    上下文
     * @param shopConfig 店铺配置
     * @param grayConfig 灰度配置
     * @return 验证结果
     */
    @Override
    public ShopConfigVerifyResult afterQuery(ShopConfigContext context, ApiShopConfigComposite shopConfig, ShopGrayConfig grayConfig) {

        // 默认下载类型
        if (CollectionUtils.isEmpty(shopConfig.getCommonConfig().getTradeType())) {
            shopConfig.getCommonConfig().setTradeType(this.defaultTradeType);
        }

        return ShopConfigVerifyResult.success();
    }

    /**
     * 保存前处理
     *
     * @param context          上下文
     * @param sourceShopConfig 已存在店铺配置
     * @param destShopConfig   待入库店铺配置
     * @return 验证结果
     */
    @Override
    public ShopConfigVerifyResult beforeSave(ShopConfigContext context, ApiShopConfigComposite sourceShopConfig, ApiShopConfigComposite destShopConfig) {

        // 校验下载类型
        if (CollectionUtils.isEmpty(destShopConfig.getCommonConfig().getTradeType())) {
            return ShopConfigVerifyResult.failed(ShopConfigErrorCode.TRADE_TYPE_EMPTY);
        }

        return ShopConfigVerifyResult.success();
    }

    // endregion
}

