package com.differ.jackyun.omsapi.user.biz.domain.shopconfig.utils;

import com.differ.jackyun.framework.component.pagehelper.util.StringUtil;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.common.KeyValuePair;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.result.AuthContentResult;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.result.AuthErrorContent;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.handler.custom.params.impl.CustomParamsHandler;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.processor.AuthFactory;
import com.differ.jackyun.omsapi.user.biz.domain.authorization.processor.BaseAuthPlatProcessor;
import com.differ.jackyun.omsapi.user.biz.domain.shopconfig.data.ApiShopConfigComposite;
import com.differ.jackyun.omsapi.user.biz.infrastructure.common.LogAdapter;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.shop.core.ApiShopAuthInfo;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.shop.core.ApiShopBaseInfo;
import com.differ.jackyun.omsapibase.data.open.erp.BaseShopInfo;
import com.differ.jackyun.omsapibase.data.shopconf.enums.ShopAuthStatusEnum;
import com.differ.jackyun.omsapibase.data.shopv2.entity.ApiShopAuthEntity;
import com.differ.jackyun.omsapibase.data.shopv2.entity.ShopBaseAuthEntity;
import com.differ.jackyun.omsapibase.infrastructure.enums.LogTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.openapi.authorization.PlatAuthorizationCustomBizData;
import com.differ.jackyun.omsapibase.infrastructure.utils.CollectionsUtil;
import com.differ.jackyun.omsapibase.infrastructure.utils.CoreUtils;
import com.differ.jackyun.omsapibase.infrastructure.utils.ExtUtils;
import com.differ.jackyun.omsapibase.infrastructure.utils.JsonUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 店铺授权信息转换工具类
 *
 * <AUTHOR>
 * @date 2022-07-04 11:26
 */
public class ShopAuthConvertUtils {

    /**
     * 构造方法
     */
    private ShopAuthConvertUtils() {

    }

    // region 公共方法

    /**
     * 类型转换（入库实体 -> 授权信息）
     *
     * @param entity 入库实体
     * @return 结果
     */
    public static ApiShopAuthInfo convert(ApiShopAuthEntity entity) {

        // 店铺Id
        Long shopId = entity.getShopId();

        // 店铺授权信息
        ApiShopAuthInfo info = new ApiShopAuthInfo();
        info.setShopId(shopId);
        try {

            // 数据转换
            info.setPolyToken(entity.getPolyToken());
            info.setAppKey(entity.getAppKey());
            info.setAuthStatus(entity.getAuthStatus() == null ? null : ShopAuthStatusEnum.create(entity.getAuthStatus().toString()));
            info.setAuthRemainTime(entity.getAuthRemainTime());
            info.setAuthSellNick(entity.getAuthSellNick());
            info.setPlatShopId(entity.getPlatShopId());
            info.setPlatShopName(entity.getPlatShopName());
            info.setPurchaseExpireTime(entity.getPurchaseExpireTime());
            info.setGmtCreate(entity.getGmtCreate());
            info.setGmtModified(entity.getGmtModified());

            // Session Key：密文 & 解密后原文
            info.setAuthSessionKey(entity.getAuthSessionKey());
            info.setDecryptedSessionKey(decrypt(shopId, entity.getAuthSessionKey()));

            // 自用型授权参数：密文 & 解析后实体
            info.setSelfUseAuth(entity.getSelfUseAuth());
            info.setSelfUseAuthItem(resolveSelfUseAuth(shopId, entity.getSelfUseAuth()));

            // 扩展参数
            info.setExtraParam(resolveExtraParam(shopId, entity.getExtraJson()));
        } catch (Exception e) {
            ShopConfigStatisticsUtils.logException(() -> ExtUtils.stringBuilderAppend(String.format("转换店铺授权信息出现异常，店铺Id：%s", shopId)), e);
        }

        return info;
    }

    /**
     * 类型转换（授权信息 -> 入库实体）
     *
     * @param shopAuthInfo 授权信息
     * @return 结果
     */
    public static ApiShopAuthEntity convert(ApiShopAuthInfo shopAuthInfo) {

        // 店铺Id
        Long shopId = shopAuthInfo.getShopId();
        ApiShopAuthEntity entity = new ApiShopAuthEntity();
        entity.setShopId(shopId);
        try {

            // 数据转换
            entity.setPolyToken(shopAuthInfo.getPolyToken());
            entity.setPolyToken(shopAuthInfo.getPolyToken());
            entity.setAppKey(shopAuthInfo.getAppKey());
            entity.setAuthRemainTime(shopAuthInfo.getAuthRemainTime());
            entity.setAuthSellNick(shopAuthInfo.getAuthSellNick());
            entity.setPlatShopId(shopAuthInfo.getPlatShopId());
            entity.setPlatShopName(shopAuthInfo.getPlatShopName());
            entity.setPurchaseExpireTime(shopAuthInfo.getPurchaseExpireTime());

            // 授权状态
            if (shopAuthInfo.getAuthStatus() == null) {
                entity.setAuthStatus(Byte.parseByte(ShopAuthStatusEnum.NOT_AUTHORIZED.getValue().toString()));
            } else {
                entity.setAuthStatus(Byte.parseByte(shopAuthInfo.getAuthStatus().getValue().toString()));
            }

            // 加密 Session Key
            entity.setAuthSessionKey(encrypt(shopId, shopAuthInfo.getDecryptedSessionKey()));

            // 加密自用型授权参数
            entity.setSelfUseAuth(encrypt(shopId, JsonUtils.toJson(shopAuthInfo.getSelfUseAuthItem())));

            // 序列化扩展参数
            entity.setExtraJson(JsonUtils.toJson(shopAuthInfo.getExtraParam()));
        } catch (Exception e) {
            ShopConfigStatisticsUtils.logException(() -> ExtUtils.stringBuilderAppend(String.format("转换店铺授权信息出现异常，店铺Id：%s", shopId)), e);
        }

        return entity;
    }

    /**
     * 类型转换（入库实体 -> 授权信息）
     *
     * @param entity 入库实体
     * @return 结果
     */
    public static ApiShopAuthInfo convert(ShopBaseAuthEntity entity) {

        // 店铺Id
        Long shopId = entity.getShopId();

        // 店铺授权信息
        ApiShopAuthInfo info = new ApiShopAuthInfo();
        info.setShopId(shopId);
        try {

            // 数据转换
            info.setPolyToken(entity.getPolyToken());
            info.setAppKey(entity.getAppKey());
            info.setAuthStatus(entity.getAuthStatus() == null ? null : ShopAuthStatusEnum.create(entity.getAuthStatus().toString()));
            info.setAuthRemainTime(entity.getAuthRemainTime());
            info.setAuthSellNick(entity.getAuthSellNick());
            info.setPlatShopId(entity.getPlatShopId());
            info.setPlatShopName(entity.getPlatShopName());
            info.setPurchaseExpireTime(entity.getPurchaseExpireTime());
            info.setGmtCreate(entity.getAuthGmtCreate());
            info.setGmtModified(entity.getAuthGmtModified());

            // Session Key：密文 & 解密后原文
            info.setAuthSessionKey(entity.getAuthSessionKey());
            info.setDecryptedSessionKey(decrypt(shopId, entity.getAuthSessionKey()));

            // 自用型授权参数：密文 & 解析后实体
            info.setSelfUseAuth(entity.getSelfUseAuth());
            info.setSelfUseAuthItem(resolveSelfUseAuth(shopId, entity.getSelfUseAuth()));

            // 扩展参数
            info.setExtraParam(resolveExtraParam(shopId, entity.getAuthExtraJson()));
        } catch (Exception e) {
            ShopConfigStatisticsUtils.logException(() -> ExtUtils.stringBuilderAppend(String.format("转换店铺授权信息出现异常，店铺Id：%s", shopId)), e);
        }

        return info;
    }

    /**
     * 生成授权展示信息
     *
     * @param shopAuthInfo 店铺授权信息
     * @return 结果
     */
    public static Map<String, String> buildAuthDisplayInfo(ApiShopAuthInfo shopAuthInfo) {

        if (shopAuthInfo == null) {
            return new HashMap<>();
        }

        Map<String, String> authDisplayInfo = new HashMap<>();
        try {

            authDisplayInfo.put("授权状态", buildAuthStatusStr(shopAuthInfo));
            if (shopAuthInfo.getExtraParam() != null && StringUtils.isNotBlank(shopAuthInfo.getExtraParam().getSubscriptionExpireTime())) {
                authDisplayInfo.put("服务订购有效期", shopAuthInfo.getExtraParam().getSubscriptionExpireTime());
            }
            if (StringUtils.isNotBlank(shopAuthInfo.getAuthSellNick())) {
                authDisplayInfo.put("授权账号", shopAuthInfo.getAuthSellNick());
            }
            if (StringUtils.isNotBlank(shopAuthInfo.getPlatShopId())) {
                authDisplayInfo.put("平台店铺id", shopAuthInfo.getPlatShopId());
            }
            return authDisplayInfo;

        } catch (Exception e) {
            LogAdapter.writeSystemLog("生成授权展示信息出错", e);
        }

        return authDisplayInfo;
    }

    // endregion

    // region 私有方法

    /**
     * 加密
     *
     * @param shopId 店铺Id
     * @param source 原文
     * @return 密文
     */
    public static String encrypt(Long shopId, String source) {
        try {
            if (StringUtil.isEmpty(source)) {
                return source;
            }

            return CoreUtils.makeAES(source);
        } catch (Exception e) {
            ShopConfigStatisticsUtils.logException(() -> ExtUtils.stringBuilderAppend(String.format("店铺加密出现异常，店铺Id：%s，原文：%s", shopId, source)), e);
            return source;
        }
    }

    /**
     * 解密
     *
     * @param shopId 店铺Id
     * @param cipher 密文
     * @return 原文
     */
    public static String decrypt(Long shopId, String cipher) {
        try {
            if (StringUtil.isEmpty(cipher)) {
                return cipher;
            }

            return CoreUtils.decryptAES(cipher);
        } catch (Exception e) {
            ShopConfigStatisticsUtils.logWarning(null, () ->
                    ExtUtils.stringBuilderAppend(String.format("店铺解密出现异常，店铺Id：%s，密文：%s，异常：%s", shopId, cipher, CoreUtils.exceptionToString(e))));
            return cipher;
        }
    }

    /**
     * 解析扩展参数
     *
     * @param shopId    店铺Id
     * @param extraJson 扩展Json
     * @return 解析后结果
     */
    public static ApiShopAuthInfo.ApiShopAuthExtraParam resolveExtraParam(Long shopId, String extraJson) {
        try {
            if (StringUtil.isEmpty(extraJson)) {
                return null;
            }

            return JsonUtils.deJson(extraJson, ApiShopAuthInfo.ApiShopAuthExtraParam.class);
        } catch (Exception e) {
            ShopConfigStatisticsUtils.logException(() -> ExtUtils.stringBuilderAppend(String.format("解析店铺授权扩展参数出现异常，店铺Id：%s，扩展参数：%s", shopId, extraJson)), e);
            return null;
        }
    }

    /**
     * 解析自用型授权参数
     *
     * @param shopId      店铺Id
     * @param selfUseAuth 自用型授权参数
     * @return 解析后结果
     */
    public static PlatAuthorizationCustomBizData resolveSelfUseAuth(Long shopId, String selfUseAuth) {

        try {

            // 解密自用型授权参数
            String decryptedSelfUseAuth = decrypt(shopId, selfUseAuth);

            // 校验是否为空
            if (StringUtil.isEmpty(decryptedSelfUseAuth)) {
                return new PlatAuthorizationCustomBizData();
            }

            // 反序列化
            PlatAuthorizationCustomBizData selfUseAuthItem = JsonUtils.deJson(decryptedSelfUseAuth, PlatAuthorizationCustomBizData.class);
            if (selfUseAuthItem == null) {
                return new PlatAuthorizationCustomBizData();
            }

            return selfUseAuthItem;
        } catch (Exception e) {
            ShopConfigStatisticsUtils.logException(() -> ExtUtils.stringBuilderAppend(String.format("解析店铺自用型授权参数出现异常，店铺Id：%s，自用型授权参数：%s", shopId, selfUseAuth)), e);
            return new PlatAuthorizationCustomBizData();
        }
    }


    private static String buildAuthStatusStr(ApiShopAuthInfo shopAuthInfo) {
        ShopAuthStatusEnum authStatus = shopAuthInfo.getAuthStatus();
        if (authStatus == null) {
            return "店铺未授权";
        }
        if (!ShopAuthStatusEnum.AUTHORIZED.equals(authStatus)) {
            return authStatus.getCaption();
        }

        LocalDateTime authRemainTime = shopAuthInfo.getAuthRemainTime();
        if (authRemainTime != null) {
            // 取日期部分计算相差的天数
            long days = LocalDate.now().until(authRemainTime.toLocalDate(), ChronoUnit.DAYS);
            if (days < 0) {
                return "授权已过期，请重新授权";
            }
            if (days == 0) {
                return "授权将在今天到期，请尽快重新授权";
            }

            return String.format("已授权，%d天后过期", days);
        }
        return "已授权";
    }

    // endregion
}
