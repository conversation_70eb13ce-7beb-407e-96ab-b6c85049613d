package com.differ.jackyun.omsapi.user.biz.domain.syncstock.calculation.csm.data;

import com.differ.jackyun.omsapi.user.biz.domain.syncstock.data.BaseSyncStockSnapshot;
import com.differ.jackyun.omsapi.user.biz.domain.syncstock.data.StockSyncRule;

import java.math.BigDecimal;

/**
 * 同步库存快照（渠道库存模式）
 *
 * <AUTHOR>
 * @date 2023/4/10 16:27
 */
public class CsmSyncStockSnapshot extends BaseSyncStockSnapshot {

    /**
     * 系统货品 ID
     */
    private Long skuId;

    /**
     * 同步数量
     */
    private BigDecimal syncQuantity;

    /**
     * 同步规则
     */
    private StockSyncRule syncRule;

    /**
     * 库存数量
     */
    private BigDecimal stockQuantity;

    /**
     * 库存明细
     */
    private CsmSystemSkuStockDetail stockDetail;

    // region getter & setter

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public BigDecimal getSyncQuantity() {
        return syncQuantity;
    }

    public void setSyncQuantity(BigDecimal syncQuantity) {
        this.syncQuantity = syncQuantity;
    }

    public StockSyncRule getSyncRule() {
        return syncRule;
    }

    public void setSyncRule(StockSyncRule syncRule) {
        this.syncRule = syncRule;
    }

    public BigDecimal getStockQuantity() {
        return stockQuantity;
    }

    public void setStockQuantity(BigDecimal stockQuantity) {
        this.stockQuantity = stockQuantity;
    }

    public CsmSystemSkuStockDetail getStockDetail() {
        return stockDetail;
    }

    public void setStockDetail(CsmSystemSkuStockDetail stockDetail) {
        this.stockDetail = stockDetail;
    }

    // endregion
}
