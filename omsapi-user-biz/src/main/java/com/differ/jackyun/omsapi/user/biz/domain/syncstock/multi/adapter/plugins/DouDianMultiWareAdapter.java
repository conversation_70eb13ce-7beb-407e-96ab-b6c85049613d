package com.differ.jackyun.omsapi.user.biz.domain.syncstock.multi.adapter.plugins;

import com.differ.jackyun.omsapi.user.biz.domain.syncstock.data.multi.MultiWareAdapterContext;
import com.differ.jackyun.omsapibase.data.constant.OmsApiConstant;
import com.differ.jackyun.omsapibase.data.goodsmatch.ApiPlatGoodsMatchEntity;
import com.differ.jackyun.omsapibase.data.goodsmatch.ApiPlatGoodsMatchExtraEntity;

/**
 * 抖店
 *
 * <AUTHOR>
 * @since 2023-11-27 15:56
 */
public class DouDianMultiWareAdapter extends NormalMultiWareAdapter {

    // region 构造方法

    /**
     * 构造方法
     *
     * @param context 上下文
     */
    public DouDianMultiWareAdapter(MultiWareAdapterContext context) {
        super(context);
    }

    // endregion

    // region 接口实现 & 重写基类方法

    /**
     * 商品是否支持多仓
     *
     * @return 是否支持
     */
    @Override
    public boolean supportMulti(ApiPlatGoodsMatchEntity goodsMatch) {

        // 是否区域库存
        return this.isRegionStock(goodsMatch);
    }

    // endregion

    // region 私有方法

    /**
     * 是否区域库存
     *
     * @param goodsMatch 匹配数据
     * @return 结果
     */
    private boolean isRegionStock(ApiPlatGoodsMatchEntity goodsMatch) {

        // 解析匹配扩展数据
        ApiPlatGoodsMatchExtraEntity.ExtraJson extraJson = this.resolveMatchExtra(goodsMatch);

        // 是否区域库存
        return extraJson != null && OmsApiConstant.CHAR_1.equals(extraJson.getSkuType());
    }

    // endregion
}
