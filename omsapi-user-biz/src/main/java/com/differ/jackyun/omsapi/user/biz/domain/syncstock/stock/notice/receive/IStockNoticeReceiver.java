package com.differ.jackyun.omsapi.user.biz.domain.syncstock.stock.notice.receive;

import com.differ.jackyun.omsapi.user.biz.domain.syncstock.data.stocknotice.SystemSkuStockChangeRequest;

/**
 * 库存变动通知接收
 *
 * <AUTHOR>
 * @date 2024-11-26 17:08
 */
public interface IStockNoticeReceiver {

    /**
     * 处理库存变动通知
     *
     * @param changeRequest 请求
     */
    void processChangeNotice(SystemSkuStockChangeRequest changeRequest);
}
