package com.differ.jackyun.omsapi.user.biz.domain.syncstock.stock.notice.trigger.command.plugins;

import com.differ.jackyun.omsapi.user.biz.domain.syncstock.stock.notice.trigger.command.AbstractStockSyncCommandResolver;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.syncstock.config.sharding.core.StockSyncShardingConfig;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.syncstock.config.trigger.command.StockSyncTriggerCommandSet;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.syncstock.job.StockSyncJobShardingData;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.enums.stock.StockSyncJobShardingTypeEnum;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.enums.stock.StockSyncSlowSpeedEnum;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.enums.stock.StockSyncTriggerConfigTypeEnum;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.enums.stock.StockSyncTriggerModeEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * 库存同步指令解析：通用慢平台（统一并行）
 *
 * <AUTHOR>
 * @date 2025-01-29 19:13
 */
public class UniteNormalSlowPlatCommandResolver extends AbstractStockSyncCommandResolver {

    // region 供子类重写

    /**
     * 获取配置类型
     *
     * @return 配置类型
     */
    @Override
    protected StockSyncTriggerConfigTypeEnum getConfigType() {
        return StockSyncTriggerConfigTypeEnum.NORMAL;
    }

    /**
     * 获取任务分片
     *
     * @param memberName 会员名
     * @return 任务分片
     */
    @Override
    public List<StockSyncJobShardingData> getJobSharding(String memberName) {

        // 结果集
        List<StockSyncJobShardingData> ret = new ArrayList<>();

        // 封装任务分片：慢平台不支持自定义分片
        ret.add(this.createJobSharding(StockSyncJobShardingTypeEnum.MEMBER, memberName, memberName));
        return ret;
    }

    /**
     * 过滤自定义分片
     *
     * @param memberName     会员名
     * @param shardingSign   分片标识
     * @param shardingConfig 分片配置
     * @return 是否有效
     */
    @Override
    protected boolean filterCustomSharing(String memberName, String shardingSign, StockSyncShardingConfig shardingConfig) {
        return StockSyncTriggerModeEnum.UNITE.equals(shardingConfig.getTriggerMode());
    }

    /**
     * 解析指令
     *
     * @param jobSharding 任务分片
     * @return 指令集
     */
    @Override
    public StockSyncTriggerCommandSet resolveCommands(StockSyncJobShardingData jobSharding) {

        // 解析指令
        StockSyncTriggerCommandSet commandSet = super.resolveCommands(jobSharding);
        if (commandSet == null) {
            return null;
        }

        // 只取慢平台
        commandSet.filterCommand(t -> StockSyncSlowSpeedEnum.SLOW_SPEED_1.equals(t.getSlowSpeed()));
        return commandSet;
    }

    // endregion
}
