package com.differ.jackyun.omsapi.user.biz.domain.syncstock.sub.domian.goodsbiz.processor.plat;

import com.differ.jackyun.omsapi.user.biz.domain.syncstock.sub.domian.goodsbiz.processor.BaseGoodsBizGetProcessor;
import com.differ.jackyun.omsapi.user.biz.domain.syncstock.sub.domian.goodsbiz.processor.IGoodsBizGetProcessor;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.core.PolyAPIPlatEnum;
import com.differ.jackyun.omsapibase.infrastructure.utils.SpringResolveManager;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/3/23 15:43
 */
public class PlatGoodsBizGetProcessorFactory {

    /**
     * @param platEnum
     * @return
     */
    public static IGoodsBizGetProcessor create(PolyAPIPlatEnum platEnum) {

        if (null == platEnum) {
            return SpringResolveManager.resolve(BaseGoodsBizGetProcessor.class, "BaseGoodsBizGetProcessor");
        }

        switch (platEnum) {
            case BUSINESS_TAOBAO:
                return SpringResolveManager.resolve(TaoBaoGoodsBizGetProcessor.class);
            case BUSINESS_FXG:
                return SpringResolveManager.resolve(ByteDanceGoodsBizGetProcessor.class);
            default:
                return SpringResolveManager.resolve(BaseGoodsBizGetProcessor.class, "BaseGoodsBizGetProcessor");
        }
    }
}
