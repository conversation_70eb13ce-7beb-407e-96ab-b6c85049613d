package com.differ.jackyun.omsapi.user.biz.domain.waitquery;

import com.differ.jackyun.omsapi.component.wait.WaitLine;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.waitquery.data.WaitQueryRequest;
import com.differ.jackyun.omsapi.user.biz.infrastructure.wait.load.order.WaitLoadOrderProxyHandler;
import com.differ.jackyun.omsapi.user.biz.infrastructure.wait.load.order.core.WaitLoadOrderUtil;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.core.PolyAPIPlatEnum;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description 下载订单排队数查询处理器
 * <AUTHOR>
 * @Date 2024/10/30 9:55
 */
public class LoadOrderWaitQueryProcess extends AbstractWaitQueryProcess {

    // region 重写方法

    /**
     * 获取会员级排队handler
     *
     * @param request
     * @return
     */
    @Override
    protected WaitLine getUserHandler(WaitQueryRequest request) {
        PolyAPIPlatEnum platEnum = request.getPlat() == null ? null : PolyAPIPlatEnum.create(request.getPlat().toString());
        platEnum = Boolean.TRUE.equals(request.getIsolated()) && platEnum != null ? platEnum : null;

        return WaitLoadOrderProxyHandler.get().getUserHandler(request.getMemberName(), platEnum);
    }

    /**
     * 获取集群级排队handler
     *
     * @param request
     * @return
     */
    @Override
    protected WaitLine getGroupHandler(WaitQueryRequest request) {
        PolyAPIPlatEnum platEnum = request.getPlat() == null ? null : PolyAPIPlatEnum.create(request.getPlat().toString());
        platEnum = Boolean.TRUE.equals(request.getIsolated()) && platEnum != null ? platEnum : null;

        return WaitLoadOrderProxyHandler.get().getGlobalHandler(request.getGroupId(), platEnum);
    }

    /**
     * 查询排队数和执行数
     *
     * @param waitLine 排队
     * @param request  请求条件
     * @return
     */
    @Override
    protected Map<String, Long> queryWaitAndExecSum(WaitLine waitLine, WaitQueryRequest request) {
        Map<String, Long> result = super.queryWaitAndExecSum(waitLine, request);
        PolyAPIPlatEnum platEnum = request.getPlat() == null ? null : PolyAPIPlatEnum.create(request.getPlat().toString());

        if (Boolean.TRUE.equals(request.getIsolated()) && platEnum != null) {
            // 平台独立排队无需根据平台过滤
            platEnum = null;
        }

        // 根据平台查询时需要根据平台值过滤
        if (platEnum != null && !PolyAPIPlatEnum.NONE.equals(platEnum)) {
            PolyAPIPlatEnum plat = platEnum;
            List<String> waitKeys = waitLine.getWaitKeys(0, -1);
            List<String> execKeys = waitLine.getExecKeys();
            long waitSum = CollectionUtils.isEmpty(waitKeys) ? 0 : waitKeys.stream().filter(k -> WaitLoadOrderUtil.keyFilter(k, plat)).count();
            long execSum = CollectionUtils.isEmpty(execKeys) ? 0 : execKeys.stream().filter(k -> WaitLoadOrderUtil.keyFilter(k, plat)).count();
            result.put(WAIT_SUM, waitSum);
            result.put(EXEC_SUM, execSum);
        }
        return result;
    }

    /**
     * 查询排队详情
     *
     * @param waitLine 排队
     * @param request
     * @return
     */
    @Override
    protected Map<String, String> queryWaitData(WaitLine waitLine, WaitQueryRequest request) {

        long start = 0;
        long end = -1;
        Integer pageIndex = request.getPageIndex();
        Integer pageSize = request.getPageSize();
        PolyAPIPlatEnum plat = request.getPlat() == null ? null : PolyAPIPlatEnum.create(request.getPlat().toString());

        // 独立排队直接查询
        if (request.getIsolated() && plat != null) {
            start = (long) pageIndex * pageSize;
            end = ((long) pageIndex * pageSize + pageSize) - 1;
        }

        List<String> waitKeys = waitLine.getWaitKeys(start, end);
        if (CollectionUtils.isNotEmpty(waitKeys) && !request.getIsolated() && plat != null && !PolyAPIPlatEnum.NONE.equals(plat)) {
            // 平台不为空时需要根据平台过滤
            waitKeys = waitKeys.stream().filter(k -> WaitLoadOrderUtil.keyFilter(k, plat)).collect(Collectors.toList());
        }
        // 分页返回需要拆分
        if (CollectionUtils.isNotEmpty(waitKeys) && waitKeys.size() > pageSize) {
            int size = waitKeys.size();
            int leftBoundary = Math.min(pageIndex * pageSize, size);
            int rightBoundary = Math.min((pageIndex + 1) * pageSize, size);
            waitKeys = waitKeys.subList(leftBoundary, rightBoundary);
        }

        return waitLine.getWaitData(waitKeys);
    }

    /**
     * 查询需要删除的排队业务键
     *
     * @param waitLine 排队
     * @param request  请求
     * @return
     */
    @Override
    protected List<String> queryDeleteWaitUniqueIds(WaitLine waitLine, WaitQueryRequest request) {
        List<String> waitUniqueIds = super.queryDeleteWaitUniqueIds(waitLine, request);
        PolyAPIPlatEnum plat = request.getPlat() == null ? null : PolyAPIPlatEnum.create(request.getPlat().toString());
        // 按平台删除
        if (plat != null && !plat.equals(PolyAPIPlatEnum.NONE)) {
            waitUniqueIds = waitUniqueIds.stream().filter(k -> WaitLoadOrderUtil.keyFilter(k, plat)).collect(Collectors.toList());
        }
        return waitUniqueIds;
    }

    // endregion
}
