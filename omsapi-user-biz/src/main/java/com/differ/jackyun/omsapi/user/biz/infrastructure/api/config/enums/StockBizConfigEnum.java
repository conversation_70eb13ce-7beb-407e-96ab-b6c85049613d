package com.differ.jackyun.omsapi.user.biz.infrastructure.api.config.enums;

import com.differ.jackyun.omsapi.user.biz.infrastructure.api.config.IBizConfigEnum;

/**
 * 库存相关配置键
 *
 * <AUTHOR>
 * @date 2025-01-02 15:44
 */
public enum StockBizConfigEnum implements IBizConfigEnum {

    /**
     * 库存变动通知接收 V2 版本，格式：|会员1|会员2|
     */
    STOCK_NOTICE_RECEIVE_V2("STOCK_NOTICE_RECEIVE_V2", "库存变动通知接收 V2 版本，格式：|会员1|会员2|"),

    /**
     * 库存变动通知转换 V2 版本，格式：|转换类型-会员|
     */
    STOCK_NOTICE_CONVERT_V2("STOCK_NOTICE_CONVERT_V2", "库存变动通知转换 V2 版本，格式：|转换类型-会员|"),

    /**
     * 库存同步触发 V2 版本，格式：|触发类型-会员|
     */
    STOCK_SYNC_TRIGGER_V2("STOCK_SYNC_TRIGGER_V2", "库存同步触发 V2 版本，格式：|触发类型-会员|"),

    /**
     * 分销商商品匹配 V2 版本，格式：|会员1|会员2|
     */
    DISTRIBUTOR_GOODS_MATCH_V2("DISTRIBUTOR_GOODS_MATCH_V2", "分销商商品匹配 V2 版本，格式：|会员1|会员2|"),

    /**
     * 分销商刷新匹配分页大小，格式：|会员=分页大小|
     */
    DISTRIBUTOR_REFRESH_MATCH_PAGE_SIZE("DISTRIBUTOR_REFRESH_MATCH_PAGE_SIZE", "分销商刷新匹配分页大小，格式：|会员=分页大小|"),

    /**
     * 分销商刷新匹配单次循环次数，格式：|会员=循环次数|
     */
    DISTRIBUTOR_REFRESH_MATCH_ONCE_LOOP_TIME("DISTRIBUTOR_REFRESH_MATCH_ONCE_LOOP_TIME", "分销商刷新匹配单次循环次数，格式：|会员=循环次数|"),

    /**
     * 分销商刷新匹配是否更新已存在数据 0 不更新 1 更新，默认：0，格式：|会员-0 or 1|
     */
    DISTRIBUTOR_REFRESH_MATCH_UPDATE_EXIST("DISTRIBUTOR_REFRESH_MATCH_UPDATE_EXIST", "分销商刷新匹配是否更新已存在数据 0 不更新 1 更新，默认：0，格式：|会员-0 or 1|"),

    /**
     * 分销商刷新匹配强制中止，格式：|会员1|会员2|
     */
    DISTRIBUTOR_REFRESH_MATCH_FORCE_ABORT("DISTRIBUTOR_REFRESH_MATCH_FORCE_ABORT", "分销商刷新匹配强制中止，格式：|会员1|会员2|"),

    /**
     * 分销商库存同步兼容旧版本，格式：|会员1|会员2|
     */
    DISTRIBUTOR_STOCK_SYNC_COMPATIBLE_OLD("DISTRIBUTOR_STOCK_SYNC_COMPATIBLE_OLD", "分销商库存同步兼容旧版本，格式：|会员1|会员2|"),

    /**
     * 分销商库存变动通知转换任务执行频率，默认：20
     */
    DISTRIBUTOR_STOCK_CONVERT_JOB_FREQUENCY("DISTRIBUTOR_STOCK_CONVERT_JOB_FREQUENCY", "分销商库存变动通知转换任务执行频率，默认：20"),

    /**
     * 分销商库存同步触发任务执行频率，默认：10
     */
    DISTRIBUTOR_STOCK_TRIGGER_JOB_FREQUENCY("DISTRIBUTOR_STOCK_TRIGGER_JOB_FREQUENCY", "分销商库存同步触发任务执行频率，默认：10"),

    /**
     * 分销商店铺配置 V2 版本，格式：|会员1|会员2|
     */
    DISTRIBUTOR_SHOP_CONFIG_V2("DISTRIBUTOR_SHOP_CONFIG_V2", "分销商店铺配置 V2 版本，格式：|会员1|会员2|"),

    /**
     * 分销商手动库存同步执行最大限制数量，默认：200
     */
    DISTRIBUTOR_MANUAL_SYNC_EXECUTE_MAX_LIMIT("DISTRIBUTOR_MANUAL_SYNC_EXECUTE_MAX_LIMIT", "分销商手动库存同步执行最大限制数量，默认：200"),

    /**
     * 分销商手动库存同步拆包大小，默认：200
     */
    DISTRIBUTOR_MANUAL_SYNC_PACKAGE_SIZE("DISTRIBUTOR_MANUAL_SYNC_PACKAGE_SIZE", "分销商手动库存同步拆包大小，默认：200"),

    /**
     * 分销商手动库存同步延迟执行超时时间，默认 600，单位：秒
     */
    DISTRIBUTOR_MANUAL_DELAY_EXEC_TIMEOUT("DISTRIBUTOR_MANUAL_DELAY_EXEC_TIMEOUT", "分销商手动库存同步延迟执行超时时间，默认 600，单位：秒"),

    /**
     * 分销商手动库存同步延迟执行单次拉取任务数量，默认：5
     */
    DISTRIBUTOR_MANUAL_DELAY_EXEC_ONCE_FETCH_COUNT("DISTRIBUTOR_MANUAL_DELAY_EXEC_ONCE_FETCH_COUNT", "分销商手动库存同步延迟执行单次拉取任务数量，默认：5"),

    /**
     * 分销商手动库存同步延迟执行最大循环拉取次数，默认：20
     */
    DISTRIBUTOR_MANUAL_DELAY_EXEC_MAX_LOOP_TIME("DISTRIBUTOR_MANUAL_DELAY_EXEC_MAX_LOOP_TIME", "分销商手动库存同步延迟执行最大循环拉取次数，默认：20"),

    /**
     * 分销商店铺配置强制使用模板，格式：|会员1|会员2|
     */
    DISTRIBUTOR_SHOP_CONFIG_FORCE_TEMPLATE("DISTRIBUTOR_SHOP_CONFIG_FORCE_TEMPLATE", "分销商店铺配置强制使用模板，格式：|会员1|会员2|"),

    /**
     * 分销商店铺配置强制模板ID，格式：|会员-店铺ID|
     */
    DISTRIBUTOR_FORCE_TEMPLATE_SHOP_ID("DISTRIBUTOR_FORCE_TEMPLATE_SHOP_ID", "分销商店铺配置强制模板ID，格式：|会员-店铺ID|"),

    /**
     * 分销商强制同步全部仓库库存，格式：|会员1|会员2|
     */
    DISTRIBUTOR_FORCE_SYNC_ALL_WAREHOUSE("DISTRIBUTOR_FORCE_SYNC_ALL_WAREHOUSE", "分销商强制同步全部仓库库存，格式：|会员1|会员2|"),

    /**
     * 分销商禁止同步组合装库存，格式：|会员1|会员2|
     */
    DISTRIBUTOR_FORBID_SYNC_ASSEMBLY("DISTRIBUTOR_FORBID_SYNC_ASSEMBLY", "分销商禁止同步组合装库存，格式：|会员1|会员2|"),

    /**
     * 分销商库存同步结果通知，格式：|会员1|会员2|
     */
    DISTRIBUTOR_STOCK_SYNC_RESULT_NOTICE("DISTRIBUTOR_STOCK_SYNC_RESULT_NOTICE", "分销商库存同步结果通知，格式：|会员1|会员2|"),

    /**
     * 分销商查询已存在库存变动通知分页大小，格式：|会员=分页大小|
     */
    DISTRIBUTOR_QUERY_EXIST_STOCK_NOTICE_PAGE_SIZE("DISTRIBUTOR_QUERY_EXIST_STOCK_NOTICE_PAGE_SIZE", "分销商查询已存在库存变动通知分页大小，格式：|会员=分页大小|"),

    /**
     * 分销商多仓库存同步请求批量接口，格式：|会员1|会员2|
     */
    DISTRIBUTOR_MULTI_STOCK_SYNC_REQUEST_BATCH("DISTRIBUTOR_MULTI_STOCK_SYNC_NEW_ITF", "分销商多仓库存同步请求批量接口，格式：|会员1|会员2|"),

    /**
     * 分销商库存同步界面 V2 版本，格式：|会员1|会员2|
     */
    DISTRIBUTOR_STOCK_SYNC_FRONT_V2("DISTRIBUTOR_STOCK_SYNC_FRONT_V2", "分销商库存同步界面 V2 版本，格式：|会员1|会员2|"),

    /**
     * 分销商手动库存同步支持组合装，格式：|会员1|会员2|
     */
    DISTRIBUTOR_MANUAL_STOCK_SYNC_SUPPORT_ASSEMBLY("DISTRIBUTOR_MANUAL_STOCK_SYNC_SUPPORT_ASSEMBLY", "分销商手动库存同步支持组合装，格式：|会员1|会员2|"),

    /**
     * 分销商库存同步请求强制网关转发，格式：|供应商会员名1|供应商会员名2|
     */
    DISTRIBUTOR_STOCK_SYNC_REQUEST_FORCE_GATE_FORWARD("DISTRIBUTOR_STOCK_SYNC_REQUEST_FORCE_GATE_FORWARD", "分销商库存同步请求强制网关转发，格式：|供应商会员名1|供应商会员名2|"),

    /**
     * 库存同步页面polarDB查询版本，格式：|会员1|会员2|
     */
    PAGE_STOCK_POLAR_DB_QUERY_VERSION("PAGE_STOCK_POLAR_DB_QUERY_VERSION", "库存同步页面polarDB查询版本，格式：|会员1|会员2|"),

    /**
     * 库存同步更新匹配数据拆包数量，默认：200
     */
    SYNC_STOCK_OPTIMIZE_ONCE_UPDATE_MATCH_PACKAGE_SIZE("SYNC_STOCK_OPTIMIZE_ONCE_UPDATE_MATCH_PACKAGE_SIZE", "库存同步更新匹配数据拆包数量，默认：200"),

    /**
     * 库存同步更新匹配数据最大分组数量
     */
    SYNC_STOCK_UPDATE_MATCH_DATA_SPLIT_LIMIT("SYNC_STOCK_UPDATE_MATCH_DATA_SPLIT_LIMIT", "库存同步更新匹配数据最大分组数量，默认：200"),

    /**
     * 开启库存计算模式-排除当前店铺订购量的会员店铺（格式：|会员|会员-店铺Id|店铺Id|）
     */
    STOCK_CAL_MODE_MEMBER_SHOP_EXCEPT_SELF_ORDER_QUANTITY("STOCK_CAL_MODE_MEMBER_SHOP_EXCEPT_SELF_ORDER_QUANTITY", "开启库存计算模式-排除当前店铺订购量的会员店铺（格式：|会员|会员-店铺Id|店铺Id|）"),

    /**
     * 开启库存计算模式-排除当前店铺订购量的平台（格式：|平台|平台-会员|）
     */
    STOCK_CAL_MODE_PLAT_MEMBER_EXCEPT_SELF_ORDER_QUANTITY("STOCK_CAL_MODE_PLAT_MEMBER_EXCEPT_SELF_ORDER_QUANTITY", "开启库存计算模式-排除当前店铺订购量的平台（格式：|平台|平台-会员|）"),

    /**
     * 库存外部接口参数拆分大小，默认：1000
     */
    STOCK_OUTER_INTERFACE_PARAM_PACKAGE_SIZE("STOCK_OUT_INTERFACE_PARAM_PACKAGE_SIZE", "库存外部接口参数拆分大小，默认：1000"),

    /**
     * 快马批发同步生产日期的会员，格式：|会员1|会员2|
     */
    KMPF_SYNC_PRODUCTION_DATE_MEMBER("KMPF_SYNC_PRODUCTION_DATE_MEMBER", "快马批发同步生产日期的会员，格式：|会员1|会员2|"),

    /**
     * 库存任务计数器容量，格式：|任务名称=容量|
     */
    STOCK_JOB_COUNTER_CAPACITY("STOCK_JOB_COUNTER_CAPACITY", "库存任务计数器容量，格式：|任务名称=容量|"),

    /**
     * 开启药房网库存同步的批次信息设置使用新字段
     */
    YAO_FANG_WANG_BATCH_INFO_USE_NEW_FIELD("YAO_FANG_WANG_BATCH_INFO_USE_NEW_FIELD","开启药房网库存同步的批次信息设置使用新字段（格式：|会员1|会员2|）"),

    /**
     * 库存同步降权错误码配置
     */
    STOCK_DOWN_GRADE_LEVEL_ERROR_CODE_CONFIG("STOCK_DOWN_GRADE_LEVEL_ERROR_CODE_CONFIG", "库存同步降权错误码配置（格式：|菠萝派错误码1~降权等级code1|菠萝派错误码2~降权等级code2|）"),

    /**
     * 库存同步降权等级时间配置
     */
    STOCK_DOWN_GRADE_LEVEL_TIME_CONFIG("STOCK_DOWN_GRADE_LEVEL_TIME_CONFIG", "库存同步降权等级时间配置（格式：|降权等级code1~时间|降权等级code2~时间|）"),

    /**
     * 其它业务保存库存变动通知 V2 版本，格式：|会员1|会员2|
     */
    EXTEND_BIZ_SAVE_STOCK_NOTICE_V2("EXTEND_BIZ_SAVE_STOCK_NOTICE_V2", "其它业务保存库存变动通知 V2 版本，格式：|会员1|会员2|"),

    /**
     * 其它业务保存库存变动通知分页大小，格式：|会员=分页大小|
     */
    EXTEND_BIZ_SAVE_STOCK_NOTICE_PAGE_SIZE("EXTEND_BIZ_SAVE_STOCK_NOTICE_PAGE_SIZE", "其它业务保存库存变动通知分页大小，格式：|会员=分页大小|"),

    /**
     * 库存详情保存优化，格式：|会员1|会员2|
     */
    STOCK_DETAIL_SAVE_OPTIMIZE("STOCK_DETAIL_SAVE_OPTIMIZE", "库存详情保存优化，格式：|会员1|会员2|"),

    /**
     * 库存详情限制大小，格式：|会员=限制大小|
     */
    STOCK_DETAIL_SAVE_LIMIT_SIZE("STOCK_DETAIL_SAVE_LIMIT_SIZE", "库存详情限制大小，格式：|会员=限制大小|"),

    /**
     * 库存同步无需触发通知的单品skuId
     */
    STOCK_SINGLE_GOODS_SKU_NOT_NEED_TRIGGER_CONFIG("STOCK_SINGLE_GOODS_SKU_NOT_NEED_TRIGGER_CONFIG", "库存同步无需触发通知的单品skuId（格式：|会员1=单品skuId1#单品skuId2|）"),

    /**
     * 库存同步无需触发通知的单品标记ID
     */
    STOCK_SINGLE_GOODS_FLAG_NOT_NEED_TRIGGER_CONFIG("STOCK_SINGLE_GOODS_FLAG_NOT_NEED_TRIGGER_CONFIG", "库存同步无需触发通知的单品标记ID（格式：|会员1=系统商品标记1#系统商品标记2|）"),

    /**
     * 手动库存同步执行超时时间，默认 5，单位：秒
     */
    STOCK_SYNC_MANUAL_EXEC_TIMEOUT("STOCK_SYNC_MANUAL_EXEC_TIMEOUT", "手动库存同步执行超时时间，默认 5，单位：秒"),

    /**
     * 库存同步请求拆分大小，格式：|平台值-会员=20|平台值-ALL=20|会员-ALL=20|DEFAULT=20|
     */
    STOCK_SYNC_REQUEST_SPLIT_SIZE("STOCK_SYNC_REQUEST_SPLIT_SIZE", "库存同步请求拆分大小，格式：|平台值-会员=20|平台值-ALL=20|会员-ALL=20|DEFAULT=20|"),

    /**
     * 库存同步请求最大串行大小，格式：|平台值-会员=100|平台值-ALL=100|会员-ALL=100|DEFAULT=100|
     */
    STOCK_SYNC_REQUEST_MAX_SERIAL_SIZE("STOCK_SYNC_REQUEST_MAX_SERIAL_SIZE", "库存同步请求最大串行大小，格式：|平台值-会员=100|平台值-ALL=100|会员-ALL=100|DEFAULT=100|"),

    /**
     * 库存同步请求相同商品串行，格式：|平台-会员名|
     */
    STOCK_SYNC_REQUEST_SAME_GOODS_SERIAL("STOCK_SYNC_REQUEST_SAME_GOODS_SERIAL", "库存同步请求相同商品串行，格式：|平台-会员名|"),

    /**
     * 库存同步请求相同商品分组，格式：|平台-会员名|
     */
    STOCK_SYNC_REQUEST_SAME_GOODS_GROUP("STOCK_SYNC_REQUEST_SAME_GOODS_GROUP", "库存同步请求相同商品分组，格式：|平台-会员名|"),

    /**
     * 京东到家库存同步校验商品编码
     */
    JDDJ_STOCK_VERIFY_GOODS_CODE("JDDJ_STOCK_VERIFY_GOODS_CODE", "京东到家库存同步校验商品编码，格式：|会员名|")

    ;

    // region 变量

    /**
     * 编码
     */
    private final String code;

    /**
     * 名称
     */
    private final String name;

    // endregion

    // region 构造方法

    /**
     * 构造方法
     *
     * @param code 编码
     * @param name 名称
     */
    StockBizConfigEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    // endregion

    // region 公共方法

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getName() {
        return this.name;
    }

    // endregion
}