package com.differ.jackyun.omsapi.user.biz.infrastructure.cache.local.plugins.shop;

import com.differ.jackyun.framework.component.pagehelper.util.StringUtil;
import com.differ.jackyun.omsapi.user.biz.infrastructure.cache.local.core.AbstractLocalCache;
import com.differ.jackyun.omsapi.user.biz.infrastructure.cache.remote.hash.shop.ApiShopAutoConfigCache;
import com.differ.jackyun.omsapi.user.biz.infrastructure.common.LogAdapter;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.shop.core.ApiShopAutoConfig;
import com.differ.jackyun.omsapibase.data.shopconf.enums.ShopAuthStatusEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.LogTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.shopconfig.ChannelStatusEnum;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.core.PolyAPIPlatEnum;
import com.differ.jackyun.omsapibase.infrastructure.utils.CollectionsUtil;
import com.differ.jackyun.omsapibase.infrastructure.utils.ExtUtils;
import com.differ.jackyun.omsapibase.infrastructure.utils.SpringResolveManager;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 获取指定平台类型的店铺内存缓存
 *
 * <AUTHOR>
 * @date 2023/5/15 10:49
 */
@Component
public class ApiShopPlatLocalCache extends AbstractLocalCache<String, List<ApiShopAutoConfig>> {

    // region 构造方法

    /**
     * 构造方法
     */
    public ApiShopPlatLocalCache() {
        this.cacheMaxSize = 20000;
        this.expire = ExtUtils.randomInt(3, 5);
    }

    // endregion

    // region 变量 & 常量

    /**
     * 分隔符
     */
    private final static String SPLIT_CHAR = "~";

    // endregion

    // region 公共方法

    /**
     * 单例
     *
     * @return 单例
     */
    public static ApiShopPlatLocalCache get() {
        return SpringResolveManager.resolve(ApiShopPlatLocalCache.class);
    }

    /**
     * 获取指定平台类型的店铺
     *
     * @param memberName 会员名
     * @param plat       平台类型
     * @return 结果
     */
    public Set<Long> getShopByPlat(String memberName, PolyAPIPlatEnum plat) {

        // 校验入参
        if (StringUtil.isEmpty(memberName) || plat == null) {
            return new HashSet<>();
        }

        // 查询缓存
        List<ApiShopAutoConfig> configs = super.getCacheThenSource(String.format("%s%s%s", memberName, SPLIT_CHAR, plat.getPlatValue()));
        if (CollectionsUtil.isBlank(configs)) {
            return new HashSet<>();
        }

        return configs.stream().map(ApiShopAutoConfig::getShopId).collect(Collectors.toSet());
    }

    /**
     * 是否存在指定平台店铺
     *
     * @param memberName 会员名
     * @param plat       平台类型
     * @return 是否存在
     */
    public boolean existAppointPlat(String memberName, PolyAPIPlatEnum plat) {
        Set<Long> shopIds = this.getShopByPlat(memberName, plat);
        return shopIds != null && !shopIds.isEmpty();
    }

    /**
     * 获取指定平台类型的有效店铺（过滤授权和渠道状态）
     *
     * @param memberName 会员名
     * @param plat       平台类型
     * @return 是否存在
     */
    public Set<Long> getActiveShops(String memberName, PolyAPIPlatEnum plat) {

        // 校验入参
        if (StringUtil.isEmpty(memberName) || plat == null) {
            return new HashSet<>();
        }

        // 查询缓存
        List<ApiShopAutoConfig> configs = super.getCacheThenSource(String.format("%s%s%s", memberName, SPLIT_CHAR, plat.getPlatValue()));
        if (CollectionsUtil.isBlank(configs)) {
            return new HashSet<>();
        }

        // 过滤授权状态和渠道状态有效的店铺
        return configs.stream().filter(t -> ShopAuthStatusEnum.AUTHORIZED.getValue().byteValue() == t.getAuthStatus()
                && ChannelStatusEnum.CHANNEL_NORMAL.getValue().equals(t.getShopStatus())).map(ApiShopAutoConfig::getShopId)
                .collect(Collectors.toSet());
    }

    // endregion

    // region 重写基类方法

    /**
     * 加载数据
     *
     * @param key 会员
     * @return 结果
     */
    @Override
    protected Optional<List<ApiShopAutoConfig>> loadSource(String key) {

        // 统计内存缓存重载资源次数
        LogAdapter.writeSystemLog(key, "内存缓存重载资源", () -> ExtUtils.stringBuilderAppend("获取指定平台类型的店铺"), LogTypeEnum.TRACE);

        // 解析缓存键
        String[] keys = key.split(SPLIT_CHAR);
        if (keys.length != 2) {
            return Optional.empty();
        }

        // 会员名
        String memberName = keys[0];

        // 平台类型
        PolyAPIPlatEnum plat = PolyAPIPlatEnum.getEnumByPlatValue(Integer.parseInt(keys[1]));

        // 店铺自动配置集合
        List<ApiShopAutoConfig> configs = new ArrayList<>();

        // 查询所有店铺Id
        List<Long> allShopIds = AllShopLocalCache.get().getCacheThenSource(memberName);
        List<List<Long>> subList = CollectionsUtil.subList(allShopIds, 100);
        if (subList == null) {
            return Optional.empty();
        }

        for (List<Long> shopIds : subList) {

            // 查询自动配置缓存
            List<ApiShopAutoConfig> autoConfigs = ApiShopAutoConfigCache.build(memberName).getAutoConfigs(shopIds);
            if (autoConfigs == null || autoConfigs.isEmpty()) {
                continue;
            }

            // 获取指定类型店铺
            for (ApiShopAutoConfig autoConfig : autoConfigs) {
                if (autoConfig != null && plat.getPlatValue() == autoConfig.getShopType()) {
                    configs.add(autoConfig);
                }
            }
        }

        return Optional.of(configs);
    }

    // endregion
}