package com.differ.jackyun.omsapi.user.biz.infrastructure.cache.remote.hash.membercontrol.plugins.send;

import com.differ.jackyun.omsapi.user.biz.infrastructure.cache.remote.hash.membercontrol.AbstractMemberControlCache;
import com.differ.jackyun.omsapibase.infrastructure.utils.SpringResolveManager;
import org.springframework.stereotype.Component;

/**
 * 强制发货检查会员缓存
 *
 * <AUTHOR>
 * @since 2024-08-06 下午 4:17
 */
@Component("OvertimeForceSendCheckUserCache")
public class OvertimeForceSendCheckUserCache extends AbstractMemberControlCache {

    /**
     * 获取缓存代理对象
     *
     * @return 缓存代理对象
     */
    public static OvertimeForceSendCheckUserCache get() {
        return SpringResolveManager.resolve(OvertimeForceSendCheckUserCache.class);
    }

    /**
     * 业务类型
     *
     * @return 业务类型
     */
    @Override
    public String businessType() {
        return "send.trigger.overtimeforcesendcheckuser";
    }

    /**
     * 清除标记限制（超过约定次数后删除会员）、
     *
     * @return 最大次数
     */
    @Override
    public long clearSignLimit() {
        return 2L;
    }
}
