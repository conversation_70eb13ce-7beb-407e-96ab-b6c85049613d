package com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.auth.subscribe;

import com.differ.jackyun.omsapi.user.biz.domain.authorization.data.config.item.AuthSubscribeAddressItem;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.enums.authorization.SubscribeCompensateTypeEnum;

import java.util.Dictionary;
import java.util.List;

/**
 * @Description 补偿消息内容
 * <AUTHOR>
 * @Date 2025/2/11 14:28
 */
public class SubscribeCompensateContent {

    // region 变量

    /**
     * 1 通用店铺消息订阅 2 淘宝用户开通消息业务 {@link SubscribeCompensateTypeEnum}
     */
    private int compensateType;

    // region 通用订阅
    /**
     * 订阅地址
     */
    private List<AuthSubscribeAddressItem> subscribeAddresses;

    /**
     * 推送地址
     */
    private String pushUrl;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 是否增量订阅
     */
    private Boolean permitAdd;

    // endregion

    // region 淘宝为用户开通消息服务
    /**
     * 消息主题列表 多个用","分隔
     */
    private String topics;

    // endregion

    // region getter & setter


    public List<AuthSubscribeAddressItem> getSubscribeAddresses() {
        return subscribeAddresses;
    }

    public void setSubscribeAddresses(List<AuthSubscribeAddressItem> subscribeAddresses) {
        this.subscribeAddresses = subscribeAddresses;
    }

    public String getPushUrl() {
        return pushUrl;
    }

    public void setPushUrl(String pushUrl) {
        this.pushUrl = pushUrl;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Boolean getPermitAdd() {
        return permitAdd;
    }

    public void setPermitAdd(Boolean permitAdd) {
        this.permitAdd = permitAdd;
    }

    public String getTopics() {
        return topics;
    }

    public void setTopics(String topics) {
        this.topics = topics;
    }

    public int getCompensateType() {
        return compensateType;
    }

    public void setCompensateType(int compensateType) {
        this.compensateType = compensateType;
    }

    // endregion
}
