package com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.biz.goods;

import com.differ.jackyun.omsapi.user.biz.domain.core.ApiShopCoreUtils;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.shop.core.ApiShopBaseInfo;
import com.differ.jackyun.omsapibase.data.goods.bizgoods.BizOnlineGoodsOfSkuValueEntity;
import com.differ.jackyun.omsapibase.infrastructure.utils.CollectionsUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * @Decription 网店商品导入ERP组合装响应
 * <AUTHOR>
 * @Date 2023-12-19 11:17
 **/
public class BizOnlineGoodsImportErpFitResponse {

    // region 变量
    /**
     * 总数
     */
    private Integer totalCount;

    /**
     * 成功数
     */
    private Integer successCount;

    /**
     * 失败数
     */
    private Integer failCount;

    private List<ImportErpFitResult> results;

    public List<ImportErpFitResult> getResults() {
        return results;
    }

    public void setResults(List<ImportErpFitResult> results) {
        this.results = results;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Integer successCount) {
        this.successCount = successCount;
    }

    public Integer getFailCount() {
        return failCount;
    }

    public void setFailCount(Integer failCount) {
        this.failCount = failCount;
    }

    /**
     * 追加结果
     *
     * @param result 结果
     */
    public void appendResult(ImportErpFitResult result) {
        if (null == result) {
            return;
        }
        if (null == this.results) {
            this.results = new ArrayList<>();
        }
        this.results.add(result);
    }

    public void appendResult(List<ImportErpFitResult> results) {
        if (CollectionsUtil.isBlank(results)) {
            return;
        }
        if (null == this.results) {
            this.results = new ArrayList<>();
        }
        this.results.addAll(results);
    }

    /**
     * 追加结果
     *
     * @param memberName 会员
     * @param bizSku     规格数据
     * @param success    是否成功
     * @param msg        消息
     */
    public void appendResult(String memberName, BizOnlineGoodsOfSkuValueEntity bizSku, boolean success, String msg) {
        ImportErpFitResult result = makeImportErpFitResult(memberName, bizSku, success, msg);
        appendResult(result);
    }

    /**
     * 快速创建导入ERP组合结果对象
     *
     * @param memberName 会员
     * @param bizSku     规格数据
     * @param success    是否成功
     * @param msg        消息
     * @return 结果对象
     */
    public static ImportErpFitResult makeImportErpFitResult(String memberName, BizOnlineGoodsOfSkuValueEntity bizSku, boolean success, String msg) {
        ImportErpFitResult result = new ImportErpFitResult();
        result.setPlatGoodsGuid(bizSku.getPlatGoodsGuid());
        result.setShopIdStr(bizSku.getShopId().toString());

        // 获取店铺名称
        ApiShopBaseInfo apiShopBaseInfo = ApiShopCoreUtils.getShopBase(memberName, bizSku.getShopId(), false);
        if (null != apiShopBaseInfo) {
            result.setShopName(apiShopBaseInfo.getShopName());
        }
        result.setPlatGoodsName(bizSku.getPlatGoodsName());
        result.setPlatGoodsOuterId(bizSku.getPlatOuterId());
        result.setPlatSkuName(bizSku.getPlatSkuName());
        result.setPlatSkuOuterId(bizSku.getPlatOuterSkuId());
        result.setPlatGoodsId(bizSku.getPlatGoodsId());
        result.setPlatSkuId(bizSku.getPlatSkuId());
        result.setSuccess(success);
        result.setMsg(msg);

        return result;
    }


    // endregion

    // region 公共方法

    // endregion

    public static class ImportErpFitResult {

        private String platGoodsGuid;

        /**
         * 店铺ID
         */
        private String shopIdStr;

        /**
         * 店铺名称
         */
        private String shopName;

        /**
         * 网店商品名称
         */
        private String platGoodsName;

        /**
         * 网店商品编码
         */
        private String platGoodsOuterId;

        /**
         * 网店规格名称
         */
        private String platSkuName;

        /**
         * 网店规格编码
         */
        private String platSkuOuterId;

        /**
         * 网点商品ID
         */
        private String platGoodsId;

        /**
         * 网店商品规格ID
         */
        private String platSkuId;

        /**
         * 是否成功
         */
        private Boolean success;

        /**
         * 消息
         */
        private String msg;


        public String getShopIdStr() {
            return shopIdStr;
        }

        public void setShopIdStr(String shopIdStr) {
            this.shopIdStr = shopIdStr;
        }

        public String getShopName() {
            return shopName;
        }

        public void setShopName(String shopName) {
            this.shopName = shopName;
        }

        public String getPlatGoodsName() {
            return platGoodsName;
        }

        public void setPlatGoodsName(String platGoodsName) {
            this.platGoodsName = platGoodsName;
        }

        public String getPlatGoodsOuterId() {
            return platGoodsOuterId;
        }

        public void setPlatGoodsOuterId(String platGoodsOuterId) {
            this.platGoodsOuterId = platGoodsOuterId;
        }

        public String getPlatSkuName() {
            return platSkuName;
        }

        public void setPlatSkuName(String platSkuName) {
            this.platSkuName = platSkuName;
        }

        public String getPlatSkuOuterId() {
            return platSkuOuterId;
        }

        public void setPlatSkuOuterId(String platSkuOuterId) {
            this.platSkuOuterId = platSkuOuterId;
        }

        public Boolean getSuccess() {
            return success;
        }

        public void setSuccess(Boolean success) {
            this.success = success;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

        public String getPlatGoodsGuid() {
            return platGoodsGuid;
        }

        public void setPlatGoodsGuid(String platGoodsGuid) {
            this.platGoodsGuid = platGoodsGuid;
        }

        public String getPlatGoodsId() {
            return platGoodsId;
        }

        public void setPlatGoodsId(String platGoodsId) {
            this.platGoodsId = platGoodsId;
        }

        public String getPlatSkuId() {
            return platSkuId;
        }

        public void setPlatSkuId(String platSkuId) {
            this.platSkuId = platSkuId;
        }
    }

}
