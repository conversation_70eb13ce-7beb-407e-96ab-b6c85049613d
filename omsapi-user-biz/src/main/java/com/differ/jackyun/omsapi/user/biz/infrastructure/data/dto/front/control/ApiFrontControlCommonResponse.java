package com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.front.control;

/**
 * API 前端控件通用响应
 *
 * <AUTHOR>
 * @date 2024-03-28 18:35
 */
public class ApiFrontControlCommonResponse {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 成功响应
     *
     * @return 响应
     */
    public static ApiFrontControlCommonResponse createSuccess() {
        ApiFrontControlCommonResponse response = new ApiFrontControlCommonResponse();
        response.setSuccess(true);
        return response;
    }

    /**
     * 成功响应
     *
     * @param message 错误信息
     * @return 响应
     */
    public static ApiFrontControlCommonResponse createFailed(String message) {
        ApiFrontControlCommonResponse response = new ApiFrontControlCommonResponse();
        response.setSuccess(false);
        response.setMessage(message);
        return response;
    }

    // region getter & setter

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }


    // endregion
}
