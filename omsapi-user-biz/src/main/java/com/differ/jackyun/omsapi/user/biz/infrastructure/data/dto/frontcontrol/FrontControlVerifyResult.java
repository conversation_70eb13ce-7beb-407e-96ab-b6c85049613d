package com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.frontcontrol;

import com.differ.jackyun.omsapi.user.biz.infrastructure.data.enums.PlatBizFeatureTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.core.PolyAPIPlatEnum;

/**
 * 前端动态校验结果(仅设置可能会设置默认值的属性)
 *
 * <AUTHOR>
 * @date 2023/9/15 13:34
 */
public class FrontControlVerifyResult {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 前端业务类型
     */
    private Integer type;

    /**
     * 平台值
     *
     * @see com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.core.PolyAPIPlatEnum
     */
    private Integer platValue;

    /**
     * 父类id
     */
    private Integer parentId;

    /**
     * 当父控件的值等于此值时，显示子控件集合
     */
    private Integer parentValue;

    /**
     * 字段名
     */
    private String field;

    /**
     * 标题
     */
    private String caption;

    /**
     * 前端控件类型
     */
    private Integer controlType;

    /**
     *是否允许为空
     */
    private Byte allowEmpty;

    /**
     * 水印提示文字
     */
    private String watermark;

    /**
     * 下拉数据取值方式
     */
    private Integer optionalSrc;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 成功
     *
     * @return 结果
     */
    public static FrontControlVerifyResult createSuccess() {
        FrontControlVerifyResult response = new FrontControlVerifyResult();
        response.setSuccess(true);
        return response;
    }

    /**
     * 失败
     *
     * @param message 错误信息
     * @return 响应
     */
    public static FrontControlVerifyResult createFailed(String message) {
        FrontControlVerifyResult response = new FrontControlVerifyResult();
        response.setSuccess(false);
        response.setMessage(message);
        return response;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getPlatValue() {
        return platValue;
    }

    public void setPlatValue(Integer platValue) {
        this.platValue = platValue;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public Integer getParentValue() {
        return parentValue;
    }

    public void setParentValue(Integer parentValue) {
        this.parentValue = parentValue;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getCaption() {
        return caption;
    }

    public void setCaption(String caption) {
        this.caption = caption;
    }

    public Integer getControlType() {
        return controlType;
    }

    public void setControlType(Integer controlType) {
        this.controlType = controlType;
    }

    public Byte getAllowEmpty() {
        return allowEmpty;
    }

    public void setAllowEmpty(Byte allowEmpty) {
        this.allowEmpty = allowEmpty;
    }

    public String getWatermark() {
        return watermark;
    }

    public void setWatermark(String watermark) {
        this.watermark = watermark;
    }

    public Integer getOptionalSrc() {
        return optionalSrc;
    }

    public void setOptionalSrc(Integer optionalSrc) {
        this.optionalSrc = optionalSrc;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
}
