package com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.shop.request;

import java.util.List;

/**
 * MQ消息-店铺数据转换
 *
 * <AUTHOR>
 * @date 2021-11-04 14:06
 */
public class ApiShopDataConvertMqMessage {

    /**
     * 会员名
     */
    private String memberName;

    /**
     * 是否同步所有店铺
     */
    private boolean syncAll;

    /**
     * 店铺Id集合
     */
    private List<Long> shopIds;

    /**
     * 创建实例
     *
     * @param memberName 会员名
     * @return 实例
     */
    public static ApiShopDataConvertMqMessage create(String memberName) {
        ApiShopDataConvertMqMessage mqMessage = new ApiShopDataConvertMqMessage();
        mqMessage.setMemberName(memberName);
        mqMessage.setSyncAll(true);
        return mqMessage;
    }

    /**
     * 创建实例
     *
     * @param memberName 会员名
     * @param shopIds    店铺集合
     * @return 实例
     */
    public static ApiShopDataConvertMqMessage create(String memberName, List<Long> shopIds) {
        ApiShopDataConvertMqMessage mqMessage = new ApiShopDataConvertMqMessage();
        mqMessage.setMemberName(memberName);
        mqMessage.setShopIds(shopIds);
        mqMessage.setSyncAll(false);
        return mqMessage;
    }

    // region getter & setter

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public boolean isSyncAll() {
        return syncAll;
    }

    public void setSyncAll(boolean syncAll) {
        this.syncAll = syncAll;
    }

    public List<Long> getShopIds() {
        return shopIds;
    }

    public void setShopIds(List<Long> shopIds) {
        this.shopIds = shopIds;
    }

    // endregion
}
