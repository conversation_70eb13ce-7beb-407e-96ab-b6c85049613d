package com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.syncstock.request;

import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.core.PolyAPIPlatEnum;

import java.time.LocalDateTime;

/**
 * 库存同步日志查询
 *
 * <AUTHOR>
 * @date 2021/6/23 17:19
 */
public class ApiStockSyncLogQo extends BaseSyncStockRequestBizData {

    // region 精确查询参数

    /**
     * 会员名
     */
    private String memberName;

    /**
     * 平台商品唯一键
     */
    private String platGoodsGuid;

    /**
     * 同步开始时间
     */
    private LocalDateTime synTimeBegin;

    /**
     * 同步结束时间
     */
    private LocalDateTime synTimeEnd;

    /**
     * 同步数量
     */
    private Integer syncQuantity;

    /**
     * 数量判断符号
     */
    private String syncQuantitySymbol;

    /**
     * 同步结果
     */
    private Integer success;

    /**
     * 平台返回数量
     */
    private Integer platCount;

    /**
     * 平台返回数量判断符号
     */
    private String platCountSymbol;

    /**
     * 是否例外规则
     */
    private Integer extraRule;

    // endregion

    // region 模糊查询参数

    /**
     * 多仓标识
     */
    private String multiSign;

    /**
     * 多仓标识名称
     */
    private String multiSignName;

    /**
     * 日志内容(仅支持新界面模糊搜索)
     */
    private String syncCause;

    /**
     * 操作员
     */
    private String userName;

    // endregion

    /**
     * 分页的大小（主要用于组合，主查询的分页在http请求里）
     */
    private Integer pageSize;

    /**
     * 分页的页码（主要用于组合，主查询的分页在http请求里）
     */
    private Integer pageIndex;

    // region 其它

    /**
     * 平台
     */
    private PolyAPIPlatEnum plat;

    // endregion

    public String getSyncCause() {
        return syncCause;
    }

    public void setSyncCause(String syncCause) {
        this.syncCause = syncCause;
    }

    public String getPlatGoodsGuid() {
        return platGoodsGuid;
    }

    public void setPlatGoodsGuid(String platGoodsGuid) {
        this.platGoodsGuid = platGoodsGuid;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public LocalDateTime getSynTimeBegin() {
        return synTimeBegin;
    }

    public void setSynTimeBegin(LocalDateTime synTimeBegin) {
        this.synTimeBegin = synTimeBegin;
    }

    public LocalDateTime getSynTimeEnd() {
        return synTimeEnd;
    }

    public void setSynTimeEnd(LocalDateTime synTimeEnd) {
        this.synTimeEnd = synTimeEnd;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getSyncQuantity() {
        return syncQuantity;
    }

    public void setSyncQuantity(Integer syncQuantity) {
        this.syncQuantity = syncQuantity;
    }

    public String getSyncQuantitySymbol() {
        return syncQuantitySymbol;
    }

    public void setSyncQuantitySymbol(String syncQuantitySymbol) {
        this.syncQuantitySymbol = syncQuantitySymbol;
    }

    public Integer getPlatCount() {
        return platCount;
    }

    public void setPlatCount(Integer platCount) {
        this.platCount = platCount;
    }

    public String getPlatCountSymbol() {
        return platCountSymbol;
    }

    public void setPlatCountSymbol(String platCountSymbol) {
        this.platCountSymbol = platCountSymbol;
    }

    public Integer getExtraRule() {
        return extraRule;
    }

    public void setExtraRule(Integer extraRule) {
        this.extraRule = extraRule;
    }

    public String getMultiSign() {
        return multiSign;
    }

    public void setMultiSign(String multiSign) {
        this.multiSign = multiSign;
    }

    public String getMultiSignName() {
        return multiSignName;
    }

    public void setMultiSignName(String multiSignName) {
        this.multiSignName = multiSignName;
    }

    public Integer getSuccess() {
        return success;
    }

    public void setSuccess(Integer success) {
        this.success = success;
    }

    public PolyAPIPlatEnum getPlat() {
        return plat;
    }

    public void setPlat(PolyAPIPlatEnum plat) {
        this.plat = plat;
    }
}
