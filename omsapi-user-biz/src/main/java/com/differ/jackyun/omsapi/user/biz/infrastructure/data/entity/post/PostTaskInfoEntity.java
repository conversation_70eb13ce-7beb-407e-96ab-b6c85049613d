package com.differ.jackyun.omsapi.user.biz.infrastructure.data.entity.post;

import com.differ.jackyun.omsapibase.infrastructure.enums.post.TaskStatusEnum;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.getorder.extra.BusinessGetOrderRequest_TimeTypeEnum;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 下载订单任务信息缓存
 *
 * <AUTHOR>
 * @since 2023-02-08  16:29:44
 */
public class PostTaskInfoEntity {

    //region 字段

    /**
     * 查询开始时间
     */
    private LocalDateTime startTime;

    /**
     * 查询结束时间
     */
    private LocalDateTime endTime;

    /**
     * 查询时间类型
     */
    private BusinessGetOrderRequest_TimeTypeEnum timeType;

    /**
     * 订单增量id
     */
    private int orderId;

    /**
     * 订单总数
     */
    private int sum;

    /**
     * 成功数量
     */
    private int successCount;
    /**
     * 失败数量
     */
    private int failCount;

    /**
     * 任务状态
     */
    private TaskStatusEnum status;

    /**
     * 任务时间戳
     */
    private long taskTime;

//    /**
//     * 递交方式（0 普通递交 1 强制递交）
//     */
//    private Integer billType;


    //endregion

    //region getter setter

    public LocalDateTime getStartTime () {
        return startTime;
    }

    public void setStartTime (LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime () {
        return endTime;
    }

    public void setEndTime (LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public BusinessGetOrderRequest_TimeTypeEnum getTimeType () {
        return timeType;
    }

    public void setTimeType (BusinessGetOrderRequest_TimeTypeEnum timeType) {
        this.timeType = timeType;
    }

    public int getOrderId () {
        return orderId;
    }

    public void setOrderId (int orderId) {
        this.orderId = orderId;
    }

    public int getSum () {
        return sum;
    }

    public void setSum (int sum) {
        this.sum = sum;
    }

    public int getSuccessCount () {
        return successCount;
    }

    public void setSuccessCount (int successCount) {
        this.successCount = successCount;
    }

    public int getFailCount () {
        return failCount;
    }

    public void setFailCount (int failCount) {
        this.failCount = failCount;
    }

    public TaskStatusEnum getStatus () {
        return status;
    }

    public void setStatus (TaskStatusEnum status) {
        this.status = status;
    }

    public long getTaskTime () {
        return taskTime;
    }

    public void setTaskTime (long taskTime) {
        this.taskTime = taskTime;
    }

//    public Integer getBillType () {
//        return billType;
//    }
//
//    public void setBillType (Integer billType) {
//        this.billType = billType;
//    }


    //endregion


}
