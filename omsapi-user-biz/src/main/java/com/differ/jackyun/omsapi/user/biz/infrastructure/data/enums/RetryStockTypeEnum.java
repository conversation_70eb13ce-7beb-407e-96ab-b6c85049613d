package com.differ.jackyun.omsapi.user.biz.infrastructure.data.enums;

/**
 * 库存同步重试类型
 *
 * <AUTHOR>
 * @date 2023/2/8 17:06
 */
public enum RetryStockTypeEnum {

    /**
     * 普通库存重试
     */
    NORMAL_RETRY_STOCK((byte) 0, "普通库存重试"),

    /**
     * 多仓库存重试
     */
    MULTI_RETRY_STOCK((byte) 1, "多仓库存重试"),
    ;

    /**
     * @param code 值
     * @param name 名称
     */
    RetryStockTypeEnum(Byte code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 值
     */
    private final Byte code;

    /**
     * 名称
     */
    private final String name;


    public Byte getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
