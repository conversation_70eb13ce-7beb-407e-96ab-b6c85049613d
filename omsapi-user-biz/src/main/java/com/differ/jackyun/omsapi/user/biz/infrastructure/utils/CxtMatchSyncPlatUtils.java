package com.differ.jackyun.omsapi.user.biz.infrastructure.utils;

import com.differ.jackyun.omsapi.user.biz.domain.apicall.data.goods.request.PolyApiMatchSyncRequestBizData;
import com.differ.jackyun.omsapibase.data.goods.open.ApiTUGoodsMatchSyncResultEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 诚信通匹配同步平台工具类
 * <AUTHOR>
 * @Date 2024/3/5 17:25
 */
public class CxtMatchSyncPlatUtils {

    /**
     * 转换数据
     *
     * @param goodsMatchSyncResultEntityList 匹配数据
     * @return 请求数据
     */
    public static List<PolyApiMatchSyncRequestBizData.MatchInfo> convertGoodsMatchData(List<ApiTUGoodsMatchSyncResultEntity> goodsMatchSyncResultEntityList) {

        List<PolyApiMatchSyncRequestBizData.MatchInfo> result = new ArrayList<>();

        for (ApiTUGoodsMatchSyncResultEntity goodsMatch : goodsMatchSyncResultEntityList) {
            PolyApiMatchSyncRequestBizData.MatchInfo matchInfo = new PolyApiMatchSyncRequestBizData.MatchInfo();
            matchInfo.setItemId(goodsMatch.getPlatGoodsId());
            matchInfo.setSkuId(goodsMatch.getPlatSkuId());
            matchInfo.setErpGoodsId(String.valueOf(goodsMatch.getSkuId()));
            matchInfo.setGoodsRelationId(goodsMatch.getPlatGoodsGuid());
            result.add(matchInfo);
        }

        return result;
    }


}
