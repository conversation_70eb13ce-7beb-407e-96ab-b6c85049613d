package com.differ.jackyun.omsapi.user.biz.infrastructure.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.differ.jackyun.framework.component.pagehelper.util.StringUtil;
import com.differ.jackyun.omsapi.component.util.anno.Out;
import com.differ.jackyun.omsapi.core.infrastructure.config.business.ConfigUtil;
import com.differ.jackyun.omsapi.user.biz.domain.apicall.data.ApiCallResponse;
import com.differ.jackyun.omsapi.user.biz.infrastructure.common.LogAdapter;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.constants.SaveOrderConstant;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.rds.RDSNormalGoodsRefundResponseDTO;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.rds.RDSNormalOrderRefundResponseDTO;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.rds.RDSOrderBaseResponseDTO;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.enums.order.OrderGovSubsidyEnum;
import com.differ.jackyun.omsapibase.data.common.YesOrNo;
import com.differ.jackyun.omsapibase.data.constant.OmsApiConstant;
import com.differ.jackyun.omsapibase.data.open.ApiPlatFlagEnum;
import com.differ.jackyun.omsapibase.data.open.omsonline.TradeOnlineGoods;
import com.differ.jackyun.omsapibase.data.rdsorder.RDSShopTypesEnum;
import com.differ.jackyun.omsapibase.data.rdsorder.TBGiftPostFeeRoleEnum;
import com.differ.jackyun.omsapibase.data.rdsorder.TBPostFeeTypeEnum;
import com.differ.jackyun.omsapibase.data.rdsorder.TBYpdsOrderTypeEnum;
import com.differ.jackyun.omsapibase.data.rdsorder.response.rds.RDSOrderEntity;
import com.differ.jackyun.omsapibase.data.rdsorder.response.standard.IRDSResponse;
import com.differ.jackyun.omsapibase.data.rdsorder.response.standard.RDSExchangeOrderResponse;
import com.differ.jackyun.omsapibase.data.rdsorder.response.standard.RDSNormalOrderResponse;
import com.differ.jackyun.omsapibase.data.rdsorder.response.standard.RDSRefundOrderResponse;
import com.differ.jackyun.omsapibase.data.rdsorder.response.taobao.*;
import com.differ.jackyun.omsapibase.domain.core.DomainUtils;
import com.differ.jackyun.omsapibase.infrastructure.component.mq.data.worker.core.ext.RefundShopTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.ApplicationTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.ConfigKeyEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.ErrorCodes;
import com.differ.jackyun.omsapibase.infrastructure.enums.LogTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.order.OrderOriginalTradeTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.order.TaobaoActivityStatusEnum;
import com.differ.jackyun.omsapibase.infrastructure.exception.AppException;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.enums.*;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.getorder.PolyAPIBusinessGetOrderResponseBizData;
import com.differ.jackyun.omsapibase.infrastructure.platrequest.polyapi.business.getorder.extra.*;
import com.differ.jackyun.omsapibase.infrastructure.utils.*;
import com.differ.jackyun.omsapibase.infrastructure.utils.core.TimeFormatEnum;
import com.github.houbb.paradise.common.util.ArrayUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * RDS抓单相关的公共方法封装类
 *
 * <AUTHOR>
 * @since 2019-04-29  14:19:34
 */
public class RDSTBOrderUtils {

    //region 变量

    /**
     * 淘宝承诺发货时间标签类型
     */
    private static final String TB_CONSIGN_DATE = "TB_CONSIGN_DATE";

    /**
     * 百亿补贴
     */
    private static final String BILLION_PROMOTION_KEY = "BILLION_PROMOTION";

    /**
     * 百亿补贴
     */
    private static final String BILLION_PROMOTION_NAME = "百亿补贴";

    /**
     * 创建交易时的物流方式（交易完成前，物流方式有可能改变，但系统里的这个字段一直不变）。
     * 可选值：free(卖家包邮),post(平邮),express(快递),ems(EMS),virtual(虚拟发货)，25(次日必达)，26(预约配送)。
     */
    private static Map<String, String> mapShippingType = new HashMap<String, String>() {
        {
            put("free", "卖家包邮");
            put("post", "平邮");
            put("express", "快递");
            put("ems", "EMS");
            put("virtual", "虚拟发货");
            put("25", "次日必达");
            put("26", "预约配送");
        }
    };

    /**
     * 菠萝派对应值
     * 创建交易时的物流方式（交易完成前，物流方式有可能改变，但系统里的这个字段一直不变）。
     * 可选值：free(卖家包邮),post(平邮),express(快递),ems(EMS),virtual(虚拟发货)，25(次日必达)，26(预约配送)。
     */
    private static Map<String, BusinessGetOrderResponseOrderItem_SendTypeEnum> mapPolyShippingType = new HashMap<String, BusinessGetOrderResponseOrderItem_SendTypeEnum>() {
        {
            put("free", BusinessGetOrderResponseOrderItem_SendTypeEnum.JH_EXPRESSSEND);
            put("post", BusinessGetOrderResponseOrderItem_SendTypeEnum.JH_EXPRESSSEND);
            put("express", BusinessGetOrderResponseOrderItem_SendTypeEnum.JH_EXPRESSSEND);
            put("ems", BusinessGetOrderResponseOrderItem_SendTypeEnum.JH_EXPRESSSEND);
            put("virtual", BusinessGetOrderResponseOrderItem_SendTypeEnum.JH_VIRTUAL);
            put("25", BusinessGetOrderResponseOrderItem_SendTypeEnum.JH_EXPRESSSEND);
            put("26", BusinessGetOrderResponseOrderItem_SendTypeEnum.JH_RESERVE);
        }
    };

    /**
     * 菠萝派对应值
     * 分销供应商备注旗帜对应
     */
    public static Map<Integer, BusinessGetOrderResponseOrderItem_SellerFlagEnum> mapSellerFlag = new HashMap<Integer, BusinessGetOrderResponseOrderItem_SellerFlagEnum>() {
        {
            put(0, BusinessGetOrderResponseOrderItem_SellerFlagEnum.JH_GRAY);
            put(1, BusinessGetOrderResponseOrderItem_SellerFlagEnum.JH_RED);
            put(2, BusinessGetOrderResponseOrderItem_SellerFlagEnum.JH_YELLOW);
            put(3, BusinessGetOrderResponseOrderItem_SellerFlagEnum.JH_GREEN);
            put(4, BusinessGetOrderResponseOrderItem_SellerFlagEnum.JH_BLUE);
            put(5, BusinessGetOrderResponseOrderItem_SellerFlagEnum.JH_PURPLE);
            put(6, BusinessGetOrderResponseOrderItem_SellerFlagEnum.JH_ORANGE);
            put(7, BusinessGetOrderResponseOrderItem_SellerFlagEnum.JH_LIGHTBLUE);
            put(8, BusinessGetOrderResponseOrderItem_SellerFlagEnum.JH_LIGHTPINK);
            put(9, BusinessGetOrderResponseOrderItem_SellerFlagEnum.JH_DEEPGREEN);
            put(10, BusinessGetOrderResponseOrderItem_SellerFlagEnum.JH_PEACHBLOW);
        }
    };

    /**
     * 订单返回交易状态。可选值:
     * * TRADE_NO_CREATE_PAY(没有创建支付宝交易) * WAIT_BUYER_PAY(等待买家付款) * SELLER_CONSIGNED_PART(卖家部分发货) * WAIT_SELLER_SEND_GOODS(等待卖家发货,即:买家已付款) * WAIT_BUYER_CONFIRM_GOODS(等待买家确认收货,即:卖家已发货) * TRADE_BUYER_SIGNED(买家已签收,货到付款专用) * TRADE_FINISHED(交易成功) * TRADE_CLOSED(付款以后用户退款成功，交易自动关闭) * TRADE_CLOSED_BY_TAOBAO(付款以前，卖家或买家主动关闭交易) * PAY_PENDING(国际信用卡支付付款确认中) * WAIT_PRE_AUTH_CONFIRM(0元购合约中)
     */
    private static Map<String, Business_OrderStatusEnum> mapResponseStatus = new HashMap<String, Business_OrderStatusEnum>() {
        {
            put("TRADE_NO_CREATE_PAY", Business_OrderStatusEnum.JH_01);
            put("WAIT_BUYER_PAY", Business_OrderStatusEnum.JH_01);
            put("PAY_PENDING", Business_OrderStatusEnum.JH_01);
            put("WAIT_PRE_AUTH_CONFIRM", Business_OrderStatusEnum.JH_01);
            put("SELLER_CONSIGNED_PART", Business_OrderStatusEnum.JH_08);
            put("WAIT_SELLER_SEND_GOODS", Business_OrderStatusEnum.JH_02);
            put("WAIT_BUYER_CONFIRM_GOODS", Business_OrderStatusEnum.JH_03);
            put("TRADE_BUYER_SIGNED", Business_OrderStatusEnum.JH_04);
            put("TRADE_FINISHED", Business_OrderStatusEnum.JH_04);
            put("TRADE_CLOSED", Business_OrderStatusEnum.JH_05);
            put("TRADE_CLOSED_BY_TAOBAO", Business_OrderStatusEnum.JH_05);
            put("PAID_FORBID_CONSIGN", Business_OrderStatusEnum.JH_07);
            put("trade_no_create_pay", Business_OrderStatusEnum.JH_01);
            put("wait_buyer_pay", Business_OrderStatusEnum.JH_01);
            put("pay_pending", Business_OrderStatusEnum.JH_01);
            put("wait_pre_auth_confirm", Business_OrderStatusEnum.JH_01);
            put("seller_consigned_part", Business_OrderStatusEnum.JH_08);
            put("wait_seller_send_goods", Business_OrderStatusEnum.JH_02);
            put("wait_buyer_confirm_goods", Business_OrderStatusEnum.JH_03);
            put("trade_buyer_signed", Business_OrderStatusEnum.JH_04);
            put("trade_finished", Business_OrderStatusEnum.JH_04);
            put("trade_closed", Business_OrderStatusEnum.JH_05);
            put("trade_closed_by_taobao", Business_OrderStatusEnum.JH_05);
            put("paid_forbid_consign", Business_OrderStatusEnum.JH_07);
        }
    };

    /**
     * 订单退款状态
     */
    private static Map<String, Business_RefundStatuEnum> mapRefundStatus = new HashMap<String, Business_RefundStatuEnum>() {
        {
            put("WAIT_SELLER_AGREE", Business_RefundStatuEnum.JH_01);
            put("WAIT_BUYER_RETURN_GOODS", Business_RefundStatuEnum.JH_02);
            put("WAIT_SELLER_CONFIRM_GOODS", Business_RefundStatuEnum.JH_03);
            put("SELLER_REFUSE_BUYER", Business_RefundStatuEnum.JH_04);
            put("CLOSED", Business_RefundStatuEnum.JH_05);
            put("SUCCESS", Business_RefundStatuEnum.JH_06);
        }
    };

    /**
     * 订单退款状态
     */
    private static Map<Integer, Business_RefundStatuEnum> fxRefundStatusMap = new HashMap<Integer, Business_RefundStatuEnum>() {
        {
            put(1, Business_RefundStatuEnum.JH_01);
            put(2, Business_RefundStatuEnum.JH_02);
            put(3, Business_RefundStatuEnum.JH_03);
            put(4, Business_RefundStatuEnum.JH_05);
            put(5, Business_RefundStatuEnum.JH_06);
            put(6, Business_RefundStatuEnum.JH_04);
            put(12, Business_RefundStatuEnum.JH_08);
            put(9, Business_RefundStatuEnum.JH_06);
            put(10, Business_RefundStatuEnum.JH_11);
        }
    };

    /**
     * 订单状态（请关注此状态，如果为TRADE_CLOSED_BY_TAOBAO状态，则不要对此订单进行发货，切记啊！）。可选值:
     * <p>
     * TRADE_NO_CREATE_PAY(没有创建支付宝交易)
     * WAIT_BUYER_PAY(等待买家付款)
     * WAIT_SELLER_SEND_GOODS(等待卖家发货,即:买家已付款)
     * WAIT_BUYER_CONFIRM_GOODS(等待买家确认收货,即:卖家已发货)
     * TRADE_BUYER_SIGNED(买家已签收,货到付款专用)
     * TRADE_FINISHED(交易成功)
     * TRADE_CLOSED(付款以后用户退款成功，交易自动关闭)
     * TRADE_CLOSED_BY_TAOBAO(付款以前，卖家或买家主动关闭交易)
     * PAY_PENDING(国际信用卡支付付款确认中)
     */
    private static Map<String, BusinessGetOrderResponseOrderItem_SubOrderStatuEnum> mapSubOrderStatus = new HashMap<String, BusinessGetOrderResponseOrderItem_SubOrderStatuEnum>() {
        {
            put("TRADE_NO_CREATE_PAY", BusinessGetOrderResponseOrderItem_SubOrderStatuEnum.JH_01);
            put("WAIT_BUYER_PAY", BusinessGetOrderResponseOrderItem_SubOrderStatuEnum.JH_01);
            put("PAY_PENDING", BusinessGetOrderResponseOrderItem_SubOrderStatuEnum.JH_01);
            put("WAIT_SELLER_SEND_GOODS", BusinessGetOrderResponseOrderItem_SubOrderStatuEnum.JH_02);
            put("WAIT_BUYER_CONFIRM_GOODS", BusinessGetOrderResponseOrderItem_SubOrderStatuEnum.JH_03);
            put("TRADE_BUYER_SIGNED", BusinessGetOrderResponseOrderItem_SubOrderStatuEnum.JH_07);
            put("TRADE_FINISHED", BusinessGetOrderResponseOrderItem_SubOrderStatuEnum.JH_04);
            put("TRADE_CLOSED", BusinessGetOrderResponseOrderItem_SubOrderStatuEnum.JH_05);
            put("TRADE_CLOSED_BY_TAOBAO", BusinessGetOrderResponseOrderItem_SubOrderStatuEnum.JH_06);
            put("PAID_FORBID_CONSIGN", BusinessGetOrderResponseOrderItem_SubOrderStatuEnum.JH_10);
            put("SELLER_CONSIGNED_PART", BusinessGetOrderResponseOrderItem_SubOrderStatuEnum.JH_11);
        }
    };

    /**
     * 货运方式：SELF_PICKUP（自提）、LOGISTICS（物流发货)
     */
    private static Map<String, String> mapJXShipping = new HashMap<String, String>() {
        {
            put("SELF_PICKUP", "自提");
            put("LOGISTICS", "物流发货");
        }
    };

    /**
     * 货运方式：SELF_PICKUP（自提）、LOGISTICS（物流发货)
     */
    private static Map<String, BusinessGetOrderResponseOrderItem_SendTypeEnum> mapPolyJXShipping = new HashMap<String, BusinessGetOrderResponseOrderItem_SendTypeEnum>() {
        {
            put("SELF_PICKUP", BusinessGetOrderResponseOrderItem_SendTypeEnum.JH_FETCHSEND);
            put("LOGISTICS", BusinessGetOrderResponseOrderItem_SendTypeEnum.JH_EXPRESSSEND);
        }
    };

    /**
     * 平台支付方式字典
     */
    private static Map<String, BusinessGetOrderResponseOrderItem_PayTypeEnum> mapPolyPayType = new HashMap<String, BusinessGetOrderResponseOrderItem_PayTypeEnum>() {
        {
            put("ALIPAY_SURETY", BusinessGetOrderResponseOrderItem_PayTypeEnum.JH_ALIPAY);
            put("ALIPAY_CHAIN", BusinessGetOrderResponseOrderItem_PayTypeEnum.JH_ALIPAY);
            put("TRANSFER", BusinessGetOrderResponseOrderItem_PayTypeEnum.JH_OTHER);
            put("PREPAY", BusinessGetOrderResponseOrderItem_PayTypeEnum.JH_PREDEPOSIT);
            put("IMMEDIATELY", BusinessGetOrderResponseOrderItem_PayTypeEnum.JH_OTHER);
            put("CASHGOODS", BusinessGetOrderResponseOrderItem_PayTypeEnum.JH_OTHER);
            put("ACCOUNT_PERIOD", BusinessGetOrderResponseOrderItem_PayTypeEnum.JH_OTHER);
        }
    };

    /**
     * 经销订单返回状态
     * WAIT_FOR_SUPPLIER_AUDIT1：分销商提交申请，待供应商审核；
     * SUPPLIER_REFUSE：供应商驳回申请，待分销商确认；
     * WAIT_FOR_APPLIER_AUDIT：供应商修改后，待分销商确认；
     * WAIT_FOR_SUPPLIER_AUDIT2：分销商拒绝修改，待供应商再审核；
     * BOTH_AGREE_WAIT_PAY：审核通过下单成功，待分销商付款
     * WAIT_FOR_SUPPLIER_DELIVER：付款成功，待供应商发货；
     * WAIT_FOR_APPLIER_STORAGE：供应商发货，待分销商收货；
     * TRADE_FINISHED：分销商收货，交易成功；
     * TRADE_CLOSED：经销采购单关闭。
     */
    private static Map<String, Business_OrderStatusEnum> mapJXResponseStatus = new HashMap<String, Business_OrderStatusEnum>() {
        {
            put("WAIT_FOR_SUPPLIER_AUDIT1", Business_OrderStatusEnum.JH_01);
            put("SUPPLIER_REFUSE", Business_OrderStatusEnum.JH_01);
            put("WAIT_FOR_APPLIER_AUDIT", Business_OrderStatusEnum.JH_01);
            put("WAIT_FOR_SUPPLIER_AUDIT2", Business_OrderStatusEnum.JH_01);
            put("BOTH_AGREE_WAIT_PAY", Business_OrderStatusEnum.JH_01);
            put("WAIT_FOR_SUPPLIER_DELIVER", Business_OrderStatusEnum.JH_02);
            put("WAIT_FOR_APPLIER_STORAGE", Business_OrderStatusEnum.JH_03);
            put("TRADE_FINISHED", Business_OrderStatusEnum.JH_04);
            put("TRADE_CLOSED", Business_OrderStatusEnum.JH_05);
            put("TRADE_REFUNDING", Business_OrderStatusEnum.JH_16);
            put("PAID_FORBID_CONSIGN", Business_OrderStatusEnum.JH_07);
            put("wait_for_supplier_audit1", Business_OrderStatusEnum.JH_01);
            put("supplier_refuse", Business_OrderStatusEnum.JH_01);
            put("wait_for_applier_audit", Business_OrderStatusEnum.JH_01);
            put("wait_for_supplier_audit2", Business_OrderStatusEnum.JH_01);
            put("both_agree_wait_pay", Business_OrderStatusEnum.JH_01);
            put("wait_for_supplier_deliver", Business_OrderStatusEnum.JH_02);
            put("wait_for_applier_storage", Business_OrderStatusEnum.JH_03);
            put("trade_finished", Business_OrderStatusEnum.JH_04);
            put("trade_closed", Business_OrderStatusEnum.JH_05);
            put("paid_forbid_consign", Business_OrderStatusEnum.JH_07);
        }
    };

    /**
     * 分销货运方式，FAST(快递)、EMS、ORDINARY(平邮)、SELLER(卖家包邮)
     */
    private static Map<String, String> mapFXShipping = new HashMap<String, String>() {
        {
            put("FAST", "快递");
            put("EMS", "EMS");
            put("ORDINARY", "平邮");
            put("SELLER", "卖家包邮");
        }
    };

    /**
     * 分销货运方式
     */
    private static Map<String, BusinessGetOrderResponseOrderItem_SendTypeEnum> mapPolyFXShipping = new HashMap<String, BusinessGetOrderResponseOrderItem_SendTypeEnum>() {
        {
            put("FAST", BusinessGetOrderResponseOrderItem_SendTypeEnum.JH_EXPRESSSEND);
            put("EMS", BusinessGetOrderResponseOrderItem_SendTypeEnum.JH_EXPRESSSEND);
            put("ORDINARY", BusinessGetOrderResponseOrderItem_SendTypeEnum.JH_EXPRESSSEND);
            put("SELLER", BusinessGetOrderResponseOrderItem_SendTypeEnum.JH_EXPRESSSEND);
        }
    };

    /**
     * 分销订单退款状态
     * <p>
     * RF_STATUS_REFUNDING（子单无退款）
     * RF_STATUS_REFUNDING（子单退款中）
     * RF_STATUS_END_REFUND（子单退款结束）
     */
    private static Map<String, Business_RefundStatuEnum> mapFXRefundStatus = new HashMap<String, Business_RefundStatuEnum>() {
        {
            put("RF_STATUS_REFUNDING", Business_RefundStatuEnum.JH_01);
            put("RF_STATUS_END_REFUND", Business_RefundStatuEnum.JH_99);
            put("RF_STATUS_NO_REFUND", Business_RefundStatuEnum.JH_07);
        }
    };

    /**
     * 分销订单消费者退款状态
     */
    private static Map<String, Business_RefundStatuEnum> mapFXConsumerRefundStatus = new HashMap<String, Business_RefundStatuEnum>() {
        {
            put("TRADE_REFUNDED", Business_RefundStatuEnum.JH_06);
            put("TRADE_REFUNDING", Business_RefundStatuEnum.JH_01);
            put("WAIT_SELLER_SEND_GOODS", Business_RefundStatuEnum.JH_07);
            put("WAIT_BUYER_CONFIRM_GOODS", Business_RefundStatuEnum.JH_07);
            put("TRADE_CLOSED", Business_RefundStatuEnum.JH_06);
            put("TRADE_FINISHED", Business_RefundStatuEnum.JH_07);
            put("TRADE_CLOSED_BY_TAOBAO", Business_RefundStatuEnum.JH_07);
        }
    };

    /**
     * 分销子订单状态。可选值：
     * LOCKED(交易hold单中)
     * WAIT_FOR_PAY(等待付款)
     * PAID_WAIT_FOR_CONFIRM(付款信息待确认，待发货)
     * PAID(已付款，待发货)
     * CONSIGNED(已发货待确认收货)
     * ORDER_SUCCESS(交易成功)
     * ORDER_CLOSE(交易关闭)
     */
    private static Map<String, BusinessGetOrderResponseOrderItem_SubOrderStatuEnum> mapFXSubOrderResponseStatus = new HashMap<String, BusinessGetOrderResponseOrderItem_SubOrderStatuEnum>() {
        {
            put("LOCKED", BusinessGetOrderResponseOrderItem_SubOrderStatuEnum.JH_07);
            put("WAIT_FOR_PAY", BusinessGetOrderResponseOrderItem_SubOrderStatuEnum.JH_01);
            put("PAID_WAIT_FOR_CONFIRM", BusinessGetOrderResponseOrderItem_SubOrderStatuEnum.JH_01);
            put("PAID", BusinessGetOrderResponseOrderItem_SubOrderStatuEnum.JH_02);
            put("CONSIGNED", BusinessGetOrderResponseOrderItem_SubOrderStatuEnum.JH_03);
            put("ORDER_SUCCESS", BusinessGetOrderResponseOrderItem_SubOrderStatuEnum.JH_04);
            put("ORDER_CLOSE", BusinessGetOrderResponseOrderItem_SubOrderStatuEnum.JH_05);
        }
    };

    /**
     * 淘宝普通订单 - 订单类型集合
     * <a href="https://open.taobao.com/doc.htm?spm=a219a.7386797.0.0.b443669a5VuopO&source=search&docId=102855&docType=1">淘宝普通订单类型</a>
     */
    public static final Map<String, OrderOriginalTradeTypeEnum> NORMAL_ORDER_TYPE_MAP = new HashMap<String, OrderOriginalTradeTypeEnum>() {{
        put("eticket", OrderOriginalTradeTypeEnum.ETICKET);
        put("o2o_offlinetrade", OrderOriginalTradeTypeEnum.O2O_OFFLINETRADE);
    }};

    //endregion

    //region 实体转换

    /**
     * 将rds查出的数据类型 转为 标准数据类型结构
     *
     * @param dataOriginOrders 从数据库查出的数据订单集合
     * @param shopType         店铺类型
     * @param loadTime         订单下载时间
     * @param refundStatus     如果抓退款单,需要的退款单类别
     * @return RDS标准订单实体返回集合
     */
    public static List<IRDSResponse> convertToOrderList(List<RDSOrderEntity> dataOriginOrders, RDSShopTypesEnum shopType, LocalDateTime loadTime,
                                                        Business_RefundStatuEnum... refundStatus) {
        switch (shopType) {
            case NORMAL:
                return convertToOrderListForNormal(dataOriginOrders, loadTime);
            case FX:
                return convertToOrderListForFX(dataOriginOrders, loadTime);
            case JX:
                return convertToOrderListForJX(dataOriginOrders, loadTime);
            case REFUND:
                return convertToOrderListForRefund(dataOriginOrders, refundStatus);
            case FX_REFUND:
                return convertToOrderListForFxRefund(dataOriginOrders, refundStatus);
            default:
                AppException.throwException(ErrorCodes.LOGICERROR, "错误的店铺类型" + shopType);
        }
        return new ArrayList<>();
    }

    private final static String XIAOSHIDA_DELIVERY_FEE = "小时达配送费";

    //region RDS普通、分销、经销、时效订单集合实体转换

    //region RDS普通订单实体转换

    /**
     * 普通订单集合 将rds查出的数据类型 转为 标准数据类型结构
     *
     * @param dataOriginOrders 从数据库查出的数据订单集合
     * @param loadTime         订单下载时间
     * @return RDS标准订单实体返回集合
     */
    public static List<IRDSResponse> convertToOrderListForNormal(List<RDSOrderEntity> dataOriginOrders, LocalDateTime loadTime) {
        List<IRDSResponse> dataResponse = new ArrayList<>();
        //解析普通订单数据
        for (RDSOrderEntity item : dataOriginOrders) {
            //为空则跳过
            if (ExtUtils.isNullOrEmpty(item.getJsonOrderData())) {
                continue;
            }

            DomainUtils.doTryCatch("[淘宝RDS抓单]解析普通订单json", ApplicationTypeEnum.TASK, () -> {
                //转为TB对应的订单实体
                TBNormalOrderEntity tbNormalOrder = JsonUtils.deJson(item.getJsonOrderData(), TBNormalOrderEntity.class);
                BusinessGetOrderResponseOrderItem orderItem = convertToOrderForNormal(tbNormalOrder, loadTime, item);
                if (orderItem == null) {
                    return true;
                }
                orderItem.setHashVal(item.getHashCode());
                RDSNormalOrderResponse orderResponse = new RDSNormalOrderResponse(new ArrayList<BusinessGetOrderResponseOrderItem>() {{
                    add(orderItem);
                }}, item.getSellerNick(), item.getModifiedTime());

                dataResponse.add(orderResponse);
                return true;
            });
        }
        return dataResponse;
    }

    /**
     * 普通订单 将rds查出的数据类型 转为 标准数据类型结构
     *
     * @param entity         淘宝普通订单
     * @param loadTime       订单下载时间
     * @param rdsOrderEntity RDS抓单对应实体
     * @return RDS标准订单
     */
    public static BusinessGetOrderResponseOrderItem convertToOrderForNormal(TBNormalOrderEntity entity, LocalDateTime loadTime, RDSOrderEntity rdsOrderEntity) {
        if (entity == null || entity.getTrade_fullinfo_get_response() == null || entity.getTrade_fullinfo_get_response().getTrade() == null) {
            return null;
        }

        //取出淘宝的订单实体
        TBNormalOrderEntity.Trade tbOrder = entity.getTrade_fullinfo_get_response().getTrade();
        BusinessGetOrderResponseOrderItem standardOrder = new BusinessGetOrderResponseOrderItem();

        // 获取订单扩展信息
        Map<String, Object> tradeAttr = JsonUtils.deJson(tbOrder.getTrade_attr(), new TypeReference<Map<String, Object>>() {
        });

        //region 订单主体
        //格式 2017-05-14 09:40:36
        standardOrder.setModifyTime(
                ExtUtils.isNullOrEmpty(tbOrder.getModified()) ? DateTimeUtils.stringToTime(tbOrder.getCreated()) : DateTimeUtils.stringToTime(tbOrder.getModified()));
        // 赋值推送库修改时间
        standardOrder.setIncrementModifyTime(rdsOrderEntity.getModifiedTime());
        // 赋值推送库订单创建时间
        standardOrder.setIncrementCreateTime(rdsOrderEntity.getCreateTime());
        //下载时间
        standardOrder.setLoadTime(loadTime);
        //敏感信息加密
        standardOrder.setIsEncrypt(true);
        standardOrder.setIsPreSaleOrder("step".equalsIgnoreCase(tbOrder.getType()));
        // 预售。
        if (ExtUtils.isNullOrEmpty(tbOrder.getStep_trade_status())) {
            standardOrder.setPreSaleStatus("");
        } else {
            // 淘宝文档: https://open.taobao.com/help?spm=a219a.7386797.0.0.2d23669aLoMrpQ&source=search&docId=2494&docType=14
            standardOrder.setPreSaleStatus(tbOrder.getStep_trade_status());
            // 分阶段付款的已付金额。
            final BigDecimal stepPaidFee = ExtUtils.toDecimalNotNull(tbOrder.getStep_paid_fee());
            switch (tbOrder.getStep_trade_status()) {
                // 定金已付尾款未付
                case "FRONT_PAID_FINAL_NOPAID":
                    standardOrder.setFirstPayment(stepPaidFee);
                    break;
                // 定金和尾款都付
                case "FRONT_PAID_FINAL_PAID":
                    // 定金和尾款都付，则step_paid_fee为尾款金额。
                    standardOrder.setFirstPayment(ExtUtils.toDecimalNotNull(tbOrder.getPayment()).subtract(stepPaidFee));
                    break;
                default:
                    // 定金未付尾款未付(FRONT_NOPAID_FINAL_NOPAID)
                    standardOrder.setFirstPayment(BigDecimal.ZERO);
                    break;
            }
        }


        // region 淘宝平台标记处理

        // 根据淘宝平台优惠信息构建平台标记列表(递交给OMS)
        List<BusinessGetOrderResponseOrderTag> orderTags = RDSTBOrderUtils.promotionDetailsFlagConvert(tbOrder);

        // 淘宝春节不打烊商品标记处理
        BusinessGetOrderResponseOrderTag springFestivalNotClose = RDSTBOrderUtils.springFestivalNotClose(tbOrder.getOrders().getOrder());

        // 物流服务标记
        List<BusinessGetOrderResponseOrderTag> logisticTags = RDSTBOrderUtils.dealLogisticAgreement(tbOrder);

        if (springFestivalNotClose != null) {
            standardOrder.addOrderTag(springFestivalNotClose);
        }

        if (CollectionUtils.isNotEmpty(logisticTags)) {
            standardOrder.addOrderTag(logisticTags);
        }

        if (CollectionUtils.isNotEmpty(orderTags)) {
            standardOrder.addOrderTag(orderTags);
        }

        // 需机构鉴定标记(任一商品级jewcc_no字段不为空) 递交给OMS
        if (RDSTBOrderUtils.isNeedIdentify(tbOrder)) {
            BusinessGetOrderResponseOrderTag orderTag = new BusinessGetOrderResponseOrderTag();
            orderTag.setKey(ApiPlatFlagEnum.NEED_IDENTIFY.getCode());
            orderTag.setDesc(ApiPlatFlagEnum.NEED_IDENTIFY.getCaption());
            standardOrder.addOrderTag(orderTag);
        }

        // 淘宝小时达2.0平台标记处理
        if (isXsdOrder(tradeAttr)) {
            BusinessGetOrderResponseOrderTag orderTag = new BusinessGetOrderResponseOrderTag();
            orderTag.setKey(ApiPlatFlagEnum.TB_XSD_VERSION2.getCode());
            orderTag.setDesc(ApiPlatFlagEnum.TB_XSD_VERSION2.getCaption());
            standardOrder.addOrderTag(orderTag);
        }

        // 淘宝政府补贴订单处理
        if (isZhenFuBuTie(tbOrder)) {
            BusinessGetOrderResponseOrderTag orderTag = new BusinessGetOrderResponseOrderTag();
            orderTag.setKey(ApiPlatFlagEnum.JH_ZHENGFUBUTIE.getCode());
            orderTag.setDesc(ApiPlatFlagEnum.JH_ZHENGFUBUTIE.getCaption());
            standardOrder.addOrderTag(orderTag);
        }

        // endregion

        standardOrder.setPlatOrderNo(tbOrder.getTid() + "");
        standardOrder.setTradeStatusDescription(tbOrder.getStatus());
        standardOrder.setTradeTime(DateTimeUtils.stringToTime(tbOrder.getCreated()));
        //DecryptData( "nick", order.buyer_nick, sessionKey),
        standardOrder.setNick(tbOrder.getBuyer_nick());
        //DecryptData( "receiver_name", order.receiver_name, sessionKey),
        standardOrder.setReceiverName(tbOrder.getReceiver_name());
        standardOrder.setPayOrderNo(tbOrder.getAlipay_no());
        standardOrder.setBankAccount(tbOrder.getBuyer_alipay_no());
        //RDS数据中目前未发现字段receiver_country
        standardOrder.setCountry(tbOrder.getReceiver_country() == null ? "" : tbOrder.getReceiver_country());
        standardOrder.setProvince(tbOrder.getReceiver_state());
        standardOrder.setCity(tbOrder.getReceiver_city());
        standardOrder.setArea(tbOrder.getReceiver_district());
        standardOrder.setTown(tbOrder.getReceiver_town());
        //去除前面拼接的town
        standardOrder.setAddress(removeTownFromAddress(tbOrder.getReceiver_address(), tbOrder.getReceiver_town()));
        standardOrder.setZip(tbOrder.getReceiver_zip());
        standardOrder.setPhone(tbOrder.getReceiver_phone());
        standardOrder.setMobile(tbOrder.getReceiver_mobile());
        standardOrder.setEmail(tbOrder.getBuyer_email());
        standardOrder.setCustomerRemark(tbOrder.getBuyer_message());
        standardOrder.setSellerRemark(tbOrder.getSeller_memo());
        standardOrder.setOaid(tbOrder.getOaid());
        //小额收款订单信息
        standardOrder.setReceiptType(tbOrder.getReceipt_type());
        if (tbOrder.getReceipt_rel_ids() != null && CollectionUtils.isNotEmpty(tbOrder.getReceipt_rel_ids().getReceiptRelIds())) {
            standardOrder.setReceiptOrders(StringUtils.join(tbOrder.getReceipt_rel_ids().getReceiptRelIds(), ','));
        }
        String tbPostFee;
        if (Boolean.TRUE.equals(isXsdOrder(tradeAttr))) {
            //小时达业务邮费和商品费用处理
            tbPostFee = getXiaoShiDaTotalFee(tbOrder);
        } else {
            tbPostFee = tbOrder.getPost_fee();
        }
        // 门店id
        if (MapUtils.isNotEmpty(tradeAttr)) {
            standardOrder.setShopId(ExtUtils.getNonEmpty(tradeAttr.get("storeId"), OmsApiConstant.EMPTY_STR).toString());
        }

        // 发货时间
        standardOrder.setActualDeliveryTime(DateTimeUtils.stringToTime(tbOrder.getConsign_time()));
        //费用：邮费
        standardOrder.setPostFee(ExtUtils.toDecimal(tbPostFee));
        //费用：商品费用
        standardOrder.setGoodsFee(ExtUtils.toDecimalNotNull(tbOrder.getPayment()).add(ExtUtils.toDecimalNotNull(tbOrder.getDiscount_fee()))
                .subtract(ExtUtils.toDecimalNotNull(tbPostFee)));
        //费用：总费用
        standardOrder.setTotalAmount(ExtUtils.toDecimal(tbOrder.getPayment()));
        //费用: 实际支付金额
        standardOrder.setRealPayMoney(ExtUtils.toDecimal(tbOrder.getPayment()));
        //费用: 优惠金额
        standardOrder.setFavourableMoney(ExtUtils.toDecimal(tbOrder.getDiscount_fee()));
        // 费用：权益金
        standardOrder.setExpandUsedAmount(tbOrder.getExpand_card_expand_price_used());

        //费用: 佣金
        standardOrder.setCommissionValue(ExtUtils.toDecimal(tbOrder.getCommission_fee()));
        standardOrder.setSendStyle(getSendStyle(tbOrder.getTrade_attr(), tbOrder.getShipping_type()));
        standardOrder.setSendType(mapPolyShippingType.getOrDefault(tbOrder.getShipping_type(), BusinessGetOrderResponseOrderItem_SendTypeEnum.JH_OTHER));
        //货到付款判断
        standardOrder.setPayType(getPayType(tbOrder));
        standardOrder.setQq("");
        standardOrder.setPayTime(DateTimeUtils.stringToTime(tbOrder.getPay_time()));
        standardOrder.setCodServiceFee(ExtUtils.toDecimal(tbOrder.getSeller_cod_fee()));
        standardOrder.setSellerFlag(mapSellerFlag.getOrDefault(tbOrder.getSeller_flag(), BusinessGetOrderResponseOrderItem_SellerFlagEnum.JH_NONE));
        standardOrder.setShopType(Business_ShopTypeEnum.JH_001);

        // 设置订单交易类型
        standardOrder.setTradeType(tbOrder.getType());

        //发票类型（ 1 电子发票 2 纸质发票 ）
        standardOrder.setInvoiceType(tbOrder.getInvoice_kind());
        standardOrder.setIsNeedInvoice(!ExtUtils.isNullOrEmpty(tbOrder.getInvoice_kind()));
        //RDS数据中目前未发现字段invoice_name
        standardOrder.setInvoiceTitle(tbOrder.getInvoice_name());
        // 总税费 = 税费 + 关税费
        standardOrder.setTaxAmount(ExtUtils.toDecimalNotNull(tbOrder.getOrder_tax_fee()).add(ExtUtils.toDecimalNotNull(tbOrder.getM_tariff_fee())));
        standardOrder.setIsBrandSale(tbOrder.isIs_brand_sale());
        standardOrder.setIsForceWlb(tbOrder.isIs_force_wlb());
        standardOrder.setTaxPayerIdent(getTaxNO(tradeAttr));
        standardOrder.setIsDaiXiao(tbOrder.isIs_daixiao());
        standardOrder.setCompletedTime(DateTimeUtils.stringToTime(tbOrder.getEnd_time()));
        standardOrder.setRxAuditStatus(tbOrder.getRx_audit_status());
        standardOrder.setObFlag(tbOrder.getOb_tag());
        standardOrder.setDrugRegister(tbOrder.getDrug_register());
        standardOrder.setBuyerOpenUid(tbOrder.getBuyer_open_uid());
        //发货截止时间
        try {
            standardOrder.setLeftSendDate(dealLastSendDate(tbOrder));
        } catch (Exception e) {
            // 解析失败赋值null
            standardOrder.setLeftSendDate(null);
            BusinessLogUtils.tryCatchWrite(DomainUtils.getContextID(), e.getMessage());
        }
        if (OmsApiConstant.ASDP_BIZ_TYPE.equals(tbOrder.getAsdp_biz_type())) {
            standardOrder.setOrderType(tbOrder.getAsdp_biz_type());
        }
        standardOrder.setAsdpAds(tbOrder.getAsdp_ads());

        // region 是否是菜鸟仓订单
        int length = 0;
        if (tbOrder.getOrders() != null && tbOrder.getOrders().getOrder() != null) {
            length = tbOrder.getOrders().getOrder().length;
        }

        // 商品级获取
        standardOrder.setIsShip(length != 0 && ExtUtils.toBoolean(tbOrder.getOrders().getOrder()[length - 1].getIs_sh_ship()));
        if (!standardOrder.isShip()) {
            // 订单级获取(商品级可能获取不到)
            standardOrder.setIsShip(tbOrder.getIs_sh_ship());
            if (!standardOrder.isShip()) {
                standardOrder.setIsShip(length != 0 && OmsApiConstant.CN_SHIPPER.equals(tbOrder.getOrders().getOrder()[length - 1].getShipper()));
            }
        }

        // endregion

        // 【小时达】订单
        if (!ExtUtils.isNullOrEmpty(tbOrder.getTcps_code()) && tbOrder.getTcps_code().contains("storeJsd-self-3-ap")) {
            standardOrder.setIsHourlyOrder(YesOrNo.YES.getCode());
        }

        standardOrder.setTcpsCode(tbOrder.getTcps_code());
        standardOrder.setIsHwgFlag(tbOrder.isCrossBondedDeclare());
        if (!standardOrder.isHwgFlag()) {
            standardOrder.setIsHwgFlag(length != 0 && convertIsHWG(tbOrder.getOrders().getOrder()[length - 1].getOrder_attr()));
        }
        // 邮关订单
        boolean isPostGateDeclare = !Boolean.FALSE.equals(tbOrder.getPostGateDeclare());
        standardOrder.setPostGateDeclare(isPostGateDeclare);

        //淘宝订单其他属性
        standardOrder.setTradeAttrJson(tbOrder.getTrade_attr());
        standardOrder.setTradeStatus(mapResponseStatus.getOrDefault(tbOrder.getStatus(), Business_OrderStatusEnum.JH_98));
        //订单商品信息
        standardOrder.setGoodInfos(getAllGoods(tbOrder));
        standardOrder.setSellerNick(tbOrder.getSeller_nick());
        //淘宝送礼订单处理
        dealGiftOrder(tbOrder, standardOrder);
        // 平台优惠和明细处理
        dealPlatDiscountMoneyAndCouponDetail(tbOrder, standardOrder);
        //处理平台原始金额字段
        dealPlatOrderOriginalField(tbOrder, standardOrder);
        //家装订单处理
        jzTypeGoodsHandle(tbOrder, standardOrder);
        //BMS审单分仓结果
        standardOrder.setLogisticsInfos(getBMSLogisticsInfo(tbOrder));
        //时效订单处理
        dealTimingOrder(tbOrder, standardOrder);
        //同城购订单处理
        dealO2oOrder(tbOrder, standardOrder);
        //平台物流信息赋值
        dealLogisticInfo(tbOrder, standardOrder);
        // 平台物流供应链信息赋值
        dealLogisticChainInfo(tbOrder, standardOrder);
        // logistics_agreement节点解析
        dealLogisticAgreement(tbOrder, standardOrder);
        //前N有礼
        dealFirstNHaveAGift(tbOrder.getOrders(), standardOrder);
        //区域零售
        dealRegionRetail(tbOrder, standardOrder);
        //天猫周期购
        dealCycleOrder(tbOrder, standardOrder);
        // 国补发票
        standardOrder.addPlatOrderOriginalField((RDSTBOrderUtils.convertOrderGovSubsidy(tbOrder)));
        //百补半托管订单处理
        if (isBaiBuBanTuoGuan(tbOrder)) {
            dealBaiBuBanTuoGuan(tbOrder, standardOrder);
        }
        //清仓订单处理
        dealClearanceSaleOrder(tbOrder, standardOrder);
        //平台订单类型处理
        dealTradeType(tbOrder, standardOrder);
        //endregion
        return standardOrder;
    }

    /**
     * 平台订单类型处理
     *
     * @param tbOrder
     * @param standardOrder
     */
    private static void dealTradeType(TBNormalOrderEntity.Trade tbOrder, BusinessGetOrderResponseOrderItem standardOrder) {
        if (StringUtil.isEmpty(tbOrder.getType())) {
            return;
        }
        //增加平台订单类型平台标记
        if (tbOrder.getType().equals(ApiPlatFlagEnum.ETICKET.getCode())) {
            standardOrder.addOrderTagByEnum(ApiPlatFlagEnum.ETICKET);
        } else if (tbOrder.getType().equals(ApiPlatFlagEnum.O2O_OFFLINETRADE.getCode())) {
            standardOrder.addOrderTagByEnum(ApiPlatFlagEnum.O2O_OFFLINETRADE);
        }
    }

    /**
     * 清仓订单处理
     *
     * @param tbOrder
     * @param standardOrder
     */
    private static void dealClearanceSaleOrder(TBNormalOrderEntity.Trade tbOrder, BusinessGetOrderResponseOrderItem standardOrder) {
        if (StringUtil.isEmpty(tbOrder.getQnDistr())) {
            return;
        }
        //增加清仓订单平台标记
        BusinessGetOrderResponseOrderTag orderTag = new BusinessGetOrderResponseOrderTag();
        orderTag.setKey(ApiPlatFlagEnum.JH_QINGCANG.getCode());
        orderTag.setDesc(ApiPlatFlagEnum.JH_QINGCANG.getCaption());
        standardOrder.addOrderTag(orderTag);
    }

    /**
     * 处理平台原始金额字段
     *
     * @param tbOrder
     * @param standardOrder
     */
    private static void dealPlatOrderOriginalField(TBNormalOrderEntity.Trade tbOrder, BusinessGetOrderResponseOrderItem standardOrder) {

        Map<String, BigDecimal> platOrderOriginalFieldMap = new HashMap<>();

        // 应收合计添加到平台原始字段
        addToMap(platOrderOriginalFieldMap, SaveOrderConstant.PLAT_PAYMENT_KEY, tbOrder.getPayment(), String.valueOf(tbOrder.getTid()));

        //如果有tmser_spu_code标签，就拼接起来，放到平台原始字段里面
        if (tbOrder.getService_orders() != null && tbOrder.getService_orders().getService_order() != null) {

            //相同tmser_spu_code的数据需要累加payment
            for (TBNormalOrderEntity.Service_Order serviceOrder : tbOrder.getService_orders().getService_order()) {
                if (serviceOrder == null) {
                    continue;
                }
                if (StringUtil.isNotEmpty(serviceOrder.getTmser_spu_code())) {
                    String key = String.format(OmsApiConstant.TB_TMSER_SPU_CODE + "_%s", serviceOrder.getTmser_spu_code());
                    String value = ExtUtils.getNonEmpty(serviceOrder.getPayment(), OmsApiConstant.ZERO_STR);
                    addToMap(platOrderOriginalFieldMap, key, value, standardOrder.getPlatOrderNo());
                }
            }
        }

        if (MapUtils.isNotEmpty(platOrderOriginalFieldMap)) {
            for (Map.Entry<String, BigDecimal> entry : platOrderOriginalFieldMap.entrySet()) {
                standardOrder.addPlatOrderOriginalField(new OriginalField(entry.getKey(), entry.getValue().toString()));
            }
        }
    }

    /**
     * @param map
     * @param key
     * @param value
     */
    public static void addToMap(Map<String, BigDecimal> map, String key, String value, String orderNo) {
        try {
            // 尝试将字符串value转换为BigDecimal
            BigDecimal decimalValue = new BigDecimal(value);

            // 如果map中已经存在该key，则累加value
            if (map.containsKey(key)) {
                BigDecimal existingValue = map.get(key);
                map.put(key, existingValue.add(decimalValue));
            } else {
                // 如果map中不存在该key，则直接放入
                map.put(key, decimalValue);
            }
        } catch (NumberFormatException e) {
            // 转换失败，原始数据有问题，不是金额类型,记录日志
            LogAdapter.writeSystemLog("淘宝RDS平台原始字段转换错误", () -> ExtUtils.stringBuilderAppend("淘宝RDS平台原始字段转换失败,orderNo:", orderNo), LogTypeEnum.ERROR);
        }
    }


    /**
     * 处理百补半托管订单
     *
     * @param tbOrder
     * @param standardOrder
     */
    private static void dealBaiBuBanTuoGuan(TBNormalOrderEntity.Trade tbOrder, BusinessGetOrderResponseOrderItem standardOrder) {
        //商品级ypds_order_supply_price 累加
        BigDecimal totalAmount = Arrays.stream(tbOrder.getOrders().getOrder())
                .map(goodsInfo -> ExtUtils.toDecimalNotNull(goodsInfo.getYpdsOrderSupplyPrice()))
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //订单级处理
        standardOrder.setTotalAmount(totalAmount);
        standardOrder.setRealPayMoney(totalAmount);
        //增加百补半托管标记
        BusinessGetOrderResponseOrderTag orderTag = new BusinessGetOrderResponseOrderTag();
        orderTag.setKey(ApiPlatFlagEnum.JH_BAI_BU_BAN_TUO_GUAN.getCode());
        orderTag.setDesc(ApiPlatFlagEnum.JH_BAI_BU_BAN_TUO_GUAN.getCaption());
        standardOrder.addOrderTag(orderTag);
    }


    /**
     * 判断是否为百补半托管订单
     *
     * @param tbOrder
     * @return
     */
    private static boolean isBaiBuBanTuoGuan(TBNormalOrderEntity.Trade tbOrder) {
        // 商品级判断(ypds_order_type = 1) 有一个ypds_order_type = 1就当做百补半托管订单
        return Arrays.stream(tbOrder.getOrders().getOrder()).anyMatch((t -> TBYpdsOrderTypeEnum.Billion_Subsidy_Ban_Tuo_Guan.toString().equals(t.getYpdsOrderType())));
    }


    /**
     * 淘宝送礼订单处理
     *
     * @param tbOrder
     * @param standardOrder
     */
    private static void dealGiftOrder(TBNormalOrderEntity.Trade tbOrder, BusinessGetOrderResponseOrderItem standardOrder) {
        //处理邮费退差金额
        if (StringUtil.isNotEmpty(tbOrder.getRefundPostFee())) {
            BigDecimal divideValue = BigDecimal.valueOf(100);
            BigDecimal refundPostFee = ExtUtils.toDecimal(tbOrder.getRefundPostFee()).divide(divideValue);
            standardOrder.setTotalAmount(ExtUtils.toDecimalNotNull(standardOrder.getTotalAmount()).subtract(refundPostFee));
            standardOrder.setRealPayMoney(standardOrder.getTotalAmount());
            standardOrder.setPostFee(ExtUtils.toDecimalNotNull(standardOrder.getPostFee().subtract(refundPostFee)));
            standardOrder.setRefundPostFee(refundPostFee);
        }
        if (StringUtils.isBlank(tbOrder.getRealReceiverOpenId())) {
            return;
        }
        //送礼物(平台特殊单标记)
        BusinessGetOrderResponseOrderTag orderTag = new BusinessGetOrderResponseOrderTag();
        orderTag.setKey(ApiPlatFlagEnum.JH_PRESENT_TAOBAO.getCode());
        orderTag.setDesc(ApiPlatFlagEnum.JH_PRESENT_TAOBAO.getCaption());
        standardOrder.addOrderTag(orderTag);
        standardOrder.setRealReceiverOpenId(tbOrder.getRealReceiverOpenId());
        standardOrder.setRealReceiverDisplayNick(tbOrder.getRealReceiverDisplayNick());
        //根据返回添加支付邮费标记
        if (TBPostFeeTypeEnum.GIFT_PRE_PAID.toString().equals(tbOrder.getPostFeeType()) && StringUtils.isNotBlank(tbOrder.getGiftPostFeeRole())) {
            BusinessGetOrderResponseOrderTag tag = new BusinessGetOrderResponseOrderTag();
            if (TBGiftPostFeeRoleEnum.JH_GIFTS_GIVER_PAY_POSTAGE.toString().equals(tbOrder.getGiftPostFeeRole())) {
                tag.setKey(ApiPlatFlagEnum.JH_GIFTS_GIVER_PAY_POSTAGE.getCode());
                tag.setDesc(ApiPlatFlagEnum.JH_GIFTS_GIVER_PAY_POSTAGE.getCaption());
            } else if (TBGiftPostFeeRoleEnum.JH_GIFTS_ACCEPTER_PAY_POSTAGE.toString().equals(tbOrder.getGiftPostFeeRole())) {
                tag.setKey(ApiPlatFlagEnum.JH_GIFTS_ACCEPTER_PAY_POSTAGE.getCode());
                tag.setDesc(ApiPlatFlagEnum.JH_GIFTS_ACCEPTER_PAY_POSTAGE.getCaption());
            } else {
                return;
            }
            standardOrder.addOrderTag(tag);
        }
    }

    /**
     * 春节不打烊标记判断
     *
     * @param orders 订单商品列表
     * @return 春节不打烊标记
     */
    private static BusinessGetOrderResponseOrderTag springFestivalNotClose(TBNormalOrderEntity.Order[] orders) {
        // 任一商品title包含 ”春节不打烊“ 则为春节不打烊商品
        boolean notClose = Arrays.stream(orders).anyMatch(order -> StringUtils.isNotBlank(order.getTitle()) && order.getTitle().contains("【春节不打烊】"));

        if (notClose) {

            BusinessGetOrderResponseOrderTag orderTag = new BusinessGetOrderResponseOrderTag();
            // 和 运维后台 - 字典数据 code保持一致
            orderTag.setKey("JH_BUDAYANG");

            return orderTag;
        }
        return null;
    }

    /**
     * 处理物流服务标记
     *
     * @param tbOrder 订单信息
     */
    private static List<BusinessGetOrderResponseOrderTag> dealLogisticAgreement(TBNormalOrderEntity.Trade tbOrder) {
        List<BusinessGetOrderResponseOrderTag> orderTags = new ArrayList<>();
        String logisticsServiceMsg = Optional.ofNullable(tbOrder.getLogistics_agreement())
                .map(TBNormalOrderEntity.LogisticsAgreement::getLogistics_service_msg).orElse(null);
        if (StringUtils.isNotBlank(logisticsServiceMsg)) {
            // 兼容中英文逗号
            String regex = "[,，]";
            String[] messages = logisticsServiceMsg.split(regex);
            for (String message : messages) {
                BusinessGetOrderResponseOrderTag orderTag = new BusinessGetOrderResponseOrderTag();
                orderTag.setKey(message);
                orderTags.add(orderTag);
            }
        }
        return orderTags;
    }

    //region 获取所有的优惠金额

    /**
     * 获取所有的优惠金额
     *
     * @param tbOrder 订单实体
     * @return 所有的优惠金额
     */
    private static void dealPlatDiscountMoneyAndCouponDetail(TBNormalOrderEntity.Trade tbOrder, BusinessGetOrderResponseOrderItem standardOrder) {
        List<OriginalField> platOrderOriginalField = standardOrder.getPlatOrderOriginalField() == null ? new ArrayList<>() : standardOrder.getPlatOrderOriginalField();
        BigDecimal divideValue = BigDecimal.valueOf(100);
        BigDecimal alipayPoint = ExtUtils.toDecimalNotNull(tbOrder.getAlipay_point()).divide(divideValue, 2, RoundingMode.HALF_UP);
        BigDecimal platformSubsidyFee = ExtUtils.toDecimalNotNull(tbOrder.getPlatform_subsidy_fee());
        BigDecimal couponFee = ExtUtils.toDecimalNotNull(tbOrder.getCoupon_fee()).divide(divideValue, 2, RoundingMode.HALF_UP);
        BigDecimal tMallCouponFee = ExtUtils.toDecimalNotNull(tbOrder.getTmall_coupon_fee()).divide(divideValue, 2, RoundingMode.HALF_UP);
        //淘金币补贴 所有优惠里都要加上
        BigDecimal propointFee = ExtUtils.toDecimalNotNull(tbOrder.getPropoint()).divide(divideValue, 2, RoundingMode.HALF_UP);


        //订单优惠信息
        List<BusinessGetOrderResponseOrderItemCouponDetailInfo> couponDetails = new ArrayList<>();
        standardOrder.setCouponDetails(couponDetails);

        // 平台原始优惠详情信息
        List<BusinessGetOrderResponseOrderItemCouponDetailInfo> platOriginalCouponDetails = dealPromotionDetails(tbOrder);
        platOriginalCouponDetails.addAll(dealPlatCouponDetails(tbOrder));
        standardOrder.setPlatOriginalCouponDetails(platOriginalCouponDetails);

        //淘金币平台补贴
        if (BigDecimal.ZERO.compareTo(propointFee) < 0) {
            BusinessGetOrderResponseOrderItemCouponDetailInfo propointInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();
            propointInfo.setPrice(propointFee.toString());
            platOrderOriginalField.add(new OriginalField("propoint", propointFee.toString()));
            propointInfo.setType("淘金币平台补贴");
            propointInfo.setCouponSource(BusinessGetOrderResponseOrderItem_OrderCouponSourceEnum.JH_PLAT.toString());
            propointInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_PROPOINT);
            propointInfo.setCouponNum(1);
            couponDetails.add(propointInfo);
        }
        //天猫集分宝：不为null且大于0，则除100添加优惠
        if (tbOrder.getAlipay_point() != null && BigDecimal.valueOf(0).compareTo(tbOrder.getAlipay_point()) < 0) {
            BusinessGetOrderResponseOrderItemCouponDetailInfo couponDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();
            couponDetailInfo.setPrice(alipayPoint.toString());
            platOrderOriginalField.add(new OriginalField("alipay_point", alipayPoint.toString()));
            couponDetailInfo.setType("天猫集分宝");
            couponDetailInfo.setCouponSource(BusinessGetOrderResponseOrderItem_OrderCouponSourceEnum.JH_PLAT.toString());
            couponDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_TMJFB);
            couponDetailInfo.setCouponNum(1);
            couponDetails.add(couponDetailInfo);
        }
        // 没有权益金且tMallCouponFee有值时
        if (BigDecimal.ZERO.compareTo(tMallCouponFee) < 0 && BigDecimal.ZERO.compareTo(ExtUtils.toDecimalNotNull(tbOrder.getExpand_card_expand_price_used())) >= 0) {

            BigDecimal platDiscountMoney = alipayPoint.add(tMallCouponFee).add(propointFee);
            // 天猫红包金额处理
            BusinessGetOrderResponseOrderItemCouponDetailInfo tMallCouponFeeDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();
            tMallCouponFeeDetailInfo.setPrice(tMallCouponFee.toString());
            platOrderOriginalField.add(new OriginalField("tmall_coupon_fee", tMallCouponFee.toString()));
            tMallCouponFeeDetailInfo.setType("天猫红包");
            tMallCouponFeeDetailInfo.setCouponSource(BusinessGetOrderResponseOrderItem_OrderCouponSourceEnum.JH_PLAT.toString());
            tMallCouponFeeDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_TMallRedCoupon);
            tMallCouponFeeDetailInfo.setCouponNum(1);
            couponDetails.add(tMallCouponFeeDetailInfo);

            // 开启配置强制累加的会员
            if (YunConfigUtils.contains(tbOrder.getSeller_nick(), ConfigKeyEnum.TAOBAO_AMOUNT_COUPON_DETAILS_SUM)
                    && platformSubsidyFee.compareTo(BigDecimal.ZERO) > 0) {
                BusinessGetOrderResponseOrderItemCouponDetailInfo platformSubsidyFeeDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();
                platformSubsidyFeeDetailInfo.setPrice(platformSubsidyFee.toString());
                platOrderOriginalField.add(new OriginalField("platform_subsidy_fee", platformSubsidyFee.toString()));
                platformSubsidyFeeDetailInfo.setType("平台购物券");
                platformSubsidyFeeDetailInfo.setCouponSource(BusinessGetOrderResponseOrderItem_OrderCouponSourceEnum.JH_PLAT.toString());
                platformSubsidyFeeDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_PLATCOUPON);
                platformSubsidyFeeDetailInfo.setCouponNum(1);
                couponDetails.add(platformSubsidyFeeDetailInfo);
                platDiscountMoney = platDiscountMoney.add(platformSubsidyFee);
                standardOrder.setPlatDiscountMoney(platDiscountMoney);
                //将优惠信息赋值菠萝派平台原始字段
                standardOrder.setPlatOrderOriginalField(platOrderOriginalField);
                return;
            }

            // platformSubsidyFee大于tMallCouponFee需要再加上platformSubsidyFee
            if (platformSubsidyFee.compareTo(tMallCouponFee) > 0) {
                //平台购物券
                BusinessGetOrderResponseOrderItemCouponDetailInfo platformSubsidyFeeDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();
                platformSubsidyFeeDetailInfo.setPrice(platformSubsidyFee.toString());
                platOrderOriginalField.add(new OriginalField("platform_subsidy_fee", platformSubsidyFee.toString()));
                platformSubsidyFeeDetailInfo.setType("平台购物券");
                platformSubsidyFeeDetailInfo.setCouponSource(BusinessGetOrderResponseOrderItem_OrderCouponSourceEnum.JH_PLAT.toString());
                platformSubsidyFeeDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_PLATCOUPON);
                platformSubsidyFeeDetailInfo.setCouponNum(1);
                couponDetails.add(platformSubsidyFeeDetailInfo);
                platDiscountMoney = platDiscountMoney.add(platformSubsidyFee);
                standardOrder.setPlatDiscountMoney(platDiscountMoney);
                //将优惠信息赋值菠萝派平台原始字段
                standardOrder.setPlatOrderOriginalField(platOrderOriginalField);
                return;
            }

            standardOrder.setPlatDiscountMoney(platDiscountMoney);
            //将优惠信息赋值菠萝派平台原始字段
            standardOrder.setPlatOrderOriginalField(platOrderOriginalField);
            return;
        }

        //红包
        if (tbOrder.getCoupon_fee() != null && BigDecimal.ZERO.compareTo(tbOrder.getCoupon_fee()) < 0) {
            BusinessGetOrderResponseOrderItemCouponDetailInfo couponDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();
            couponDetailInfo.setPrice(couponFee.toString());
            couponDetailInfo.setType("红包");
            platOrderOriginalField.add(new OriginalField("coupon_fee", couponFee.toString()));
            couponDetailInfo.setCouponSource(BusinessGetOrderResponseOrderItem_OrderCouponSourceEnum.JH_PLAT.toString());
            couponDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_REDCOUPON);
            couponDetailInfo.setCouponNum(1);
            couponDetails.add(couponDetailInfo);
        }
        //平台购物券
        if (tbOrder.getPlatform_subsidy_fee() != null && BigDecimal.ZERO.compareTo(tbOrder.getPlatform_subsidy_fee()) < 0) {
            BusinessGetOrderResponseOrderItemCouponDetailInfo platformSubsidyFeeDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();
            platformSubsidyFeeDetailInfo.setPrice(platformSubsidyFee.toString());
            platOrderOriginalField.add(new OriginalField("platform_subsidy_fee", platformSubsidyFee.toString()));
            platformSubsidyFeeDetailInfo.setType("平台购物券");
            platformSubsidyFeeDetailInfo.setCouponSource(BusinessGetOrderResponseOrderItem_OrderCouponSourceEnum.JH_PLAT.toString());
            platformSubsidyFeeDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_PLATCOUPON);
            platformSubsidyFeeDetailInfo.setCouponNum(1);
            couponDetails.add(platformSubsidyFeeDetailInfo);
        }

        // 其次取 couponFee + platformSubsidyFee
        standardOrder.setPlatDiscountMoney(alipayPoint.add(couponFee.add(platformSubsidyFee).add(propointFee)));

        couponDetails.addAll(dealCouponDetail(tbOrder));

        //将优惠信息赋值菠萝派平台原始字段
        standardOrder.setPlatOrderOriginalField(platOrderOriginalField);
    }

    /**
     * 获取平台优惠外的优惠明细
     *
     * @param tbOrder 订单实体
     * @return 所有的优惠金额
     */
    private static List<BusinessGetOrderResponseOrderItemCouponDetailInfo> dealCouponDetail(TBNormalOrderEntity.Trade tbOrder) {

        // 淘宝促销信息处理
        List<BusinessGetOrderResponseOrderItemCouponDetailInfo> couponDetails = new ArrayList<>(dealPromotionDetails(tbOrder));

        //天猫积分、天猫购物券优惠：不为null且大于0，则除100添加优惠
        if (tbOrder.getPoint_fee() != null && BigDecimal.valueOf(0).compareTo(tbOrder.getPoint_fee()) < 0) {
            BusinessGetOrderResponseOrderItemCouponDetailInfo couponDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();
            couponDetailInfo.setPrice(tbOrder.getPoint_fee().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).toString());
            couponDetailInfo.setType("天猫积分、天猫购物券");
            couponDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_POINT);
            couponDetailInfo.setCouponNum(1);
            couponDetails.add(couponDetailInfo);
        }

        //tbOrder 的 卖家手工调整金额adjustfee 不为null且大于0
        if (tbOrder.getAdjust_fee() != null && BigDecimal.valueOf(0).compareTo(ExtUtils.toDecimalNotNull(tbOrder.getAdjust_fee())) != 0) {
            BusinessGetOrderResponseOrderItemCouponDetailInfo couponDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();
            couponDetailInfo.setPrice(tbOrder.getAdjust_fee());
            couponDetailInfo.setType("卖家手工调整金额");
            couponDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_PEOPLECOUPON);
            couponDetails.add(couponDetailInfo);
        } else {

            //tbOrder 下子订单 的 卖家手工调整金额adjustfee 不为null且大于0
            if (tbOrder.getOrders() != null && tbOrder.getOrders().getOrder() != null && tbOrder.getOrders().getOrder().length > 0) {
                for (TBNormalOrderEntity.Order item : tbOrder.getOrders().getOrder()) {
                    //值大于0
                    if (item.getAdjust_fee() != null && BigDecimal.valueOf(0).compareTo(ExtUtils.toDecimalNotNull(item.getAdjust_fee())) != 0) {
                        BusinessGetOrderResponseOrderItemCouponDetailInfo couponDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();
                        couponDetailInfo.setPrice(item.getAdjust_fee());
                        couponDetailInfo.setType("卖家手工调整金额");
                        couponDetailInfo.setSku_id(item.getNum_iid() + "");
                        couponDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_PEOPLECOUPON);
                        couponDetails.add(couponDetailInfo);
                    }
                }
            }
        }
        return couponDetails;
    }

    /**
     * 促销优惠详情信息处理
     *
     * @param tbOrder 淘宝订单原始信息
     * @return 优惠详情
     */
    private static List<BusinessGetOrderResponseOrderItemCouponDetailInfo> dealPromotionDetails(TBNormalOrderEntity.Trade tbOrder) {

        List<BusinessGetOrderResponseOrderItemCouponDetailInfo> couponDetails = new ArrayList<>();

        if (tbOrder.getPromotion_details() == null || tbOrder.getPromotion_details().getPromotion_detail() == null) {
            return couponDetails;
        }

        for (TBNormalOrderEntity.Promotion_Detail item : tbOrder.getPromotion_details().getPromotion_detail()) {

            //买就送
            boolean isGift = !ExtUtils.isNullOrEmpty(item.getGift_item_name());
            BusinessGetOrderResponseOrderItemCouponDetailInfo couponDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();
            couponDetailInfo.setPrice(item.getDiscount_fee());
            couponDetailInfo.setSku_id(item.getPromotion_name());
            couponDetailInfo.setOriginFiledName(item.getPromotion_name());
            couponDetailInfo.setCouponSource(BusinessGetOrderResponseOrderItem_OrderCouponSourceEnum.JH_TAOBAO.toString());

            if (isGift) {
                //淘宝赠品满送
                couponDetailInfo.setType(item.getGift_item_name());
                couponDetailInfo.setCouponNum(ExtUtils.isNullOrEmpty(item.getGift_item_num()) ? 0 : Integer.parseInt(item.getGift_item_num()));
                couponDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_FREECOUPON);
            } else {
                //淘宝非赠品
                couponDetailInfo.setType(item.getPromotion_desc());
                couponDetailInfo.setCouponNum(1);
                couponDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_OTHER);
            }

            couponDetails.add(couponDetailInfo);
        }

        return couponDetails;
    }

    /**
     * 平台优惠信息处理
     *
     * @param tbOrder 淘宝订单原始信息
     * @return 优惠详情
     */
    private static List<BusinessGetOrderResponseOrderItemCouponDetailInfo> dealPlatCouponDetails(TBNormalOrderEntity.Trade tbOrder) {

        List<BusinessGetOrderResponseOrderItemCouponDetailInfo> couponDetails = new ArrayList<>();

        BigDecimal divideValue = BigDecimal.valueOf(100);

        //淘金币平台补贴
        BigDecimal propointFee = ExtUtils.toDecimalNotNull(tbOrder.getPropoint()).divide(divideValue, 2, RoundingMode.HALF_UP);
        if (BigDecimal.ZERO.compareTo(propointFee) < 0) {
            BusinessGetOrderResponseOrderItemCouponDetailInfo couponDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();
            couponDetailInfo.setPrice(propointFee.toString());
            couponDetailInfo.setType("淘金币平台补贴");
            couponDetailInfo.setOriginFiledName("propoint");
            couponDetailInfo.setCouponSource(BusinessGetOrderResponseOrderItem_OrderCouponSourceEnum.JH_PLAT.toString());
            couponDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_PROPOINT);
            couponDetailInfo.setCouponNum(1);
            couponDetails.add(couponDetailInfo);
        }

        //天猫集分宝：不为null且大于0，则除100添加优惠
        if (tbOrder.getAlipay_point() != null && BigDecimal.valueOf(0).compareTo(tbOrder.getAlipay_point()) < 0) {
            BusinessGetOrderResponseOrderItemCouponDetailInfo couponDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();

            BigDecimal alipayPoint = ExtUtils.toDecimalNotNull(tbOrder.getAlipay_point()).divide(divideValue, 2, RoundingMode.HALF_UP);
            couponDetailInfo.setPrice(alipayPoint.toString());
            couponDetailInfo.setType("天猫集分宝");
            couponDetailInfo.setOriginFiledName("alipay_point");
            couponDetailInfo.setCouponSource(BusinessGetOrderResponseOrderItem_OrderCouponSourceEnum.JH_PLAT.toString());
            couponDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_TMJFB);
            couponDetailInfo.setCouponNum(1);

            couponDetails.add(couponDetailInfo);
        }

        // 天猫红包：不为null且大于0，则除100添加优惠
        if (tbOrder.getTmall_coupon_fee() != null && BigDecimal.ZERO.compareTo(tbOrder.getTmall_coupon_fee()) < 0) {
            BusinessGetOrderResponseOrderItemCouponDetailInfo tMallCouponFeeDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();

            BigDecimal tMallCouponFee = ExtUtils.toDecimalNotNull(tbOrder.getTmall_coupon_fee()).divide(divideValue, 2, RoundingMode.HALF_UP);
            tMallCouponFeeDetailInfo.setPrice(tMallCouponFee.toString());
            tMallCouponFeeDetailInfo.setType("天猫红包");
            tMallCouponFeeDetailInfo.setOriginFiledName("tmall_coupon_fee");
            tMallCouponFeeDetailInfo.setCouponSource(BusinessGetOrderResponseOrderItem_OrderCouponSourceEnum.JH_PLAT.toString());
            tMallCouponFeeDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_TMallRedCoupon);
            tMallCouponFeeDetailInfo.setCouponNum(1);

            couponDetails.add(tMallCouponFeeDetailInfo);
        }

        // 红包：不为null且大于0，则除100添加优惠
        if (tbOrder.getCoupon_fee() != null && BigDecimal.ZERO.compareTo(tbOrder.getCoupon_fee()) < 0) {
            BusinessGetOrderResponseOrderItemCouponDetailInfo couponDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();

            BigDecimal couponFee = ExtUtils.toDecimalNotNull(tbOrder.getCoupon_fee()).divide(divideValue, 2, RoundingMode.HALF_UP);
            couponDetailInfo.setPrice(couponFee.toString());
            couponDetailInfo.setType("红包");
            couponDetailInfo.setOriginFiledName("coupon_fee");
            couponDetailInfo.setCouponSource(BusinessGetOrderResponseOrderItem_OrderCouponSourceEnum.JH_PLAT.toString());
            couponDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_REDCOUPON);
            couponDetailInfo.setCouponNum(1);

            couponDetails.add(couponDetailInfo);
        }

        // 平台购物券
        if (tbOrder.getPlatform_subsidy_fee() != null && BigDecimal.ZERO.compareTo(tbOrder.getPlatform_subsidy_fee()) < 0) {
            BusinessGetOrderResponseOrderItemCouponDetailInfo platformSubsidyFeeDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();

            BigDecimal platformSubsidyFee = ExtUtils.toDecimalNotNull(tbOrder.getPlatform_subsidy_fee());
            platformSubsidyFeeDetailInfo.setPrice(platformSubsidyFee.toString());
            platformSubsidyFeeDetailInfo.setType("平台购物券");
            platformSubsidyFeeDetailInfo.setOriginFiledName("platform_subsidy_fee");
            platformSubsidyFeeDetailInfo.setCouponSource(BusinessGetOrderResponseOrderItem_OrderCouponSourceEnum.JH_PLAT.toString());
            platformSubsidyFeeDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_PLATCOUPON);
            platformSubsidyFeeDetailInfo.setCouponNum(1);

            couponDetails.add(platformSubsidyFeeDetailInfo);
        }

        return couponDetails;
    }

    //endregion

    //region 淘宝前N有礼处理

    /**
     * 天猫周期购订单处理
     *
     * @param tbOrder       淘宝RDS报文
     * @param standardOrder 转换为api的报文
     */
    private static void dealCycleOrder(TBNormalOrderEntity.Trade tbOrder, BusinessGetOrderResponseOrderItem standardOrder) {
        if (tbOrder.getDelivery_plan() == null || tbOrder.getDelivery_plan().getDelivery_plan() == null || tbOrder.getDelivery_plan().getDelivery_plan().length == 0) {
            return;
        }

        // 处理周期购数据
        List<BusinessGetOrderResponseOrderDeliveryPlanInfo> deliveryPlanList = new ArrayList<>();
        for (TBNormalOrderEntity.DeliveryPlan tbDeliveryPlan : tbOrder.getDelivery_plan().getDelivery_plan()) {
            BusinessGetOrderResponseOrderDeliveryPlanInfo deliveryPlan = new BusinessGetOrderResponseOrderDeliveryPlanInfo();
            deliveryPlan.setCurrPhase(tbDeliveryPlan.getCurr_phase());
            deliveryPlan.setGoodsNum(tbDeliveryPlan.getGoods_num());
            deliveryPlan.setOaid(tbDeliveryPlan.getOaid());
            deliveryPlan.setOrderId(tbDeliveryPlan.getOrder_id());
            deliveryPlan.setPlanId(tbDeliveryPlan.getPlan_id());
            deliveryPlan.setPlanRefundStatus(tbDeliveryPlan.getPlan_refund_status());
            deliveryPlan.setPlanStatus(tbDeliveryPlan.getPlan_status());
            deliveryPlan.setPrepareTimeBegin(tbDeliveryPlan.getPrepare_time_begin());
            deliveryPlan.setReceiverAddress(tbDeliveryPlan.getReceiver_address());
            deliveryPlan.setReceiverCity(tbDeliveryPlan.getReceiver_city());
            deliveryPlan.setReceiverCountry(tbDeliveryPlan.getReceiver_country());
            deliveryPlan.setReceiverDistrict(tbDeliveryPlan.getReceiver_district());
            deliveryPlan.setReceiverName(tbDeliveryPlan.getReceiver_name());
            deliveryPlan.setReceiverMobile(tbDeliveryPlan.getReceiver_mobile());
            deliveryPlan.setReceiverPhone(tbDeliveryPlan.getReceiver_phone());
            deliveryPlan.setReceiverState(tbDeliveryPlan.getReceiver_state());
            deliveryPlan.setReceiverTown(tbDeliveryPlan.getReceiver_town());
            deliveryPlan.setShipTimeBegin(tbDeliveryPlan.getShip_time_begin());
            deliveryPlanList.add(deliveryPlan);
        }

        // 赋值到标准报文对象上
        standardOrder.setPlatDeliveryPlan(deliveryPlanList);
    }

    // endregion

    // region

    /**
     * 淘宝花呗分期购信息处理
     *
     * @param outGoodInfo 菠萝派报文格式商品信息
     * @param good        淘宝商品级信息
     */
    private static void dealInstallmentGoods(@Out BusinessGetOrderResponseOrderItemGoodInfo outGoodInfo, TBNormalOrderEntity.Order good) {
        // 花呗分期期数
        outGoodInfo.setInstalmentNumber(good.getFqg_num());
        // 是否商家承担手续费
        outGoodInfo.setIsMerchantBearFee(good.getIs_fqg_s_fee());
    }

    // endregion

    /**
     * 处理承诺发货时间
     *
     * @param tbOrder 淘宝原始报文
     * @return 剩余发货时间
     */
    private static LocalDateTime dealLastSendDate(TBNormalOrderEntity.Trade tbOrder) {
        // 解析Os_date+Os_range作为LastSendDate
        return parseOs_date_Os_range(tbOrder);
    }

    /**
     * 解析Os_date_Os_range作为LastSendDate
     *
     * @param tbOrder
     * @return
     */
    private static LocalDateTime parseOs_date_Os_range(TBNormalOrderEntity.Trade tbOrder) {
        if (tbOrder == null || StringUtils.isEmpty(tbOrder.getOs_date()) || StringUtils.isEmpty(tbOrder.getOs_range())) {
            return null;
        }

        //"os_range":"18:00-20:00", "os_date":"2023-03-22"
        String[] strArr = tbOrder.getOs_range().split("-");
        if (strArr.length != 2) {
            return null;
        }

        return LocalDateTime.parse(String.format("%s %s", tbOrder.getOs_date(), strArr[0]), TimeFormatEnum.LONG_DATE_PATTERN_NONESENCONDS.formatter);
    }

    /**
     * 解析商品级的order_attr里的最小estConTime作为LastSendDate
     *
     * @param tbOrder
     * @return
     */
    private static LocalDateTime parseEstConTime(TBNormalOrderEntity.Trade tbOrder) {
        // 商品级最小时间
        LocalDateTime minDateTime = null;

        if (tbOrder.getOrders() == null || tbOrder.getOrders().getOrder() == null || tbOrder.getOrders().getOrder().length == 0) {
            return null;
        }

        // 循环遍历每一个商品
        for (TBNormalOrderEntity.Order tbGoodsInfo : tbOrder.getOrders().getOrder()) {
            if (StringUtil.isEmpty(tbGoodsInfo.getOrder_attr())) {
                continue;
            }
            Map<String, Object> dicResult = JsonUtils.deJson(tbGoodsInfo.getOrder_attr(), new TypeReference<Map<String, Object>>() {
            });

            if (dicResult == null || !dicResult.containsKey("estConTime")) {
                continue;
            }
            String str = dicResult.get("estConTime").toString();
            if (StringUtils.isEmpty(str)) {
                continue;
            }
            String[] strArr = str.split("_");
            if (strArr.length != 2) {
                continue;
            }

            //"order_attr":"{"estConTime":"1_2023-02-05"}"。 1_2023-02-05 含义解读 1 代表绝对发货时间，2023-02-05发货最晚时间
            //"order_attr": "{"estConTime":"2_7"}"。2_7  含义解读：2 代表相对发货时间，7代表付款后7天内发货。
            //"_"分割后，如果前面是1，承诺发货时间用后面的日期+ 23:59:59，如 2023-02-05 23:59:59。如果前面是2，承诺发货时间 用付款时间+天的时间。
            LocalDateTime dateTime = null;
            if (OmsApiConstant.CHAR_1.equals(strArr[0])) {
                dateTime = DateTimeUtils.shortDateStringToTimeV2(strArr[1]);
            }
            if (OmsApiConstant.CHAR_2.equals(strArr[0])) {
                LocalDateTime payTime = DateTimeUtils.stringToTime2(tbOrder.getPay_time());
                if (payTime != null) {
                    dateTime = payTime.plusDays(Long.parseLong(strArr[1]));
                }
            }

            if (dateTime == null) {
                continue;
            }

            // 找出最早的时间
            if (minDateTime == null || minDateTime.isAfter(dateTime)) {
                minDateTime = dateTime;
            }


        }
        return minDateTime;
    }


    /**
     * 解析商品级的estimate_con_time作为lastSendDate
     *
     * @param tbOrder
     * @return
     */
    private static LocalDateTime parseEstimate_con_time(TBNormalOrderEntity.Trade tbOrder) {
        if (tbOrder.getOrders() != null && tbOrder.getOrders().getOrder() != null) {
            // 解析Estimate_con_time信息，格式：“付款后5天内“、“2022年03月31日24点前“
            LocalDateTime minDateTime = null;
            for (TBNormalOrderEntity.Order tbGoodsInfo : tbOrder.getOrders().getOrder()) {
                if (StringUtil.isEmpty(tbGoodsInfo.getEstimate_con_time())) {
                    continue;
                }
                LocalDateTime dateTime = parseGoodsEstimate_con_time(tbOrder, tbGoodsInfo);

                if (null == dateTime) {
                    continue;
                }

                // 找出最早的时间
                if (minDateTime == null || minDateTime.isAfter(dateTime)) {
                    minDateTime = dateTime;
                }
            }
            if (minDateTime != null) {
                return minDateTime;
            }
        }
        return null;
    }

    /**
     * 服务标签 存在承诺发货时间处理
     *
     * @param logisticsTag 服务标签
     * @param payTime      支付时间
     * @return 承诺发货时间
     */
    private static LocalDateTime dealLogisticServiceTag(TBNormalOrderEntity.Logistics_Tag logisticsTag, String payTime) {
        if (StringUtils.isEmpty(payTime)) {
            return null;
        }

        TBNormalOrderEntity.Logistic_Service_Tag[] logisticServiceTagArr = logisticsTag.getLogistic_service_tag_list().getLogistic_service_tag();
        for (TBNormalOrderEntity.Logistic_Service_Tag logisticServiceTag : logisticServiceTagArr) {
            if (StringUtils.isEmpty(logisticServiceTag.getService_tag()) || StringUtils.isEmpty(logisticServiceTag.getService_type())) {
                continue;
            }

            // 可能会出现单个字母小写的情况，全部转换为大写
            String serviceType = logisticServiceTag.getService_type().toUpperCase();
            if (!serviceType.contains(TB_CONSIGN_DATE)) {
                continue;
            }

            // 物流服务下的标签属性,多个标签之间有";"分隔(淘宝开放文档)
            String[] serviceTagArr = logisticServiceTag.getService_tag().split(";");
            for (String serviceTag : serviceTagArr) {
                if (serviceTag.contains("consignDate=")) {
                    String[] consignDateArr = serviceTag.split("=");
                    return Objects.requireNonNull(DateTimeUtils.stringToTime(payTime)).plusHours(Integer.parseInt(consignDateArr[1]));
                }
            }
        }
        return null;
    }

    //endregion

    //region 淘宝前N有礼处理

    /**
     * 淘宝前N有礼处理
     *
     * @param orders        淘宝RDS报文
     * @param standardOrder 转换为api的报文
     */
    private static void dealFirstNHaveAGift(TBNormalOrderEntity.Orders orders, BusinessGetOrderResponseOrderItem standardOrder) {
        // 是否所有活动货品完成
        boolean isAllActivityGoodsFinish = true;

        for (TBNormalOrderEntity.Order tbGoodsInfo : orders.getOrder()) {
            if (StringUtils.isEmpty(tbGoodsInfo.getOs_activity_id()) || StringUtils.isEmpty(tbGoodsInfo.getOs_fg_item_id())) {
                continue;
            }

            standardOrder.setIsTaobaoActivity(true);
            // 添加商品信息
            BusinessGetOrderResponseOrderItemGoodInfo goodsInfo = new BusinessGetOrderResponseOrderItemGoodInfo();
            goodsInfo.setPlatGoodsId(tbGoodsInfo.getOs_fg_item_id());
            goodsInfo.setSubOrderNo(tbGoodsInfo.getOs_fg_item_id());
            BigDecimal goodsCount = !StringUtils.isEmpty(tbGoodsInfo.getOs_gift_count()) ? new BigDecimal(tbGoodsInfo.getOs_gift_count()) : new BigDecimal(0);
            goodsInfo.setGoodsCount(goodsCount);
            goodsInfo.setTradeGoodsName("前N有礼活动礼品");
            goodsInfo.setIsTaobaoActivityGift(true);
            goodsInfo.setRefundStatus(mapRefundStatus.getOrDefault(tbGoodsInfo.getRefund_status(), Business_RefundStatuEnum.JH_07));
            standardOrder.getGoodInfos().add(goodsInfo);

            //为空，直接设置状态为未完成，不为空，则将活动商品添加到商品列表中
            if (StringUtils.isEmpty(tbGoodsInfo.getOs_sort_num())) {
                isAllActivityGoodsFinish = false;
            }
        }

        standardOrder.setTaobaoActivityStatus(isAllActivityGoodsFinish ? TaobaoActivityStatusEnum.ACTIVITY_FINISHED : TaobaoActivityStatusEnum.ACTIVITY_UNFINISHED);
    }

    //endregion

    // region 区域零售订单处理

    /**
     * 区域零售订单处理
     *
     * @param order         淘宝RDS报文
     * @param standardOrder 转换为api的报文
     */
    private static void dealRegionRetail(TBNormalOrderEntity.Trade order, BusinessGetOrderResponseOrderItem standardOrder) {
        // 区域零售订单和天猫轻购门店订单发货接口不同，所以不能把门店订单当做区域零售订单。区域零售的识别字段tcps_code需要平台开启“天猫新零售”权限，暂时先用seller_memo识别。
        // 判断tcps_code，不为空则表示为区域零售订单。
        final boolean regionRetailOrder = (!ExtUtils.isNullOrEmpty(order.getSeller_memo()) && order.getSeller_memo().contains("区域零售"))
                || (!ExtUtils.isNullOrEmpty(order.getTcps_code()) && order.getTcps_code().contains("storeJsd"));
        if (regionRetailOrder) {
            standardOrder.setIsRegionRetail(YesOrNo.YES.getCode());
        }
    }

    // endregion

    //region RDS时效订单处理

    /**
     * 时效订单处理
     *
     * @param tbOrder       tbrds订单
     * @param standardOrder 标准订单
     */
    public static void dealTimingOrder(TBNormalOrderEntity.Trade tbOrder, BusinessGetOrderResponseOrderItem standardOrder) {
        //时效订单字段传值
        standardOrder.setTimingPromise(tbOrder.getTiming_promise());
        standardOrder.setPromiseService(tbOrder.getPromise_service());
        standardOrder.setEsDate(tbOrder.getEs_date());
        standardOrder.setEsRange(tbOrder.getEs_range());
        standardOrder.setOsDate(tbOrder.getOs_date());
        standardOrder.setOsRange(tbOrder.getOs_range());
        standardOrder.setCutoffMinutes(tbOrder.getCutoff_minutes());
        standardOrder.setEsTime(tbOrder.getEs_time());
        standardOrder.setDeliveryTime(tbOrder.getDelivery_time());
        standardOrder.setCollectTime(tbOrder.getCollect_time());
        standardOrder.setDispatchTime(tbOrder.getDispatch_time());
        standardOrder.setSignTime(tbOrder.getSign_time());
        standardOrder.setStoreCode(tbOrder.getRetail_store_code());
        standardOrder.setLatestSignTime(tbOrder.getPromise_sign_time());
        if (OmsApiConstant.TIMING_PROMISE.equals(tbOrder.getTiming_promise())) {
            standardOrder.setShopType(Business_ShopTypeEnum.JH_004);
            // 时效订单取tradeAttr中的时效信息
            if (!StringUtils.isEmpty(tbOrder.getTrade_attr())) {
                TBOtherTradeAttr attr = JsonUtils.deJson(tbOrder.getTrade_attr(), TBOtherTradeAttr.class);
                standardOrder.setEsDate(attr.getEsDate());
                standardOrder.setEsRange(attr.getEsRange());
                standardOrder.setOsDate(attr.getOsDate());
                standardOrder.setCutoffMinutes(attr.getCutoffMinutes());
                standardOrder.setEsTime(attr.getEsTime());
                standardOrder.setDeliveryTime(attr.getDeliveryTime());
                standardOrder.setCollectTime(attr.getCollectTime());
                standardOrder.setDispatchTime(attr.getDispatchTime());
                standardOrder.setSignTime(attr.getSignTime());
            }
        }
    }

    //endregion

    //region RDS同城购订单处理

    /**
     * 同城购订单处理
     *
     * @param tbOrder       tbrds订单
     * @param standardOrder 标准订单
     */
    public static void dealO2oOrder(TBNormalOrderEntity.Trade tbOrder, BusinessGetOrderResponseOrderItem standardOrder) {
        //同城购订单字段传值
        standardOrder.setO2o(tbOrder.getO2o());
        standardOrder.setO2oDelivery(tbOrder.getO2o_delivery());
        standardOrder.setO2oGuideId(tbOrder.getO2o_guide_id());
        standardOrder.setO2oGuideName(tbOrder.getO2o_guide_name());
        standardOrder.setO2oShopId(tbOrder.getO2o_shop_id());
        standardOrder.setO2oShopName(tbOrder.getO2o_shop_name());
        standardOrder.setOmniChannelParam(tbOrder.getOmnichannel_param());

        // O2O订单平台标记
        if (StringUtils.isNotBlank(tbOrder.getO2o_guide_id())) {

            BusinessGetOrderResponseOrderTag orderTag = new BusinessGetOrderResponseOrderTag();
            orderTag.setKey(ApiPlatFlagEnum.JH_O2OORDER.getCode());
            orderTag.setDesc(ApiPlatFlagEnum.JH_O2OORDER.getCaption());

            standardOrder.addOrderTag(orderTag);
        }
    }

    //endregion

    //region 物流信息赋值

    /**
     * 淘宝物流信息赋值
     *
     * @param tbOrder
     * @param standardOrder
     */
    public static void dealLogisticInfo(TBNormalOrderEntity.Trade tbOrder, BusinessGetOrderResponseOrderItem standardOrder) {
        if (tbOrder.getOrders() == null || tbOrder.getOrders().getOrder() == null || tbOrder.getOrders().getOrder().length == 0) {
            return;
        }

        TBNormalOrderEntity.Order order = tbOrder.getOrders().getOrder()[0];

        standardOrder.setLogisticNo(order.getInvoice_no());
        standardOrder.setLogisticName(order.getLogistics_company());
        if (OmsApiConstant.ASDP_BIZ_TYPE.equals(tbOrder.getAsdp_biz_type())) {
            standardOrder.setLogisticName(tbOrder.getDelivery_cps());
        }
    }

    //endregion

    //region 物流信息赋值

    /**
     * logistics_agreement节点解析
     *
     * @param tbOrder
     * @param standardOrder
     */
    public static void dealLogisticAgreement(TBNormalOrderEntity.Trade tbOrder, BusinessGetOrderResponseOrderItem standardOrder) {
        if (tbOrder.getOrders() == null || tbOrder.getOrders().getOrder() == null || tbOrder.getOrders().getOrder().length == 0) {
            return;
        }

        if (tbOrder.getLogistics_agreement() != null) {
            TBNormalOrderEntity.LogisticsAgreement logisticsAgreement = tbOrder.getLogistics_agreement();
            standardOrder.setSinkType(logisticsAgreement.getSink_type());
            // 承诺/最晚送达时间
            if (!StringUtils.isEmpty(logisticsAgreement.getPromise_sign_time())) {
                standardOrder.setLatestSignTime(logisticsAgreement.getPromise_sign_time());
            }
            // 承诺/最晚送达时间
            if (!StringUtils.isEmpty(logisticsAgreement.getPush_time())) {
                standardOrder.setLatestPushTime(logisticsAgreement.getPush_time());
            }
        }
    }

    //endregion

    //region 淘宝物流供应链信息赋值

    /**
     * 淘宝物流供应链信息赋值
     *
     * @param tbOrder       淘宝原数据
     * @param standardOrder 转换后的聚合对象订单数据
     */
    private static void dealLogisticChainInfo(TBNormalOrderEntity.Trade tbOrder, BusinessGetOrderResponseOrderItem standardOrder) {
        if (tbOrder.getOrders() == null || tbOrder.getOrders().getOrder() == null || tbOrder.getOrders().getOrder().length == 0) {
            return;
        }

        // 判断是否开启配置键
        if (!ConfigUtil.isConfig(YunConfigUtils.getDBConfig(ConfigKeyEnum.TAO_BAO_LOGISTICS_CHAIN_ENABLE), tbOrder.getSeller_nick())) {
            return;
        }

        if (tbOrder.getLogistics_agreement() != null) {
            TBNormalOrderEntity.LogisticsAgreement logisticsAgreement = tbOrder.getLogistics_agreement();
            String asdpBizType = logisticsAgreement.getAsdp_biz_type();
            // 判断是否是翱象订单
            if (StringUtils.isEmpty(asdpBizType) || !OmsApiConstant.ASDP_BIZ_TYPE_AOX.equals(asdpBizType)) {
                return;
            }
            standardOrder.setOrderType(asdpBizType);

            if (!StringUtils.isEmpty(logisticsAgreement.getLogistics_service_msg())) {
                standardOrder.setLogisticsServiceMsg(logisticsAgreement.getLogistics_service_msg());
            }

            String asdpAds = logisticsAgreement.getAsdp_ads();
            if (StringUtils.isEmpty(asdpAds)) {
                asdpAds = tbOrder.getAsdp_ads();
            }
            standardOrder.setAsdpAds(asdpAds);

            // 计划送达时间
            if (!StringUtils.isEmpty(logisticsAgreement.getSign_time())) {
                standardOrder.setEsDate(logisticsAgreement.getSign_time());
            }

            // 承诺/最晚送达时间
            if (!StringUtils.isEmpty(logisticsAgreement.getPromise_sign_time())) {
                standardOrder.setLatestSignTime(logisticsAgreement.getPromise_sign_time());
            }
        }

        if (tbOrder.getLogistics_infos() != null && tbOrder.getLogistics_infos().getLogistics_info() != null && tbOrder.getLogistics_infos().getLogistics_info().length > 0) {
            String promiseCollectTime = "";
            String deliveryTime = "";
            for (TBNormalOrderEntity.Logistics_Info item : tbOrder.getLogistics_infos().getLogistics_info()) {
                // 承诺/最晚揽收时间（取最早那个）
                if (!StringUtils.isEmpty(item.getPromise_collect_time())
                        && (StringUtils.isEmpty(promiseCollectTime) || promiseCollectTime.compareTo(item.getPromise_collect_time()) > 0)) {
                    promiseCollectTime = item.getPromise_collect_time();
                }

                // 承诺/最晚出库时间（取最早那个）
                if (!StringUtils.isEmpty(item.getPromise_outbound_time())
                        && (StringUtils.isEmpty(deliveryTime) || deliveryTime.compareTo(item.getPromise_outbound_time()) > 0)) {
                    deliveryTime = item.getPromise_outbound_time();
                }

                BusinessGetOrderResponseOrderItemGoodInfo goodInfo = standardOrder.getGoodInfos().stream().filter(t -> t.getSubOrderNo().equals(item.getSub_trade_id())).findFirst().orElse(null);
                if (goodInfo != null && item.getPromise_outbound_time() != null) {
                    try {
                        // 设置商品级承诺/最晚出库时间
                        goodInfo.setSendDeadLine(DateTimeUtils.stringToTime(item.getPromise_outbound_time()));
                    } catch (Exception e) {
                        LogAdapter.writeSystemLog("淘宝最晚出库时间转换", () -> ExtUtils.stringBuilderAppend("转换异常，", "订单号：", String.valueOf(tbOrder.getTid()), "商品子单号：", item.getSub_trade_id()));
                    }
                }
            }

            if (!StringUtils.isEmpty(promiseCollectTime)) {
                standardOrder.setPromiseCollectTime(promiseCollectTime);
            }
            if (!StringUtils.isEmpty(deliveryTime)) {
                standardOrder.setDeliveryTime(deliveryTime);
            }
        }
    }

    // endregion 淘宝物流供应链信息赋值

    //region RDS经销订单实体转换

    /**
     * 经销订单集合 将rds查出的数据类型 转为 标准数据类型结构
     *
     * @param dataOriginOrders 从数据库查出的数据订单集合
     * @param loadTime         订单下载时间
     * @return RDS经销订单实体返回集合
     */
    public static List<IRDSResponse> convertToOrderListForJX(List<RDSOrderEntity> dataOriginOrders, LocalDateTime loadTime) {
        List<IRDSResponse> dataResponse = new ArrayList<>();
        //解析普通订单数据
        for (RDSOrderEntity item : dataOriginOrders) {
            //为空则跳过
            if (ExtUtils.isNullOrEmpty(item.getJsonOrderData())) {
                continue;
            }

            DomainUtils.doTryCatch("[淘宝RDS抓单]解析经销订单json", ApplicationTypeEnum.TASK, () -> {
                //转为TB对应的订单实体
                TBJXOrderEntity tbJXOrder = JsonUtils.deJson(item.getJsonOrderData(), TBJXOrderEntity.class);
                List<BusinessGetOrderResponseOrderItem> lstOrder = convertToOrderForJX(tbJXOrder, loadTime, item);
                if (lstOrder == null || lstOrder.isEmpty()) {
                    return true;
                }
                lstOrder.forEach(order -> order.setHashVal(item.getHashCode()));
                RDSNormalOrderResponse orderResponse = new RDSNormalOrderResponse(lstOrder, item.getSellerNick(), item.getModifiedTime());
                dataResponse.add(orderResponse);
                return true;
            });
        }
        return dataResponse;
    }

    /**
     * 经销订单 将rds查出的数据类型 转为 标准数据类型结构
     *
     * @param entity   淘宝普通订单
     * @param loadTime 订单下载时间
     * @return RDS标准订单
     */
    public static List<BusinessGetOrderResponseOrderItem> convertToOrderForJX(TBJXOrderEntity entity, LocalDateTime loadTime, RDSOrderEntity rdsOrderEntity) {
        if (entity == null || entity.getFenxiao_dealer_requisitionorder_query_response() == null
                || entity.getFenxiao_dealer_requisitionorder_query_response().getDealer_orders() == null
                || entity.getFenxiao_dealer_requisitionorder_query_response().getDealer_orders().getDealer_order() == null) {
            return null;
        }
        TBJXOrderEntity.Dealer_Order[] tbOrders = entity.getFenxiao_dealer_requisitionorder_query_response().getDealer_orders().getDealer_order();
        List<BusinessGetOrderResponseOrderItem> orderList = new ArrayList<>();
        //region 订单主体
        for (TBJXOrderEntity.Dealer_Order tbOrder : tbOrders) {
            BusinessGetOrderResponseOrderItem order = new BusinessGetOrderResponseOrderItem();
            order.setModifyTime(ExtUtils.isNullOrEmpty(tbOrder.getModified_time()) ?
                    DateTimeUtils.stringToTime(tbOrder.getApplied_time()) :
                    DateTimeUtils.stringToTime(tbOrder.getModified_time()));
            // 赋值推送库修改时间
            order.setIncrementModifyTime(rdsOrderEntity.getModifiedTime());
            // 赋值推送库订单创建时间
            order.setIncrementCreateTime(rdsOrderEntity.getCreateTime());
            order.setLoadTime(loadTime);
            order.setIsEncrypt(true);
            order.setPlatOrderNo(tbOrder.getDealer_order_id());
            order.setTradeStatusDescription(tbOrder.getOrder_status());
            order.setTradeTime(DateTimeUtils.stringToTime(tbOrder.getApplied_time()));
            order.setNick(tbOrder.getApplier_nick());
            order.setReceiverName(tbOrder.getReceiver().getName());
            order.setPayOrderNo(tbOrder.getAlipay_no());
            order.setPayTime(DateTimeUtils.stringToTime(tbOrder.getPay_time()));
            order.setProvince(tbOrder.getReceiver().getState());
            order.setCity(tbOrder.getReceiver().getCity());
            order.setArea(tbOrder.getReceiver().getDistrict());
            order.setAddress(tbOrder.getReceiver().getAddress());
            order.setZip(tbOrder.getReceiver().getZip());
            order.setPhone(tbOrder.getReceiver().getPhone());
            order.setMobile(tbOrder.getReceiver().getMobile_phone());
            order.setCustomerRemark(tbOrder.getRefuse_reason_applier());
            order.setSellerRemark(tbOrder.getSupplier_memo());
            order.setPostFee(ExtUtils.toDecimal(tbOrder.getLogistics_fee()));
            order.setGoodsFee(ExtUtils.toDecimal(tbOrder.getTotal_price()));
            order.setTotalAmount(ExtUtils.toDecimal(tbOrder.getTotal_price()));
            order.setRealPayMoney(ExtUtils.toDecimal(tbOrder.getTotal_price()));
            order.setIdCard(tbOrder.getReceiver().getCard_id());
            order.setShopType(Business_ShopTypeEnum.JH_003);
            order.setShouldPayType(tbOrder.getPay_type());
            order.setSendStyle(mapJXShipping.getOrDefault(tbOrder.getLogistics_type(), tbOrder.getLogistics_type()));
            order.setSendType(mapPolyJXShipping.getOrDefault(tbOrder.getLogistics_type(), BusinessGetOrderResponseOrderItem_SendTypeEnum.JH_OTHER));
            order.setPayType(mapPolyPayType.getOrDefault(tbOrder.getPay_type(), BusinessGetOrderResponseOrderItem_PayTypeEnum.JH_ALIPAY));
            order.setTradeStatus(mapJXResponseStatus.getOrDefault(tbOrder.getOrder_status(), Business_OrderStatusEnum.JH_98));
            order.setBuyerOpenUid(tbOrder.getBuyer_open_uid());
            order.setSupplierName(tbOrder.getSupplier_nick());
            //商品明细
            List<BusinessGetOrderResponseOrderItemGoodInfo> goodInfos = new ArrayList<>();
            if (tbOrder.getDealer_order_details() != null && tbOrder.getDealer_order_details().getDealer_order_detail() != null) {
                for (TBJXOrderEntity.Dealer_Order_Detail tbGood : tbOrder.getDealer_order_details().getDealer_order_detail()) {
                    BusinessGetOrderResponseOrderItemGoodInfo good = new BusinessGetOrderResponseOrderItemGoodInfo();
                    good.setProductId(tbGood.getProduct_id());
                    good.setPlatGoodsId(tbGood.getProduct_id());
                    good.setPlatSkuId(tbGood.getSku_id());
                    good.setSubOrderNo(tbGood.getDealer_detail_id());
                    good.setTradeGoodsNo(tbGood.getSku_number());
                    good.setOutSkuId(tbGood.getSku_number());
                    good.setTradeGoodsName(tbGood.getProduct_title());
                    good.setTradeGoodsSpec(tbGood.getSku_spec());
                    good.setGoodsCount(ExtUtils.toDecimalNotNull(tbGood.getQuantity()));
                    good.setPrice(ExtUtils.toDecimal(tbGood.getFinal_price()));
                    good.setDiscountMoney(BigDecimal.valueOf(0));
                    good.setStatus(BusinessGetOrderResponseOrderItem_SubOrderStatuEnum.create(order.getTradeStatus().toString()));
                    if (good.getGoodsCount() != null && good.getGoodsCount().intValue() > 0) {
                        good.setPrice(ExtUtils.toDecimalNotNull(tbGood.getPrice_count())
                                .divide(good.getGoodsCount() == null ? BigDecimal.valueOf(0) : good.getGoodsCount(), 2, RoundingMode.HALF_UP));
                    }
                    if (order.getTradeStatus().equals(Business_OrderStatusEnum.JH_02) && tbGood.isIs_deleted()) {
                        good.setRefundStatus(Business_RefundStatuEnum.JH_06);
                    } else {
                        good.setRefundStatus(Business_RefundStatuEnum.JH_07);
                    }
                    goodInfos.add(good);
                }
            }
            order.setGoodInfos(goodInfos);
            orderList.add(order);
        }

        //endregion
        return orderList;
    }

    //endregion

    //region RDS分销订单实体转换

    /**
     * 分销订单集合 将rds查出的数据类型 转为 标准数据类型结构
     *
     * @param dataOriginOrders 从数据库查出的数据订单集合
     * @param loadTime         订单下载时间
     * @return RDS分销订单实体返回集合
     */
    public static List<IRDSResponse> convertToOrderListForFX(List<RDSOrderEntity> dataOriginOrders, LocalDateTime loadTime) {
        List<IRDSResponse> dataResponse = new ArrayList<>();
        //解析普通订单数据
        for (RDSOrderEntity item : dataOriginOrders) {
            //为空则跳过
            if (ExtUtils.isNullOrEmpty(item.getJsonOrderData())) {
                continue;
            }
            DomainUtils.doTryCatch("[淘宝RDS抓单]解析分销订单json", ApplicationTypeEnum.TASK, () -> {
                //转为TB对应的订单实体
                TBFXOrderEntity tbFXOrder = JsonUtils.deJson(item.getJsonOrderData(), TBFXOrderEntity.class);
                List<BusinessGetOrderResponseOrderItem> lstOrder = convertToOrderForFX(tbFXOrder, loadTime, item);
                if (lstOrder == null || lstOrder.isEmpty()) {
                    return true;
                }
                lstOrder.forEach(order -> order.setHashVal(item.getHashCode()));
                RDSNormalOrderResponse orderResponse = new RDSNormalOrderResponse(lstOrder, item.getSellerNick(), item.getModifiedTime());
                dataResponse.add(orderResponse);
                return true;
            });
        }
        return dataResponse;
    }

    /**
     * 分销订单 将rds查出的数据类型 转为 标准数据类型结构
     *
     * @param entity         淘宝普通订单
     * @param loadTime       订单下载时间
     * @param rdsOrderEntity RDS抓单对应实体
     * @return RDS分销订单
     */
    public static List<BusinessGetOrderResponseOrderItem> convertToOrderForFX(TBFXOrderEntity entity, LocalDateTime loadTime, RDSOrderEntity rdsOrderEntity) {
        if (null == entity || null == entity.getFenxiao_orders_get_response() || null == entity.getFenxiao_orders_get_response().getPurchase_orders() || null == entity
                .getFenxiao_orders_get_response().getPurchase_orders().getPurchase_order()) {
            return null;
        }

        TBFXOrderEntity.Purchase_Order[] tbOrders = entity.getFenxiao_orders_get_response().getPurchase_orders().getPurchase_order();
        List<BusinessGetOrderResponseOrderItem> orderList = new ArrayList<>();
        for (TBFXOrderEntity.Purchase_Order tbOrder : tbOrders) {
            BusinessGetOrderResponseOrderItem order = new BusinessGetOrderResponseOrderItem();
            //region 实体对应
            order.setModifyTime(
                    ExtUtils.isNullOrEmpty(tbOrder.getModified()) ? DateTimeUtils.stringToTime(tbOrder.getCreated()) : DateTimeUtils.stringToTime(tbOrder.getModified()));
            // 赋值推送库修改时间
            order.setIncrementModifyTime(rdsOrderEntity.getModifiedTime());
            // 赋值推送库订单创建时间
            order.setIncrementCreateTime(rdsOrderEntity.getCreateTime());
            order.setLoadTime(loadTime);
            order.setIsEncrypt(true);
            order.setPlatOrderNo(tbOrder.getFenxiao_id() + "");
            order.setTradeStatusDescription(tbOrder.getStatus());
            order.setTradeTime(DateTimeUtils.stringToTime(tbOrder.getCreated()));
            order.setNick(tbOrder.getDistributor_username());
            order.setChannelCode(tbOrder.getChannel_code());
            order.setSupplierName(tbOrder.getSupplier_username());
            if (tbOrder.getReceiver() != null) {
                order.setReceiverName(tbOrder.getReceiver().getName());
                order.setPayOrderNo(tbOrder.getAlipay_no());
                order.setProvince(tbOrder.getReceiver().getState());
                order.setCity(tbOrder.getReceiver().getCity());
                order.setArea(tbOrder.getReceiver().getDistrict());
                order.setAddress(tbOrder.getReceiver().getAddress());
                order.setZip(tbOrder.getReceiver().getZip());
                order.setPhone(tbOrder.getReceiver().getPhone());
                order.setMobile(tbOrder.getReceiver().getMobile_phone());
                order.setIdCard(tbOrder.getReceiver().getCard_id());

                if (!StringUtils.isEmpty(tbOrder.getReceiver().getOaid())) {
                    order.setOaid(tbOrder.getReceiver().getOaid());
                }
            }
            order.setBuyerOpenUid(tbOrder.getBuyer_open_uid());
            order.setCustomerRemark(tbOrder.getMemo());
            order.setSellerRemark(tbOrder.getSupplier_memo());
            order.setPostFee(ExtUtils.toDecimal(tbOrder.getPost_fee()));
            order.setGoodsFee(ExtUtils.toDecimal(tbOrder.getTotal_fee()));
            order.setTotalAmount(ExtUtils.toDecimal(tbOrder.getDistributor_payment()));
            order.setRealPayMoney(ExtUtils.toDecimal(tbOrder.getDistributor_payment()));
            order.setSendStyle(mapFXShipping.getOrDefault(tbOrder.getShipping(), tbOrder.getShipping()));
            order.setSendType(mapPolyFXShipping.getOrDefault(tbOrder.getShipping(), BusinessGetOrderResponseOrderItem_SendTypeEnum.JH_OTHER));
            order.setShouldPayType(tbOrder.getPay_type());
            order.setPayType(mapPolyPayType.getOrDefault(tbOrder.getPay_type(), BusinessGetOrderResponseOrderItem_PayTypeEnum.JH_ALIPAY));
            order.setPayTime(DateTimeUtils.stringToTime(tbOrder.getPay_time()));
            order.setSellerFlag(mapSellerFlag.getOrDefault(tbOrder.getSupplier_flag(), BusinessGetOrderResponseOrderItem_SellerFlagEnum.JH_NONE));
            order.setShopType(Business_ShopTypeEnum.JH_002);
            order.setTradeStatus(mapResponseStatus.getOrDefault(tbOrder.getStatus(), Business_OrderStatusEnum.JH_98));
            order.setIsDaiXiao(true);
            //物流信息
            order.setLogisticName(tbOrder.getLogistics_company_name());
            order.setLogisticNo(tbOrder.getLogistics_id());
            // 交易订单号。
            order.setInnerTransactionId(Long.toString(tbOrder.getTc_order_id()));
            // 是否为菜鸟仓代发货
            if (null != tbOrder.getFeatures() && ArrayUtil.isNotEmpty(tbOrder.getFeatures().getFeature())) {
                TBFXOrderEntity.Feature shipper = Arrays.stream(tbOrder.getFeatures().getFeature()).filter(
                        feature -> feature.getAttr_key().equalsIgnoreCase("shipper") && feature.getAttr_value().equalsIgnoreCase(OmsApiConstant.CN_SHIPPER)).findFirst().orElse(null);
                if (Objects.nonNull(shipper)) {
                    order.setIsShip(true);
                }
            }

            // 分销单终端应收合计 = SUM(子单buyer_payment)
            BigDecimal buyerPayment = BigDecimal.ZERO;

            //商品
            List<BusinessGetOrderResponseOrderItemGoodInfo> dataGoods = new ArrayList<>();
            for (TBFXOrderEntity.Sub_Purchase_Order tbGood : tbOrder.getSub_purchase_orders().getSub_purchase_order()) {
                BusinessGetOrderResponseOrderItemGoodInfo good = new BusinessGetOrderResponseOrderItemGoodInfo();

                buyerPayment = buyerPayment.add(ExtUtils.getNonEmpty(ExtUtils.toDecimal(tbGood.getBuyer_payment()), BigDecimal.ZERO));

                good.setProductId(tbGood.getItem_id() + "");
                good.setPlatGoodsId(tbGood.getItem_id() + "");
                good.setPlatSkuId(tbGood.getSku_id() + "");
                good.setSubOrderNo(tbGood.getFenxiao_id() + "");
                good.setTradeGoodsNo(getTradeGoodsNo(tbGood.getItem_outer_id(), tbGood.getSku_outer_id()));
                good.setOutSkuId(tbGood.getSku_outer_id());
                good.setOutId(tbGood.getItem_outer_id());
                good.setOutItemId(tbGood.getItem_outer_id());
                good.setTradeGoodsName(tbGood.getTitle());
                good.setTradeGoodsSpec(tbGood.getOld_sku_properties());
                good.setGoodsCount(BigDecimal.valueOf(tbGood.getNum()));
                good.setPrice(ExtUtils.toDecimal(tbGood.getPrice()));
                good.setGoodsBuyerPayment(ExtUtils.toDecimal(tbGood.getBuyer_payment()));
                good.setGoodsRefundFee(ExtUtils.toDecimal(tbGood.getRefund_fee()));
                good.setGoodsDistributorPayment(ExtUtils.toDecimal(tbGood.getDistributor_payment()));
                good.setDiscountMoney(ExtUtils.toDecimal(tbGood.getDiscount_fee()));
                good.setRefundStatus(getFxOrderGoodsRefundStatus(tbGood));
                good.setStatus(mapFXSubOrderResponseStatus.getOrDefault(tbGood.getOrder_status(), null));
                //分销订单递交oms相关字段
                good.setCustomerSubtradeNo(Long.toString(tbGood.getTc_order_id()));
                good.setCustomerPrice(tbGood.getAuction_price());

                // 分销单国补发票商品信息处理
                good.addPlatOrderOriginalField(RDSTBOrderUtils.convertFxGoodsGovSubsidy(tbOrder, tbGood));

                // 设置淘宝分销订单商品平台标记
                good.setGoodsTags(convertFXGoodsTags(tbOrder, tbGood));

                // 仓库编码
                if (tbGood.getFeatures() != null && tbGood.getFeatures().getFeature() != null && tbGood.getFeatures().getFeature().length > 0) {
                    Arrays.stream(tbGood.getFeatures().getFeature()).forEach(feature -> {
                        // 平台仓库编码
                        if ("storeCode".equalsIgnoreCase(feature.getAttr_key())) {
                            good.setStoreCode(feature.getAttr_value());
                        }

                        // 分销仓库编码
                        if ("tfxStoreCode".equalsIgnoreCase(feature.getAttr_key())) {
                            good.setWarehouseGoodCode(feature.getAttr_value());
                        }

                        //配置键控制商品级外部发货标记判断
                        if (ConfigUtil.isConfig(YunConfigUtils.getDBConfig(ConfigKeyEnum.FX_GOODS_CN_SHIPPER_SUPPLIER_USERNAME), tbOrder.getSupplier_username())) {
                            // 是否为菜鸟仓代发货
                            if ("shipper".equalsIgnoreCase(feature.getAttr_key()) && feature.getAttr_value().equalsIgnoreCase(OmsApiConstant.CN_SHIPPER)) {
                                order.setIsShip(true);
                            }
                        }
                    });
                }

                dataGoods.add(good);
            }

            // 分销单终端应收合计 = SUM(子单buyer_payment)
            order.setBuyerPayment(buyerPayment);

            order.setGoodInfos(dataGoods);

            //BMS
            if (tbOrder.getLogistics_infos() != null && tbOrder.getLogistics_infos().getErp_logistics_info() != null
                    && tbOrder.getLogistics_infos().getErp_logistics_info().length > 0) {
                List<BusinessGetOrderResponseOrderItemLogisticsInfo> logisticsInfos = new ArrayList<>();
                for (TBFXOrderEntity.Erp_Logistics_Info tbItem : tbOrder.getLogistics_infos().getErp_logistics_info()) {
                    BusinessGetOrderResponseOrderItemLogisticsInfo logisticsInfo = new BusinessGetOrderResponseOrderItemLogisticsInfo();
                    logisticsInfo.setConsignType(tbItem.getConsign_type());
                    logisticsInfo.setItemCode(tbItem.getItem_code());
                    logisticsInfo.setItemID(tbItem.getItem_id());
                    logisticsInfo.setNeedConsignNum(tbItem.getNeed_consign_num());
                    logisticsInfo.setNumiid(tbItem.getNum_iid());
                    logisticsInfo.setSkuId(tbItem.getSku_id());
                    logisticsInfo.setStoreCode(tbItem.getStore_code());
                    logisticsInfo.setSubTradeID(tbItem.getSub_order_id());
                    logisticsInfo.setTradeID(tbItem.getOrder_id());
                    logisticsInfo.setType(tbItem.getType());
                    logisticsInfo.setDeliveryCps(tbItem.getDelivery_cps());
                    logisticsInfo.setBizSdType(tbItem.getBiz_sd_type());
                    logisticsInfo.setBizDeliveryCode(tbItem.getBiz_delivery_code());
                    logisticsInfo.setBizStoreCode(tbItem.getBiz_store_code());
                    logisticsInfos.add(logisticsInfo);
                }
                order.setLogisticsInfos(logisticsInfos);
            }

            if (isFxZhenFuBuTie(tbOrder)) {
                BusinessGetOrderResponseOrderTag orderTag = new BusinessGetOrderResponseOrderTag();
                orderTag.setKey(ApiPlatFlagEnum.JH_ZHENGFUBUTIE.getCode());
                orderTag.setDesc(ApiPlatFlagEnum.JH_ZHENGFUBUTIE.getCaption());
                order.addOrderTag(orderTag);
            }

            // 分销单国补发票订单信息处理
            order.addPlatOrderOriginalField(RDSTBOrderUtils.convertFxOrderGovSubsidy(tbOrder));

            //淘宝订单其他属性
            if (tbOrder.getFeatures() != null && tbOrder.getFeatures().getFeature() != null && tbOrder.getFeatures().getFeature().length > 0) {
                TBFXOrderEntity.Feature erpHold = Arrays.stream(tbOrder.getFeatures().getFeature()).filter(i -> "erpHold".equalsIgnoreCase(i.getAttr_key())).findFirst()
                        .orElse(null);
                if (erpHold != null) {
                    TBOtherTradeAttr attr = new TBOtherTradeAttr();
                    attr.setErpHold(erpHold.getAttr_value());
                    order.setTradeAttrJson(JsonUtils.toJson(attr));
                }
            }
            //endregion
            orderList.add(order);
        }
        return orderList;
    }

    /**
     * 是否分销单政府补贴订单
     *
     * @param tbOrder 分销订单
     * @return 是否分销单政府补贴订单
     */
    private static boolean isFxZhenFuBuTie(TBFXOrderEntity.Purchase_Order tbOrder) {

        // 分销单扩展信息为空不处理
        if (tbOrder.getFeatures() == null || ArrayUtils.isEmpty(tbOrder.getFeatures().getFeature())) {
            return false;
        }

        List<TBFXOrderEntity.Feature> features = CollectionsUtil.arrayToList(tbOrder.getFeatures().getFeature());

        for (TBFXOrderEntity.Feature feature : features) {

            // use_gov_subsidy = 1 为分销单政府补贴
            if (OrderGovSubsidyEnum.USE_GOV_SUBSIDY.getCode().equals(feature.getAttr_key()) && ExtUtils.toBoolean(feature.getAttr_value())) {
                return true;
            }

            // _F_zfbt_06 = 1 为分销单政府补贴
            if (OrderGovSubsidyEnum.USE_GOV_SUBSIDY_FX.getCode().equals(feature.getAttr_key()) && ExtUtils.toBoolean(feature.getAttr_value())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 转换淘宝普通订单国补发票信息
     *
     * @param tbOrder 淘宝普通订单原始报文
     * @return 普通单国补发票信息
     */
    private static List<OriginalField> convertOrderGovSubsidy(TBNormalOrderEntity.Trade tbOrder) {

        // 分销单国补信息
        List<OriginalField> govSubsidyList = new ArrayList<>();

        // 交易资金到门店
        if (StringUtils.isNotBlank(tbOrder.getGovStore())) {

            govSubsidyList.add(new OriginalField(OrderGovSubsidyEnum.GOV_STORE.getCode(), tbOrder.getGovStore()));
        }

        // 政府补贴优惠金额
        if (StringUtils.isNotBlank(tbOrder.getGovSubsidyAmountExact())) {
            govSubsidyList.add(new OriginalField(OrderGovSubsidyEnum.GOV_SUBSIDY_AMOUNT_EXACT.getCode(), ExtUtils.toDecimalNotNull(tbOrder.getGovSubsidyAmountExact()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).toString()));
        }

        // 国补支付优惠金额，可能包含信用卡支付渠道及信用卡立减等活动
        if (StringUtils.isNotBlank(tbOrder.getGovSubsidyAmount())) {

            govSubsidyList.add(new OriginalField(OrderGovSubsidyEnum.GOV_SUBSIDY_AMOUNT.getCode(), ExtUtils.toDecimalNotNull(tbOrder.getGovSubsidyAmount()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).toString()));
        }

        // 预计会享受优惠
        if (StringUtils.isNotBlank(tbOrder.getUseGovPredict())) {

            govSubsidyList.add(new OriginalField(OrderGovSubsidyEnum.USE_GOV_PREDICT.getCode(), tbOrder.getUseGovPredict()));
        }

        // 订单支付时，用户是否符合政府补贴资格，不代表最终一定使用国补
        if (StringUtils.isNotBlank(tbOrder.getUseGovSubsidy())) {

            govSubsidyList.add(new OriginalField(OrderGovSubsidyEnum.USE_GOV_SUBSIDY.getCode(), tbOrder.getUseGovSubsidy()));
        }

        // 政府补贴扩展信息标，一品卖多地等场景使用，标识分公司主体
        if (StringUtils.isNotBlank(tbOrder.getGovMainSubject())) {
            govSubsidyList.add(new OriginalField(OrderGovSubsidyEnum.GOV_MAIN_SUBJECT.getCode(), tbOrder.getGovMainSubject()));
        }

        return govSubsidyList;
    }

    /**
     * 转换淘宝普通订单商品国补发票信息
     *
     * @param tbGoods 淘宝普通订单商品原始报文
     * @return 普通单商品国补发票信息
     */
    private static List<OriginalField> convertGoodsGovSubsidy(TBNormalOrderEntity.Order tbGoods) {

        // 分销单国补信息
        List<OriginalField> govSubsidyList = new ArrayList<>();

        // 招商皮信息
        if (StringUtils.isNotBlank(tbGoods.getGovZhaoshangpi())) {
            govSubsidyList.add(new OriginalField(OrderGovSubsidyEnum.GOV_ZHAOSHANGPI.getCode(), tbGoods.getGovZhaoshangpi()));
        }

        // 商品国补SN码校验信息
        if (StringUtils.isNotBlank(tbGoods.getGov_sn_check())) {

            // 构建国补序列号校验规则描述
            String govSnCheckStr = buildGovSnCheckStr(tbGoods.getGov_sn_check());

            govSubsidyList.add(new OriginalField(OrderGovSubsidyEnum.GOV_SN_CHECK.getCode(), govSnCheckStr));
        }

        // 国补订单能效等级：0=无能效 1=一级能效 2=二级能效
        if (StringUtils.isNotBlank(tbGoods.getGovEnergyLevel())) {
            govSubsidyList.add(new OriginalField(OrderGovSubsidyEnum.GOV_ENERGY_LEVEL.getCode(), tbGoods.getGovEnergyLevel()));
        }

        return govSubsidyList;
    }

    /**
     * 转换淘宝普通订单商品自定义信息
     *
     * @param tbGoods 淘宝普通订单商品原始报文
     * @return 普通单商品自定义信息
     */
    private static List<OriginalField> convertGoodsCustomization(TBNormalOrderEntity.Order tbGoods) {

        // 自定义信息
        List<OriginalField> customizationList = new ArrayList<>();

        if (StringUtils.isNotBlank(tbGoods.getCustomization())) {
            TBNormalOrderEntity.Customization customization = JSON.parseObject(tbGoods.getCustomization(), TBNormalOrderEntity.Customization.class);
            if (customization != null && customization.getLuxury() != null && ArrayUtils.isNotEmpty(customization.getLuxury().getServices())) {
                String customizationString = Arrays.stream(customization.getLuxury().getServices()).map(TBNormalOrderEntity.Customization.Service::getValue).filter(Objects::nonNull).map(v -> MessageFormat.format("{0}{1}{2}", v.getName(), OmsApiConstant.COLON, v.getContent())).collect(Collectors.joining(OmsApiConstant.SEMI_COLON));
                customizationList.add(new OriginalField(OmsApiConstant.CUSTOMIZATION, customizationString));
            }
        }

        return customizationList;
    }

    /**
     * 转换淘宝分销订单国补发票信息
     *
     * @param tbOrder 淘宝分销订单原始报文
     * @return 分销单国补发票信息
     */
    private static List<OriginalField> convertFxOrderGovSubsidy(TBFXOrderEntity.Purchase_Order tbOrder) {

        // 分销单扩展信息为空不处理
        if (tbOrder.getFeatures() == null || ArrayUtils.isEmpty(tbOrder.getFeatures().getFeature())) {
            return Collections.emptyList();
        }

        // 分销单国补信息
        List<OriginalField> govSubsidyList = new ArrayList<>();

        // 分销订单扩展字段列表
        List<TBFXOrderEntity.Feature> fxOrderFeatures = CollectionsUtil.arrayToList(tbOrder.getFeatures().getFeature());

        // 循环分销单扩展信息设置分销单国补发票信息
        for (TBFXOrderEntity.Feature feature : fxOrderFeatures) {

            if (feature == null) {
                continue;
            }

            // 交易资金到门店
            if (OrderGovSubsidyEnum.GOV_STORE.getCode().equals(feature.getAttr_key())) {
                govSubsidyList.add(new OriginalField(feature.getAttr_key(), feature.getAttr_value()));
                continue;
            }

            // 政府补贴优惠金额
            if (OrderGovSubsidyEnum.GOV_SUBSIDY_AMOUNT_EXACT.getCode().equals(feature.getAttr_key())) {
                govSubsidyList.add(new OriginalField(feature.getAttr_key(), ExtUtils.toDecimalNotNull(feature.getAttr_value()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).toString()));
                continue;
            }

            // 国补支付优惠金额，可能包含信用卡支付渠道及信用卡立减等活动
            if (OrderGovSubsidyEnum.GOV_SUBSIDY_AMOUNT.getCode().equals(feature.getAttr_key())) {
                govSubsidyList.add(new OriginalField(feature.getAttr_key(), ExtUtils.toDecimalNotNull(feature.getAttr_value()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).toString()));
                continue;
            }

            // 预计会享受优惠
            if (OrderGovSubsidyEnum.USE_GOV_PREDICT.getCode().equals(feature.getAttr_key())) {
                govSubsidyList.add(new OriginalField(feature.getAttr_key(), feature.getAttr_value()));
                continue;
            }

            // 订单支付时，用户是否符合政府补贴资格，不代表最终一定使用国补
            if (OrderGovSubsidyEnum.USE_GOV_SUBSIDY.getCode().equals(feature.getAttr_key())) {
                govSubsidyList.add(new OriginalField(feature.getAttr_key(), feature.getAttr_value()));
                continue;
            }

            // 分销订单是否国补（订单级：use_gov_subsidy 没返回则取 订单级：_F_zfbt_06）
            if (OrderGovSubsidyEnum.USE_GOV_SUBSIDY_FX.getCode().equals(feature.getAttr_key())) {
                govSubsidyList.add(new OriginalField(OrderGovSubsidyEnum.USE_GOV_SUBSIDY.getCode(), feature.getAttr_value()));
            }

        }

        return govSubsidyList;
    }

    /**
     * 转换淘宝分销订单商品国补发票信息
     *
     * @param tbOrder 淘宝分销订单报文
     * @param tbGoods 淘宝分销订单商品原始报文
     * @return 分销单商品国补发票信息
     */
    private static List<OriginalField> convertFxGoodsGovSubsidy(TBFXOrderEntity.Purchase_Order tbOrder, TBFXOrderEntity.Sub_Purchase_Order tbGoods) {

        // 分销订单商品扩展信息为空不处理
        if (tbGoods.getFeatures() == null || ArrayUtils.isEmpty(tbGoods.getFeatures().getFeature())) {
            return Collections.emptyList();
        }

        // 分销订单商品扩展信息
        List<TBFXOrderEntity.Feature> fxOrderGoodsFeatures = CollectionsUtil.arrayToList(tbGoods.getFeatures().getFeature());

        // 分销单国补信息
        List<OriginalField> govSubsidyList = new ArrayList<>();

        // 循环分销单扩展信息设置分销单国补发票信息
        for (TBFXOrderEntity.Feature feature : fxOrderGoodsFeatures) {

            if (feature == null) {
                continue;
            }

            // 招商皮信息
            if (OrderGovSubsidyEnum.GOV_ZHAOSHANGPI.getCode().equals(feature.getAttr_key())) {
                govSubsidyList.add(new OriginalField(feature.getAttr_key(), feature.getAttr_value()));
                continue;
            }

            // 招商皮信息商品级：gov_zhaoshangpi 为空则取 商品级：_F_zfbt_01
            if (OrderGovSubsidyEnum._F_ZFBT_01.getCode().equals(feature.getAttr_key())) {
                govSubsidyList.add(new OriginalField(OrderGovSubsidyEnum.GOV_ZHAOSHANGPI.getCode(), feature.getAttr_value()));
                continue;
            }

            // 分销商品国补SN码校验信息（商品级：_F_zfbt_sn）
            if (OrderGovSubsidyEnum._F_ZFBT_SN.getCode().equals(feature.getAttr_key()) && StringUtils.isNotBlank(feature.getAttr_value())) {

                // 构建国补序列号校验规则描述
                String govSnCheckStr = buildGovSnCheckStr(feature.getAttr_value());

                govSubsidyList.add(new OriginalField(OrderGovSubsidyEnum.GOV_SN_CHECK.getCode(), govSnCheckStr));
            }

        }

        // 分销商品国补SN校验商品级参数：_F_zfbt_sn 为空取订单级：_F_zfbt_sn
        if (govSubsidyList.stream().noneMatch(originalField -> OrderGovSubsidyEnum.GOV_SN_CHECK.getCode().equals(originalField.getKey()))) {

            // 取订单级：_F_zfbt_sn
            String fxOrderGovSnCheck = getFeatureValueByKey(tbOrder.getFeatures(), OrderGovSubsidyEnum._F_ZFBT_SN.getCode());

            if (StringUtils.isNotBlank(fxOrderGovSnCheck)) {

                // 使用订单级参数构建国补序列号校验规则描述
                String govSnCheckStr = buildGovSnCheckStr(fxOrderGovSnCheck);

                govSubsidyList.add(new OriginalField(OrderGovSubsidyEnum.GOV_SN_CHECK.getCode(), govSnCheckStr));
            }
        }

        return govSubsidyList;
    }

    /**
     * 构建国补序列号校验规则描述
     *
     * @param govSnCheck 国补序列号校验值
     * @return 描述
     */
    private static String buildGovSnCheckStr(String govSnCheck) {

        // 国补序列号校验值为空
        if (StringUtils.isBlank(govSnCheck)) {
            return OmsApiConstant.EMPTY_STR;
        }

        // 商品国补SN码校验信息按照_分割取出信息
        String[] govSnCheckArray = govSnCheck.split("_");

        // 拼接递交给OMS的商品国补SN码校验信息
        return String.format("%s;sn填%s个,imei填%s个,%s", govSnCheck, govSnCheckArray[0], govSnCheckArray[1],
                OmsApiConstant.CHAR_1.equals(govSnCheckArray[2]) ? "校验" : "不校验");
    }

    //endregion

    //region 私有方法

    //region 去除淘宝订单详细地址里包含镇/街道的部分

    /**
     * 去除淘宝订单详细地址里包含镇/街道的部分
     *
     * @param address 详细地址
     * @param town    镇/街道
     * @return 处理后的淘宝订单详细地址
     */
    private static String removeTownFromAddress(String address, String town) {
        //若均为空则直接返回详细地址
        if (ExtUtils.isNullOrEmpty(address) || ExtUtils.isNullOrEmpty(town)) {
            return address;
        }

        //若街道字符串不在详细地址开头则直接返回详细地址
        if (address.indexOf(town) != 0) {
            return address;
        }

        try {
            //详细地址里去除街道，返回
            return address.replace(town, "");
        } catch (Exception e) {
            LogUtils.writeError(ApplicationTypeEnum.WORKER, "原字符串:address" + address + ";town:" + town + "；淘宝订单详细地址替换镇/街道时异常：" + CoreUtils.exceptionToString(e));
        }

        return address;
    }

    //endregion

    //region 获取发货方式

    /**
     * 获取发货方式
     *
     * @param trade_attr    交易属性
     * @param shipping_type 发货类型
     * @return 发货方式
     */
    private static String getSendStyle(String trade_attr, String shipping_type) {
        String sndStyle = "";
        if (!ExtUtils.isNullOrEmpty(shipping_type)) {
            sndStyle = mapShippingType.getOrDefault(shipping_type, shipping_type);
        }

        if (ExtUtils.isNullOrEmpty(trade_attr)) {
            return sndStyle;
        }

        TBOtherTradeAttr trade = JsonUtils.deJson(trade_attr, TBOtherTradeAttr.class);
        if (trade == null || !ExtUtils.toBoolean(trade.getTmallDelivery())) {
            return sndStyle;
        }

        if (ExtUtils.toBoolean(trade.getTmallDelivery())) {
            if (ExtUtils.isNullOrEmpty(trade.getDeliveryCps())) {
                return sndStyle;
            }
            return trade.getDeliveryCps().split(",")[0];
        }
        return sndStyle;
    }

    //endregion

    //region 获取税号

    /**
     * 获取税号
     *
     * @param tradeAttr 订单扩展信息
     * @return 税号
     */
    private static String getTaxNO(Map<String, Object> tradeAttr) {
        String ret = "";
        if (tradeAttr != null && tradeAttr.containsKey("buyerTaxNO")) {
            ret = ExtUtils.getNonEmpty(tradeAttr.get("buyerTaxNO"), ret).toString();
        }
        return ret;
    }
    //endregion

    //region 是否小时达订单

    /**
     * 是否小时达2.0订单
     *
     * @param tradeAttr 订单扩展信息
     * @return 税号
     */
    private static Boolean isXsdOrder(Map<String, Object> tradeAttr) {
        return tradeAttr != null && tradeAttr.containsKey(SaveOrderConstant.XSD_FIELD_NAME) && SaveOrderConstant.XSD_FLAG_VALUE.equals(tradeAttr.get(SaveOrderConstant.XSD_FIELD_NAME));
    }

    /**
     * 是否包含政府补贴
     *
     * @param tbOrder
     * @return
     */
    private static Boolean isZhenFuBuTie(TBNormalOrderEntity.Trade tbOrder) {

        // 订单级判断(useGovSubsidy = 1)
        return StringUtils.isNotBlank(tbOrder.getUseGovSubsidy()) && ExtUtils.toBoolean(tbOrder.getUseGovSubsidy());
    }

    //endregion

    //region 获取平台分摊优惠金额

    /**
     * 获取平台分摊优惠金额
     *
     * @param tbOrder order订单详情
     * @return 平台分摊优惠金额
     */
    private static BigDecimal getPlatDiscountMoney(TBNormalOrderEntity.Trade tbOrder) {
        BigDecimal divideValue = BigDecimal.valueOf(100);
        BigDecimal alipayPoint = ExtUtils.toDecimalNotNull(tbOrder.getAlipay_point()).divide(divideValue, 2, RoundingMode.HALF_UP);
        BigDecimal platformSubsidyFee = ExtUtils.toDecimalNotNull(tbOrder.getPlatform_subsidy_fee());
        BigDecimal couponFee = ExtUtils.toDecimalNotNull(tbOrder.getCoupon_fee()).divide(divideValue, 2, RoundingMode.HALF_UP);
        BigDecimal tMallCouponFee = ExtUtils.toDecimalNotNull(tbOrder.getTmall_coupon_fee()).divide(divideValue, 2, RoundingMode.HALF_UP);

        // 没有权益金且tMallCouponFee有值时
        if (BigDecimal.ZERO.compareTo(tMallCouponFee) < 0 && BigDecimal.ZERO.compareTo(ExtUtils.toDecimalNotNull(tbOrder.getExpand_card_expand_price_used())) >= 0) {
            BigDecimal platDiscountMoney = alipayPoint.add(tMallCouponFee);
            // platformSubsidyFee和tMallCouponFee相同时只加一个，不同时加2个
            if (platformSubsidyFee.multiply(divideValue).compareTo(tMallCouponFee) <= 0) {
                return platDiscountMoney;
            }
            return platDiscountMoney.add(platformSubsidyFee);
        }
        // 其次取 couponFee + platformSubsidyFee
        return alipayPoint.add(couponFee.add(platformSubsidyFee));
    }

    //endregion

    //region 是否海外直邮

    /**
     * 获取是否海外直邮
     *
     * @param order_attr 订单特性
     * @return 是否海外直邮
     */
    private static boolean convertIsHWG(String order_attr) {
        String gbzy = "";
        if (!ExtUtils.isNullOrEmpty(order_attr)) {
            Map<String, Object> dicResult = JsonUtils.deJson(order_attr, new TypeReference<Map<String, Object>>() {
            });
            if (dicResult != null && dicResult.containsKey("gbzy")) {
                gbzy = dicResult.get("gbzy").toString();
            }
        }
        return ExtUtils.toBoolean(gbzy);
    }

    //endregion

    //region 获取所有的优惠金额

    /**
     * 获取所有的优惠金额
     *
     * @param tbOrder 订单实体
     * @return 所有的优惠金额
     */
    private static List<BusinessGetOrderResponseOrderItemCouponDetailInfo> getAllCouponAmount(TBNormalOrderEntity.Trade tbOrder) {
        //订单优惠信息
        List<BusinessGetOrderResponseOrderItemCouponDetailInfo> couponDetails = new ArrayList<>();

        //天猫积分、天猫购物券优惠：不为null且大于0，则除100添加优惠
        if (tbOrder.getPoint_fee() != null && BigDecimal.valueOf(0).compareTo(tbOrder.getPoint_fee()) < 0) {
            BusinessGetOrderResponseOrderItemCouponDetailInfo couponDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();
            couponDetailInfo.setPrice(tbOrder.getPoint_fee().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).toString());
            couponDetailInfo.setType("天猫积分、天猫购物券");
            couponDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_POINT);
            couponDetailInfo.setCouponNum(1);
            couponDetails.add(couponDetailInfo);
        }

        //天猫集分宝：不为null且大于0，则除100添加优惠
        if (tbOrder.getAlipay_point() != null && BigDecimal.valueOf(0).compareTo(tbOrder.getAlipay_point()) < 0) {
            BusinessGetOrderResponseOrderItemCouponDetailInfo couponDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();
            couponDetailInfo.setPrice(ExtUtils.toDecimalNotNull(tbOrder.getAlipay_point()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).toString());
            couponDetailInfo.setType("天猫集分宝");
            couponDetailInfo.setCouponSource(BusinessGetOrderResponseOrderItem_OrderCouponSourceEnum.JH_PLAT.toString());
            couponDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_TMJFB);
            couponDetailInfo.setCouponNum(1);
            couponDetails.add(couponDetailInfo);
        }

        //红包
        if (tbOrder.getCoupon_fee() != null && BigDecimal.valueOf(0).compareTo(tbOrder.getCoupon_fee()) < 0) {
            BusinessGetOrderResponseOrderItemCouponDetailInfo couponDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();
            couponDetailInfo.setPrice(ExtUtils.toDecimalNotNull(tbOrder.getCoupon_fee()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).toString());
            couponDetailInfo.setType("红包");
            couponDetailInfo.setCouponSource(BusinessGetOrderResponseOrderItem_OrderCouponSourceEnum.JH_PLAT.toString());
            couponDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_REDCOUPON);
            couponDetailInfo.setCouponNum(1);
            couponDetails.add(couponDetailInfo);
        }

        //平台购物券
        if (tbOrder.getPlatform_subsidy_fee() != null && BigDecimal.valueOf(0).compareTo(tbOrder.getPlatform_subsidy_fee()) < 0) {
            BusinessGetOrderResponseOrderItemCouponDetailInfo couponDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();
            couponDetailInfo.setPrice(ExtUtils.toDecimalNotNull(tbOrder.getPlatform_subsidy_fee()).toString());
            couponDetailInfo.setType("平台购物券");
            couponDetailInfo.setCouponSource(BusinessGetOrderResponseOrderItem_OrderCouponSourceEnum.JH_PLAT.toString());
            couponDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_PLATCOUPON);
            couponDetailInfo.setCouponNum(1);
            couponDetails.add(couponDetailInfo);
        }

        //淘宝促销信息处理：不为null
        if (tbOrder.getPromotion_details() != null && tbOrder.getPromotion_details().getPromotion_detail() != null) {
            for (TBNormalOrderEntity.Promotion_Detail item : tbOrder.getPromotion_details().getPromotion_detail()) {
                //买就送
                boolean isGift = !ExtUtils.isNullOrEmpty(item.getGift_item_name());
                if (isGift) {
                    //淘宝赠品满送
                    BusinessGetOrderResponseOrderItemCouponDetailInfo couponDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();
                    couponDetailInfo.setPrice(item.getDiscount_fee());
                    couponDetailInfo.setSku_id(item.getPromotion_name());
                    couponDetailInfo.setType(item.getGift_item_name());
                    couponDetailInfo.setCouponNum(ExtUtils.isNullOrEmpty(item.getGift_item_num()) ? 0 : Integer.parseInt(item.getGift_item_num()));
                    couponDetailInfo.setCouponSource(BusinessGetOrderResponseOrderItem_OrderCouponSourceEnum.JH_TAOBAO.toString());
                    couponDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_FREECOUPON);
                    couponDetails.add(couponDetailInfo);
                } else {
                    //淘宝非赠品
                    BusinessGetOrderResponseOrderItemCouponDetailInfo couponDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();
                    couponDetailInfo.setPrice(item.getDiscount_fee());
                    couponDetailInfo.setSku_id(item.getPromotion_name());
                    couponDetailInfo.setType(item.getPromotion_desc());
                    couponDetailInfo.setCouponNum(1);
                    couponDetailInfo.setCouponSource(BusinessGetOrderResponseOrderItem_OrderCouponSourceEnum.JH_TAOBAO.toString());
                    couponDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_OTHER);
                    couponDetails.add(couponDetailInfo);
                }
            }
        }

        //tbOrder 的 卖家手工调整金额adjustfee 不为null且大于0
        if (tbOrder.getAdjust_fee() != null && BigDecimal.valueOf(0).compareTo(ExtUtils.toDecimalNotNull(tbOrder.getAdjust_fee())) != 0) {
            BusinessGetOrderResponseOrderItemCouponDetailInfo couponDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();
            couponDetailInfo.setPrice(tbOrder.getAdjust_fee());
            couponDetailInfo.setType("卖家手工调整金额");
            couponDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_PEOPLECOUPON);
            couponDetails.add(couponDetailInfo);
        } else {

            //tbOrder 下子订单 的 卖家手工调整金额adjustfee 不为null且大于0
            if (tbOrder.getOrders() != null && tbOrder.getOrders().getOrder() != null && tbOrder.getOrders().getOrder().length > 0) {
                for (TBNormalOrderEntity.Order item : tbOrder.getOrders().getOrder()) {
                    //值大于0
                    if (item.getAdjust_fee() != null && BigDecimal.valueOf(0).compareTo(ExtUtils.toDecimalNotNull(item.getAdjust_fee())) != 0) {
                        BusinessGetOrderResponseOrderItemCouponDetailInfo couponDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();
                        couponDetailInfo.setPrice(item.getAdjust_fee());
                        couponDetailInfo.setType("卖家手工调整金额");
                        couponDetailInfo.setSku_id(item.getNum_iid() + "");
                        couponDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_PEOPLECOUPON);
                        couponDetails.add(couponDetailInfo);
                    }
                }
            }
        }

        // 天猫红包金额处理
        if (null != tbOrder.getTmall_coupon_fee() && BigDecimal.ZERO.compareTo(tbOrder.getTmall_coupon_fee()) < 0) {
            BusinessGetOrderResponseOrderItemCouponDetailInfo couponDetailInfo = new BusinessGetOrderResponseOrderItemCouponDetailInfo();
            couponDetailInfo.setPrice(ExtUtils.toDecimalNotNull(tbOrder.getTmall_coupon_fee()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).toString());
            couponDetailInfo.setType("天猫红包");
            couponDetailInfo.setCouponSource(BusinessGetOrderResponseOrderItem_OrderCouponSourceEnum.JH_PLAT.toString());
            couponDetailInfo.setCouponType(BusinessGetOrderResponseOrderItem_OrderCouponTypeEnum.JH_TMallRedCoupon);
            couponDetailInfo.setCouponNum(1);
            couponDetails.add(couponDetailInfo);
        }

        return couponDetails;
    }

    //endregion

    //region 获取商品集合

    /**
     * 商品集合循环的处理
     *
     * @param tbOrder 订单实体
     * @return 商品集合
     */
    private static List<BusinessGetOrderResponseOrderItemGoodInfo> getAllGoods(TBNormalOrderEntity.Trade tbOrder) {
        if (tbOrder.getOrders() == null || tbOrder.getOrders().getOrder() == null || tbOrder.getOrders().getOrder().length == 0) {
            return null;
        }
        List<BusinessGetOrderResponseOrderItemGoodInfo> dataGoods = new ArrayList<>();
        for (TBNormalOrderEntity.Order good : tbOrder.getOrders().getOrder()) {
            //过滤小时达配送费商品
            if (Boolean.TRUE.equals(filterGoodsInfoByXiaoShiDa(tbOrder, good))) {
                continue;
            }
            BusinessGetOrderResponseOrderItemGoodInfo goodInfo = new BusinessGetOrderResponseOrderItemGoodInfo();
            // 商品特殊退款类型
            goodInfo.setRefundTypeStr(good.getSpecial_refund_type());
            goodInfo.setProductId(good.getNum_iid() + "");
            goodInfo.setPlatGoodsId(good.getNum_iid() + "");
            goodInfo.setSubOrderNo(good.getOid() + "");
            goodInfo.setTradeGoodsNo(getTradeGoodsNo(good.getOuter_iid(), good.getOuter_sku_id()));
            goodInfo.setOutSkuId(good.getOuter_sku_id());
            goodInfo.setOutId(good.getOuter_iid());
            goodInfo.setOutItemId(good.getOuter_iid());
            goodInfo.setPlatSkuId(good.getSku_id());
            goodInfo.setTradeGoodsName(good.getTitle());
            goodInfo.setTradeGoodsSpec(good.getSku_properties_name());
            goodInfo.setGoodsCount(BigDecimal.valueOf(good.getNum()));
            goodInfo.setPrice(ExtUtils.toDecimal(good.getPrice()));
            goodInfo.setStoreCode(getStoreCode(tbOrder, good));
            goodInfo.setDiscountMoney(ExtUtils.toDecimalNotNull(good.getDiscount_fee()).subtract(ExtUtils.toDecimalNotNull(good.getAdjust_fee())));
            goodInfo.setGoodsBuyerPayment(ExtUtils.toDecimalNotNull(good.getPayment()));
            goodInfo.setIsGift(Boolean.TRUE.equals(good.getIs_free_gift()));
            // 鉴定编号
            goodInfo.setIdentificationNumber(good.getJewcc_no());
            // 商品类目
            goodInfo.setClassId(String.valueOf(good.getCid()));
            goodInfo.setTotalFee(ExtUtils.toDecimalNotNull(good.getDivide_order_fee()));
            //分摊金额
            goodInfo.setPartMjzDiscount(ExtUtils.toDecimal(good.getPart_mjz_discount()));
            // 权益金优惠分摊金额
            goodInfo.setSubOrderExpandUsedAmount(ExtUtils.toDecimalNotNull(good.getExpand_card_expand_price_used_suborder()));
            goodInfo.setTaxAmount(ExtUtils.toDecimal(good.getSub_order_tax_fee()));
            goodInfo.setIsDaiXiaoGoods(good.isIs_daixiao());
            goodInfo.setGoodsOrderAttr(good.getOrder_attr());
            goodInfo.setGoodType(good.isIs_daixiao() ? Business_GoodTypeEnum.JH_04 : Business_GoodTypeEnum.JH_01);
            //预售订单时，备注信息填预计发货时间 XQ202501020087 【工单】淘宝平台商品级备注去掉发货时间的备注
            //goodInfo.setRemark(ExtUtils.isNullOrEmpty(good.getEstimate_con_time()) ? "" : "预计发货时间：" + good.getEstimate_con_time());
            // 处理预计发货时间
            goodInfo.setSendDeadLine(dealGoodsSendDeadLine(tbOrder, good));
            goodInfo.setRefundStatus(mapRefundStatus.getOrDefault(good.getRefund_status(), Business_RefundStatuEnum.JH_07));
            goodInfo.setStatus(mapSubOrderStatus.get(good.getStatus()));
            //特殊处理，有些客户即使是PAID_FORBID_CONSIGN 子订单，且是待发货的状态的也需要发货
            if (YunConfigUtils.contains(tbOrder.getSeller_nick(), ConfigKeyEnum.BUSINESS_DOWNLOADORDERRDS_PAID_FORBID_CONSIGN_IGNORE) && "PAID_FORBID_CONSIGN"
                    .equalsIgnoreCase(good.getStatus()) && "WAIT_SELLER_SEND_GOODS".equalsIgnoreCase(tbOrder.getStatus())) {
                goodInfo.setStatus(BusinessGetOrderResponseOrderItem_SubOrderStatuEnum.JH_02);
            }
            goodInfo.setCustomization(good.getCustomization());
            goodInfo.setPictureUrl(good.getPic_path());

            // 天猫优仓
            // 是否自流转货品
            goodInfo.setIsPlatStorageOrder(OmsApiConstant.BOOLEAN_YES_STR.equals(good.getAuto_flow()));
            goodInfo.setLatestCollectTime(good.getPromise_collect_time());

            //前n有礼 商品设置活动id，活动商品数量
            dealFirstNHaveAGiftOfGoods(good, goodInfo, tbOrder);
            //
            //            //自定义处理
            //            sbCustom.append(getCustomizationContent(good.getCustomization()));

            // XQ202302001035设置商品级EstConTime到货品备注 XQ202501020087 【工单】淘宝平台商品级备注去掉发货时间的备注
            //setEstConTimeToRemark(goodInfo, good);

            // 组合品信息处理
            if (null != good.getCombineItemInfo() && CollectionsUtil.isNotBlank(good.getCombineItemInfo().getCombineSubItem())) {
                goodInfo.setSubGoods(handleCombineItemInfo(good.getCombineItemInfo().getCombineSubItem()));
            }
            goodInfo.setSnCodeTag(good.getBybt_sn_code_tag());

            // 花呗分期购
            dealInstallmentGoods(goodInfo, good);

            // 商品国补发票信息
            goodInfo.addPlatOrderOriginalField(RDSTBOrderUtils.convertGoodsGovSubsidy(good));
            // 商品定制信息
            goodInfo.addPlatOrderOriginalField(RDSTBOrderUtils.convertGoodsCustomization(good));

            // 设置淘宝普通订单商品平台标记
            goodInfo.setGoodsTags(convertNormalGoodsTags(good));

            //百补半托管商品信息处理
            if (isBaiBuBanTuoGuan(tbOrder)) {
                dealGoodsBaiBuBanTuoGuan(good, goodInfo);
            }
            dataGoods.add(goodInfo);
        }
        return dataGoods;
    }

    //处理百补半托管商品信息
    private static void dealGoodsBaiBuBanTuoGuan(TBNormalOrderEntity.Order good, BusinessGetOrderResponseOrderItemGoodInfo goodInfo) {
        if (StringUtil.isEmpty(good.getYpdsOrderSupplyPrice())) {
            return;
        }
        BigDecimal supplyPrice = new BigDecimal(good.getYpdsOrderSupplyPrice());
        if ((supplyPrice.compareTo(BigDecimal.ZERO) <= 0)) {
            return;
        }
        //设置商品单价和总价
        goodInfo.setPrice(supplyPrice.divide(goodInfo.getGoodsCount(), 2, RoundingMode.HALF_UP));
        goodInfo.setTotalFee(supplyPrice);
    }

    /**
     * 转换淘宝普通商品平台标记列表
     *
     * @param good 淘宝商品报文
     * @return 普通商品平台标记列表
     */
    private static List<BusinessGetOrderResponseOrderItemGoodInfo.GoodsTag> convertNormalGoodsTags(TBNormalOrderEntity.Order good) {

        // 淘宝商品平台标记列表
        List<BusinessGetOrderResponseOrderItemGoodInfo.GoodsTag> goodsTags = new ArrayList<>();

        // 国补SN码校验标记
        if (StringUtils.isNotBlank(good.getGov_sn_check())) {

            goodsTags.add(new BusinessGetOrderResponseOrderItemGoodInfo.GoodsTag(ApiPlatFlagEnum.JH_GOV_SN_CHECK.getCode(), good.getGov_sn_check()));

            String[] splitArr = StringUtils.split(good.getGov_sn_check(), '_');
            if (splitArr.length == 3) {
                if (NumberUtils.toInt(splitArr[0]) > 0) {
                    goodsTags.add(new BusinessGetOrderResponseOrderItemGoodInfo.GoodsTag(SaveOrderConstant.JH_SN, good.getGov_sn_check()));
                }
                if (NumberUtils.toInt(splitArr[1]) > 0) {
                    goodsTags.add(new BusinessGetOrderResponseOrderItemGoodInfo.GoodsTag(SaveOrderConstant.JH_IMEI, good.getGov_sn_check()));
                }
            }

        }

        return goodsTags;
    }

    /**
     * 转换淘宝分销订单商品平台标记列表
     *
     * @param tbOrder 分销订单报文
     * @param fxGoods 分销商品报文
     * @return 分销商品平台标记列表
     */
    private static List<BusinessGetOrderResponseOrderItemGoodInfo.GoodsTag> convertFXGoodsTags(TBFXOrderEntity.Purchase_Order tbOrder, TBFXOrderEntity.Sub_Purchase_Order fxGoods) {

        // 淘宝商品平台标记列表
        List<BusinessGetOrderResponseOrderItemGoodInfo.GoodsTag> goodsTags = new ArrayList<>();

        // 分销商品国补序列号校验值
        String fxGovSnCheck;

        // 分销商品级国补序列号校验值：_F_zfbt_sn
        fxGovSnCheck = getFeatureValueByKey(fxGoods.getFeatures(), OrderGovSubsidyEnum._F_ZFBT_SN.getCode());

        // 分销商品级国补序列号校验为空，取订单级：_F_zfbt_sn
        if (StringUtils.isBlank(fxGovSnCheck)) {

            // 分销订单级国补序列号校验值
            fxGovSnCheck = getFeatureValueByKey(tbOrder.getFeatures(), OrderGovSubsidyEnum._F_ZFBT_SN.getCode());
        }

        // 分销商品国补SN码校验标记
        if (StringUtils.isNotBlank(fxGovSnCheck)) {

            goodsTags.add(new BusinessGetOrderResponseOrderItemGoodInfo.GoodsTag(ApiPlatFlagEnum.JH_GOV_SN_CHECK.getCode(), fxGovSnCheck));

            String[] splitArr = StringUtils.split(fxGovSnCheck, '_');
            if (splitArr.length == 3) {
                if (NumberUtils.toInt(splitArr[0]) > 0) {
                    goodsTags.add(new BusinessGetOrderResponseOrderItemGoodInfo.GoodsTag(SaveOrderConstant.JH_SN, fxGovSnCheck));
                }
                if (NumberUtils.toInt(splitArr[1]) > 0) {
                    goodsTags.add(new BusinessGetOrderResponseOrderItemGoodInfo.GoodsTag(SaveOrderConstant.JH_IMEI, fxGovSnCheck));
                }
            }

        }

        return goodsTags;
    }

    /**
     * 根据key获取指定扩展字段的value
     *
     * @param features 扩展字段信息列表
     * @param attrKey  指定扩展字段的key
     * @return
     */
    private static String getFeatureValueByKey(TBFXOrderEntity.Features features, String attrKey) {

        // 扩展字段为空不处理
        if (StringUtils.isBlank(attrKey) || features == null || ArrayUtils.isEmpty(features.getFeature())) {
            return OmsApiConstant.EMPTY_STR;
        }

        // 获取指定key扩展字段value
        return CollectionsUtil.arrayToList(features.getFeature()).stream().filter(feature -> attrKey.equals(feature.getAttr_key())).map(TBFXOrderEntity.Feature::getAttr_value)
                .findFirst().orElse(OmsApiConstant.EMPTY_STR);
    }

    /**
     * 判断是否为小时达配送费商品，需要过滤
     *
     * @param tbOrder
     * @return
     */
    private static Boolean filterGoodsInfoByXiaoShiDa(TBNormalOrderEntity.Trade tbOrder, TBNormalOrderEntity.Order good) {
        Map<String, Object> tradeAttr = JsonUtils.deJson(tbOrder.getTrade_attr(), new TypeReference<Map<String, Object>>() {
        });
        //天猫小时达2.0业务处理
        if (Boolean.TRUE.equals(isXsdOrder(tradeAttr))) {
            if (XIAOSHIDA_DELIVERY_FEE.equals(good.getTitle())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取小时达配送费total_fee
     *
     * @param tbOrder
     * @return
     */
    private static String getXiaoShiDaTotalFee(TBNormalOrderEntity.Trade tbOrder) {
        //根据商品title识别，为“小时达配送费”的，取其total_fee
        if (tbOrder.getOrders() == null || tbOrder.getOrders().getOrder() == null || tbOrder.getOrders().getOrder().length == 0) {
            return null;
        }
        TBNormalOrderEntity.Order order = Arrays.stream(tbOrder.getOrders().getOrder()).filter(t -> XIAOSHIDA_DELIVERY_FEE.equals(t.getTitle())).findFirst().orElse(null);
        if (order != null && StringUtils.isNotBlank(order.getTotal_fee())) {
            return order.getTotal_fee();
        }
        return null;
    }

    //endregion

    // region 获取商品仓库编码

    /**
     * 获取商品仓库编码
     *
     * @param tbOrder 订单信息
     * @param good    商品信息
     * @return
     */
    private static String getStoreCode(TBNormalOrderEntity.Trade tbOrder, TBNormalOrderEntity.Order good) {
        // 商品级有直接返回
        if (StringUtils.isNotBlank(good.getStore_code())) {
            return good.getStore_code();
        }
        try {
            if (tbOrder.getService_tags() == null || tbOrder.getService_tags().getLogistics_tag() == null) {
                return "";
            }
            // 从service_tags中解析storeCode
            for (TBNormalOrderEntity.Logistics_Tag logisticsTag : tbOrder.getService_tags().getLogistics_tag()) {
                if (!logisticsTag.getOrder_id().equals(String.valueOf(good.getOid()))) {
                    continue;
                }
                if (logisticsTag.getLogistic_service_tag_list().getLogistic_service_tag() == null) {
                    continue;
                }
                for (TBNormalOrderEntity.Logistic_Service_Tag logisticServiceTag : logisticsTag.getLogistic_service_tag_list().getLogistic_service_tag()) {
                    if (!"cdc".equals(logisticServiceTag.getService_type())) {
                        continue;
                    }
                    if (StringUtils.isBlank(logisticServiceTag.getService_tag())) {
                        continue;
                    }
                    String searchString = "storeCode=";
                    int index1 = logisticServiceTag.getService_tag().indexOf(searchString);
                    if (index1 < 0) {
                        continue;
                    }
                    int index2 = logisticServiceTag.getService_tag().indexOf(";", index1 + searchString.length());
                    return logisticServiceTag.getService_tag().substring(index1 + searchString.length(), index2);
                }
            }
        } catch (Exception e) {
            BusinessLogUtils.tryCatchWrite(DomainUtils.getContextID(), e.getMessage());
        }

        return "";
    }

    // endregion

    /**
     * 处理组合品信息
     *
     * @param combineSubItemDTOList 组合品信息详情集合
     * @return 子商品信息集合
     */
    private static List<BusinessGetOrderResponseOrderItemGoodInfoSubGoodsInfo> handleCombineItemInfo(List<TradeOnlineGoods.CombineGoodsItem> combineSubItemDTOList) {
        List<BusinessGetOrderResponseOrderItemGoodInfoSubGoodsInfo> subGoodsInfoList = new ArrayList<>();
        for (TradeOnlineGoods.CombineGoodsItem combineSubItemDTO : combineSubItemDTOList) {
            BusinessGetOrderResponseOrderItemGoodInfoSubGoodsInfo subGoodsInfo = new BusinessGetOrderResponseOrderItemGoodInfoSubGoodsInfo();
            subGoodsInfo.setProductId(combineSubItemDTO.getItemId().toString());
            subGoodsInfo.setTradeGoodsNo(combineSubItemDTO.getOuterIid());
            subGoodsInfo.setTradeGoodsName(combineSubItemDTO.getItemName());
            subGoodsInfo.setTradeGoodsSpec(combineSubItemDTO.getSkuTitle());
            subGoodsInfo.setGoodsCount(combineSubItemDTO.getQuantity());
            subGoodsInfo.setPrice(combineSubItemDTO.getOriginFee());
            subGoodsInfo.setOutSkuId(combineSubItemDTO.getOuterIid());
            subGoodsInfo.setOutItemId(combineSubItemDTO.getOuterIid());
            subGoodsInfo.setPlatGoodsId(combineSubItemDTO.getItemId().toString());
            subGoodsInfo.setPlatSkuId(combineSubItemDTO.getSkuId().toString());
            subGoodsInfoList.add(subGoodsInfo);
        }
        return subGoodsInfoList;
    }

    // region 处理商品级承诺发货时间

    /**
     * 处理商品级承诺发货时间
     *
     * @param tbOrder 淘宝原始订单报文
     * @return 剩余发货时间
     */
    private static LocalDateTime dealGoodsSendDeadLine(TBNormalOrderEntity.Trade tbOrder, TBNormalOrderEntity.Order tbGoods) {

        // 解析商品级的consign_due_time作为商品级lastSendDate
        LocalDateTime sendDeadLine = parseGoodsConsignDueTime(tbOrder, tbGoods);
        if (sendDeadLine != null) {
            return sendDeadLine;
        }

        // 解析商品级的estimate_con_time作为sendDeadLine
        sendDeadLine = parseGoodsEstimate_con_time(tbOrder, tbGoods);
        if (sendDeadLine != null) {
            return sendDeadLine;
        }

        // 解析商品级的order_attr里的estConTime作为sendDeadLine
        sendDeadLine = parseEstConTime(tbOrder, tbGoods);
        if (sendDeadLine != null) {
            return sendDeadLine;
        }
        // 解析订单级级的logistics_tag里的consignDate作为sendDeadLine
        sendDeadLine = parseLogisticsTag(tbOrder, tbGoods);

        return sendDeadLine;
    }

    /**
     * 处理商品级承诺发货时间
     *
     * @param tbOrder 淘宝原始订单报文
     * @return 剩余发货时间
     */
    private static LocalDateTime parseLogisticsTag(TBNormalOrderEntity.Trade tbOrder, TBNormalOrderEntity.Order tbGoods) {
        if (tbOrder.getService_tags() == null
                || tbOrder.getService_tags().getLogistics_tag() == null || tbOrder.getService_tags().getLogistics_tag().length == 0) {
            return null;
        }
        TBNormalOrderEntity.Logistics_Tag[] logisticsTagArr = tbOrder.getService_tags().getLogistics_tag();
        for (TBNormalOrderEntity.Logistics_Tag logisticsTag : logisticsTagArr) {
            if (!String.valueOf(tbGoods.getOid()).equals(logisticsTag.getOrder_id()) || logisticsTag.getLogistic_service_tag_list() == null || logisticsTag.getLogistic_service_tag_list().getLogistic_service_tag() == null
                    || logisticsTag.getLogistic_service_tag_list().getLogistic_service_tag().length == 0) {
                continue;
            }

            // 服务标签处理
            LocalDateTime consignLastSendDate = dealLogisticServiceTag(logisticsTag, tbOrder.getPay_time());
            if (consignLastSendDate != null) {
                return consignLastSendDate;
            }
        }

        return null;
    }

    /**
     * 解析商品级的consign_due_time作为商品级lastSendDate
     *
     * @param tbOrder     淘宝原始订单报文
     * @param tbGoodsInfo 淘宝原始商品报文
     * @return 承诺发货时间
     */
    private static LocalDateTime parseGoodsConsignDueTime(TBNormalOrderEntity.Trade tbOrder, TBNormalOrderEntity.Order tbGoodsInfo) {
        if (StringUtil.isEmpty(tbGoodsInfo.getConsignDueTime())) {
            return null;
        }

        String[] strArr = tbGoodsInfo.getConsignDueTime().split("_");
        if (strArr.length != 2) {
            return null;
        }

        LocalDateTime dateTime = null;

        try {

            // "_"分割后，如果前面是1，取后面的时间，时分秒用 23:59:59，例如：1_2024-11-27承诺发货时间为：2024-11-27 23:59:59
            if (OmsApiConstant.CHAR_1.equals(strArr[0])) {
                dateTime = DateTimeUtils.shortDateStringToTimeV2(strArr[1]);
            }

            // 如果前面是2，用付款时间pay_time加上后面的时间天数。例如：2_2 且付款时间为2025-01-20 11:17:44,承诺发货时间为：2025-01-22 11:17:44
            if (OmsApiConstant.CHAR_2.equals(strArr[0])) {
                LocalDateTime payTime = DateTimeUtils.stringToTime2(tbOrder.getPay_time());
                if (payTime != null) {
                    dateTime = payTime.plusDays(Long.parseLong(strArr[1]));
                }
            }
        } catch (Exception e) {
            LogAdapter.writeSystemLog("解析承诺发货时间", e);
        }

        return dateTime;
    }

    /**
     * 解析商品级的estimate_con_time作为商品级lastSendDate
     *
     * @param tbOrder     淘宝原始订单报文
     * @param tbGoodsInfo 淘宝原始商品报文
     * @return
     */
    private static LocalDateTime parseGoodsEstimate_con_time(TBNormalOrderEntity.Trade tbOrder, TBNormalOrderEntity.Order tbGoodsInfo) {
        if (StringUtil.isEmpty(tbGoodsInfo.getEstimate_con_time())) {
            return null;
        }

        // 解析格式为 “2022年03月31日24点前“ 的逻辑
        if (tbGoodsInfo.getEstimate_con_time().contains("年") && tbGoodsInfo.getEstimate_con_time().contains("月") && tbGoodsInfo.getEstimate_con_time().contains("日")) {
            String dateTimeStr = tbGoodsInfo.getEstimate_con_time().substring(0, tbGoodsInfo.getEstimate_con_time().length() - 1);
            if (!RegexUtils.isDateTime(dateTimeStr)) {
                return null;
            }
            LocalDateTime dateTime = LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern("yyyy年MM月dd日HH点"));
            // LocalDateTime解析的时候24点会自动往前进一天，所以这里特殊处理一下，24点的减去1秒
            if (dateTimeStr.contains("24点")) {
                return dateTime.plusSeconds(-1);
            }
        }

        // 解析格式为 “付款后5天内“ 的逻辑
        // 支付时间
        LocalDateTime payTime = DateTimeUtils.stringToTime(tbOrder.getPay_time());
        if (payTime == null) {
            return null;
        }

        if (StringUtil.isEmpty(tbGoodsInfo.getEstimate_con_time())) {
            return null;
        }

        // 获取指定天数
        long num = Long.parseLong(RegexUtils.findNumber(tbGoodsInfo.getEstimate_con_time()));
        // 如果是以天为单位，则换算成小时
        if (StringUtil.isNotEmpty(tbGoodsInfo.getEstimate_con_time()) && tbGoodsInfo.getEstimate_con_time().contains("天")) {
            num = num * 24;
        }

        return payTime.plusHours(num);
    }

    /**
     * 解析商品级的order_attr里的estConTime作为sendDeadLine
     *
     * @param tbOrder 淘宝原始订单报文
     * @param tbGoods 淘宝原始商品报文
     * @return
     */
    private static LocalDateTime parseEstConTime(TBNormalOrderEntity.Trade tbOrder, TBNormalOrderEntity.Order tbGoods) {
        // 校验商品级信息
        if (StringUtil.isEmpty(tbGoods.getOrder_attr())) {
            return null;
        }
        Map<String, Object> dicResult = JsonUtils.deJson(tbGoods.getOrder_attr(), new TypeReference<Map<String, Object>>() {
        });

        if (dicResult == null || !dicResult.containsKey("estConTime")) {
            return null;
        }
        String str = dicResult.get("estConTime").toString();
        if (StringUtils.isEmpty(str)) {
            return null;
        }
        String[] strArr = str.split("_");
        if (strArr.length != 2) {
            return null;
        }

        //"order_attr":"{"estConTime":"1_2023-02-05"}"。 1_2023-02-05 含义解读 1 代表绝对发货时间，2023-02-05发货最晚时间
        //"order_attr": "{"estConTime":"2_7"}"。2_7  含义解读：2 代表相对发货时间，7代表付款后7天内发货。
        //"_"分割后，如果前面是1，承诺发货时间用后面的日期+ 23:59:59，如 2023-02-05 23:59:59。如果前面是2，承诺发货时间 用付款时间+天的时间。
        LocalDateTime dateTime = null;
        if (OmsApiConstant.CHAR_1.equals(strArr[0])) {
            dateTime = DateTimeUtils.shortDateStringToTimeV2(strArr[1]);
        }
        if (OmsApiConstant.CHAR_2.equals(strArr[0])) {
            LocalDateTime payTime = DateTimeUtils.stringToTime2(tbOrder.getPay_time());
            if (payTime != null) {
                dateTime = payTime.plusDays(Long.parseLong(strArr[1]));
            }
        }
        return dateTime;
    }

    // endregion

    //region 设置商品级EstConTime到货品备注

    /**
     * 设置商品级EstConTime到货品备注
     *
     * @param goodInfo api格式商品信息
     */
    private static void setEstConTimeToRemark(BusinessGetOrderResponseOrderItemGoodInfo goodInfo, TBNormalOrderEntity.Order good) {
        if (!ExtUtils.isNullOrEmpty(good.getOrder_attr())) {
            Map<String, Object> dicResult = JsonUtils.deJson(good.getOrder_attr(), new TypeReference<Map<String, Object>>() {
            });
            if (dicResult != null && dicResult.containsKey("estConTime")) {
                String str = dicResult.get("estConTime").toString();
                String[] strArr = str.split("_");
                if (strArr.length != 2) {
                    return;
                }
                //"order_attr":"{"estConTime":"1_2023-02-05"}"。 1_2023-02-05 含义解读 1 代表绝对发货时间，2023-02-05发货最晚时间
                //"order_attr": "{"estConTime":"2_7"}"。2_7  含义解读：2 代表相对发货时间，7代表付款后7天内发货。
                if (OmsApiConstant.CHAR_1.equals(strArr[0])) {
                    goodInfo.setRemark(goodInfo.getRemark() + String.format(";最晚发货时间：%s 23:59:59", strArr[1]));
                }
                if (OmsApiConstant.CHAR_2.equals(strArr[0])) {
                    goodInfo.setRemark(goodInfo.getRemark() + String.format(";最晚发货时间：付款后%s天内发货", strArr[1]));
                }
            }
        }
    }

    //endregion

    //region 前n有礼 商品设置活动id，活动商品数量

    /**
     * 前n有礼 商品设置活动id，活动商品数量
     *
     * @param tbGoodsInfo rds 淘宝订单商品信息
     * @param goodInfo    api格式商品信息
     * @param tbOrder     rds 淘宝订单信息
     */
    private static void dealFirstNHaveAGiftOfGoods(TBNormalOrderEntity.Order tbGoodsInfo, BusinessGetOrderResponseOrderItemGoodInfo goodInfo, TBNormalOrderEntity.Trade tbOrder) {
        if (!BusinessGetOrderResponseOrderItem_SubOrderStatuEnum.JH_02.getValue().equals(tbOrder.getStatus()) || StringUtils.isEmpty(tbGoodsInfo.getOs_activity_id())) {
            return;
        }
        goodInfo.setTaobaoActivityId(tbGoodsInfo.getOs_activity_id());
        goodInfo.setTaobaoActivityNum(tbGoodsInfo.getOs_gift_count());
    }

    //endregion

    //region 淘宝自定义实体处理

    /**
     * 淘宝自定义实体处理
     *
     * @param strCustom 淘宝自定义实体字符串
     * @return 获取淘宝自定义实体内容
     */
    private static String getCustomizationContent(String strCustom) {
        String ret = "";
        if (ExtUtils.isNullOrEmpty(strCustom)) {
            return ret;
        }
        CustomizationEntity custom = JsonUtils.deJson(strCustom, CustomizationEntity.class);
        if (custom == null || custom.getText() == null || custom.getText().length == 0) {
            return ret;
        }
        for (CustomizationEntity.Text text : custom.getText()) {
            if (!ExtUtils.isNullOrEmpty(text.getContent())) {
                ret += text.getContent() + "\r\n";
            }
        }
        return ret;
    }

    //endregion

    //region 计算TradeGoodsNO

    /**
     * 计算TradeGoodsNO
     *
     * @param outer_iid    外部iid
     * @param outer_sku_id 外部sku_id
     * @return 计算TradeGoodsNO
     */
    private static String getTradeGoodsNo(String outer_iid, String outer_sku_id) {
        String strTradeGoodsNo;
        if (ExtUtils.isNullOrEmpty(outer_sku_id)) {
            strTradeGoodsNo = outer_iid == null ? "" : outer_iid;
        } else {
            outer_iid = outer_iid == null ? "" : outer_iid;
            //子商家编码包含主商家编码时取子商家编码，否则连起来
            if (outer_sku_id.startsWith(outer_iid)) {
                strTradeGoodsNo = outer_sku_id;
            } else {

                strTradeGoodsNo = outer_iid + outer_sku_id;
            }
        }
        return strTradeGoodsNo;
    }

    //endregion

    //region 家装订单货品处理

    /**
     * 家装订单货品处理
     *
     * @param tbOrder       tbrds订单
     * @param standardOrder 标准订单
     */
    private static void jzTypeGoodsHandle(TBNormalOrderEntity.Trade tbOrder, BusinessGetOrderResponseOrderItem standardOrder) {
        if (tbOrder.getService_orders() != null && tbOrder.getService_orders().getService_order() != null) {
            List<BusinessGetOrderResponseOrderItemServiceOrderInfo> dataServiceOrder = new ArrayList<>();
            List<BusinessGetOrderResponseOrderItemGoodInfo> dataGoods = new ArrayList<>();
            for (TBNormalOrderEntity.Service_Order goodJz : tbOrder.getService_orders().getService_order()) {
                if (goodJz == null) {
                    continue;
                }
                //家装延保
                if ("extend_protect".equalsIgnoreCase(goodJz.getTmser_spu_code()) && !ExtUtils.isNullOrEmpty(goodJz.getTitle()) && goodJz.getTitle().contains("延保")) {
                    BusinessGetOrderResponseOrderItemServiceOrderInfo serviceOrderInfo = new BusinessGetOrderResponseOrderItemServiceOrderInfo();
                    serviceOrderInfo.setServiceName(goodJz.getTitle());
                    serviceOrderInfo.setServiceNum(goodJz.getNum());
                    serviceOrderInfo.setServicePrice(goodJz.getPrice());
                    serviceOrderInfo.setServiceId(goodJz.getService_id() + "");
                    serviceOrderInfo.setServiceType(goodJz.getTmser_spu_code());
                    dataServiceOrder.add(serviceOrderInfo);
                } else if (isJzType(goodJz.getTmser_spu_code())) {
                    //是家装类型
                    BusinessGetOrderResponseOrderItemGoodInfo goodInfo = new BusinessGetOrderResponseOrderItemGoodInfo();
                    goodInfo.setGoodsCount(BigDecimal.valueOf(goodJz.getNum()));
                    goodInfo.setStatus(BusinessGetOrderResponseOrderItem_SubOrderStatuEnum.JH_02);
                    goodInfo.setRefundStatus(Business_RefundStatuEnum.JH_07);
                    goodInfo.setSubOrderNo(goodJz.getOid_str());
                    goodInfo.setTradeGoodsName(goodJz.getTitle());
                    goodInfo.setTradeGoodsSpec(goodJz.getTmser_spu_code());
                    goodInfo.setPlatGoodsId(goodJz.getTitle());
                    goodInfo.setPlatSkuId(goodJz.getTmser_spu_code());
                    goodInfo.setOutItemId(goodJz.getTitle());
                    goodInfo.setOutSkuId(goodJz.getTmser_spu_code());
                    goodInfo.setPrice(goodJz.getPrice());
                    goodInfo.setTradeGoodsNo(goodJz.getTitle());
                    goodInfo.setTotalFee(StringUtils.isEmpty(goodJz.getTotal_fee()) ? new BigDecimal(0) : new BigDecimal(goodJz.getTotal_fee()));
                    dataGoods.add(goodInfo);
                }
            }
            standardOrder.setServiceOrders(dataServiceOrder);
            standardOrder.setIsJZOrder(!dataGoods.isEmpty());
            standardOrder.getGoodInfos().addAll(dataGoods);
        }
    }

    //endregion

    //region 判断是否家装订单

    /**
     * 是否是家装订单
     *
     * @param jzType 家装订单字符串
     * @return 是不是
     */
    private static boolean isJzType(String jzType) {
        List<String> jzTypes = YunConfigUtils.getInstallSpuCodes();

        for (String type : jzTypes) {
            if (type.equals(jzType)) {
                return true;
            }
        }
        return false;
    }

    //endregion

    //region BMS审单分仓结果处理

    /**
     * BMS审单分仓结果处理
     *
     * @param tbOrder order订单详情
     * @return 审单分仓结果处理
     */
    private static List<BusinessGetOrderResponseOrderItemLogisticsInfo> getBMSLogisticsInfo(TBNormalOrderEntity.Trade tbOrder) {
        //如果不为空
        if (tbOrder.getLogistics_infos() != null && tbOrder.getLogistics_infos().getLogistics_info() != null && tbOrder.getLogistics_infos().getLogistics_info().length > 0) {
            List<BusinessGetOrderResponseOrderItemLogisticsInfo> dataBMSLogistics = new ArrayList<>();
            for (TBNormalOrderEntity.Logistics_Info item : tbOrder.getLogistics_infos().getLogistics_info()) {
                BusinessGetOrderResponseOrderItemLogisticsInfo logisticsInfo = new BusinessGetOrderResponseOrderItemLogisticsInfo();
                logisticsInfo.setConsignType(item.getConsign_type());
                logisticsInfo.setItemCode(item.getItem_code());
                logisticsInfo.setItemID(item.getItem_id());
                logisticsInfo.setNeedConsignNum(item.getNeed_consign_num());
                logisticsInfo.setNumiid(item.getNum_iid());
                logisticsInfo.setSkuId(item.getSku_id());
                logisticsInfo.setStoreCode(item.getStore_code());
                logisticsInfo.setSubTradeID(item.getSub_trade_id());
                logisticsInfo.setTradeID(item.getTrade_id());
                logisticsInfo.setType(item.getType());
                logisticsInfo.setDeliveryCps(item.getDelivery_cps());
                logisticsInfo.setBizSdType(item.getBiz_sd_type());
                logisticsInfo.setBizDeliveryCode(item.getBiz_delivery_code());
                logisticsInfo.setBizStoreCode(item.getBiz_store_code());
                logisticsInfo.setSendDivisionCode((item.getSend_division_code()));
                logisticsInfo.setBizDeliveryType(item.getBiz_delivery_type());
                logisticsInfo.setBlackDeliveryCps(item.getBlack_delivery_cps());
                logisticsInfo.setWhiteDeliveryCps(item.getWhite_delivery_cps());
                dataBMSLogistics.add(logisticsInfo);
            }
            return dataBMSLogistics;
        }
        return null;
    }

    //endregion

    //endregion
    //endregion

    //region RDS售后订单实体转换

    /**
     * RDS售后订单实体集合转换
     *
     * @param dataOriginOrders 淘宝售后订单
     * @param refundStatus     如果抓退款单,需要的退款单类别
     * @return RDS标准订单
     */
    public static List<IRDSResponse> convertToOrderListForRefund(List<RDSOrderEntity> dataOriginOrders, Business_RefundStatuEnum... refundStatus) {
        List<IRDSResponse> dataResponse = new ArrayList<>();
        //解析售后订单数据
        for (RDSOrderEntity item : dataOriginOrders) {
            //为空则跳过
            if (ExtUtils.isNullOrEmpty(item.getJsonOrderData())) {
                continue;
            }

            DomainUtils.doTryCatch("[淘宝RDS抓单]解析退货退款订单json", ApplicationTypeEnum.TASK, () -> {
                //转为TB对应的订单实体
                TBRefundOrderEntity tbRefundOrder = JsonUtils.deJson(item.getJsonOrderData(), TBRefundOrderEntity.class);

                // 判断数据是否为空
                if (tbRefundOrder == null || tbRefundOrder.getRefund_get_response() == null || tbRefundOrder.getRefund_get_response().getRefund() == null) {
                    return true;
                }

                // 如果退款金额为0需要转换为换货单
                if (ExtUtils.toDecimalNotNull(tbRefundOrder.getRefund_get_response().getRefund().getRefund_fee()).doubleValue() == 0D && tbRefundOrder.getRefund_get_response().getRefund().isHas_good_return()) {
                    // 换货单
                    BusinessGetExchangeOrderResponseOrderItem orderItem = convertToOrderForExchange(tbRefundOrder, refundStatus);
                    if (orderItem == null) {
                        return true;
                    }
                    RDSExchangeOrderResponse orderResponse = new RDSExchangeOrderResponse(new ArrayList<BusinessGetExchangeOrderResponseOrderItem>() {{
                        add(orderItem);
                    }}, item.getSellerNick(), item.getModifiedTime());

                    dataResponse.add(orderResponse);
                } else {
                    // 退款单
                    BusinessGetRefundOrderResponseOrderItem orderItem = convertToOrderForRefund(tbRefundOrder, refundStatus);
                    if (orderItem == null) {
                        return true;
                    }
                    RDSRefundOrderResponse orderResponse = new RDSRefundOrderResponse(new ArrayList<BusinessGetRefundOrderResponseOrderItem>() {{
                        add(orderItem);
                    }}, item.getSellerNick(), item.getModifiedTime());

                    dataResponse.add(orderResponse);
                }


                return true;
            });
        }
        return dataResponse;
    }

    /**
     * RDS售后订单实体转换
     *
     * @param entity       淘宝售后订单
     * @param refundStatus 如果抓退款单,需要的退款单类别
     * @return RDS标准订单
     */
    public static BusinessGetRefundOrderResponseOrderItem convertToOrderForRefund(TBRefundOrderEntity entity, Business_RefundStatuEnum... refundStatus) {
        if (entity == null || entity.getRefund_get_response() == null || entity.getRefund_get_response().getRefund() == null) {
            return null;
        }
        BusinessGetRefundOrderResponseOrderItem order = new BusinessGetRefundOrderResponseOrderItem();
        TBRefundOrderEntity.Refund tbOrder = entity.getRefund_get_response().getRefund();
        Business_RefundStatuEnum refundStatuEnum = mapRefundStatus.getOrDefault(tbOrder.getStatus(), Business_RefundStatuEnum.JH_99);
        //如果本订单 的退款类型，不在需要的退款单类别中，则直接返回null
        if (refundStatus != null && !CoreUtils.arrayContains(refundStatus, refundStatuEnum)) {
            return null;
        }
        //region 订单属性转换
        order.setIsEncrypt(true);
        order.setRefundStatus(refundStatuEnum);
        order.setRefundNo(tbOrder.getRefund_id() + "");
        order.setCreateTime(DateTimeUtils.stringToTime(tbOrder.getCreated()));
        order.setUpdateTime(DateTimeUtils.stringToTime(tbOrder.getModified()));
        order.setOrderStatus(mapResponseStatus.getOrDefault(tbOrder.getOrder_status(), Business_OrderStatusEnum.JH_98));
        order.setOrderStatusDesc(tbOrder.getOrder_status());
        order.setReason(tbOrder.getReason());
        order.setPlatOrderNo(tbOrder.getTid() + "");
        order.setSubPlatOrderNo(tbOrder.getOid() + "");
        order.setHasGoodsReturn(tbOrder.isHas_good_return());
        order.setDesc(tbOrder.getDesc());
        order.setBuyerNick(tbOrder.getBuyer_nick());
        order.setSellerNick(tbOrder.getSeller_nick());
        order.setLogisticName(tbOrder.getCompany_name());
        order.setLogisticNo(tbOrder.getSid());
        order.setTotalAmount(ExtUtils.toDecimal(tbOrder.getTotal_fee()));
        order.setRefundAmount(ExtUtils.toDecimal(tbOrder.getRefund_fee()));
        order.setPayAmount(ExtUtils.toDecimal(tbOrder.getPayment()));
        order.setRefundPhase(tbOrder.getRefund_phase());
        order.setRefundShopType(RefundShopTypeEnum.TAOBAO_NORMAL.toString());
        order.setRefundVersion(tbOrder.getRefund_version() + "");
        if (null != tbOrder.getRefund_remind_timeout() && !ExtUtils.isNullOrEmptyX(tbOrder.getRefund_remind_timeout().getTimeout())) {
            order.setTimeout(DateTimeUtils.stringToTime(tbOrder.getRefund_remind_timeout().getTimeout()));
        }
        //商品
        List<BusinessGetRefundResponseRefundGoodInfo> dataGoods = new ArrayList<>();
        BusinessGetRefundResponseRefundGoodInfo goodInfo = new BusinessGetRefundResponseRefundGoodInfo();
        goodInfo.setOuterId(tbOrder.getOuter_id());
        goodInfo.setProductNum(tbOrder.getNum());
        goodInfo.setPlatProductId(tbOrder.getNum_iid() + "");
        goodInfo.setSubPlatOrderNo(tbOrder.getOid() + "");
        goodInfo.setSku(getTBSKUId(tbOrder.getSku()));
        goodInfo.setProductName(tbOrder.getTitle());
        goodInfo.setReason(tbOrder.getReason());
        goodInfo.setPrice(tbOrder.getPrice());
        dataGoods.add(goodInfo);
        order.setRefundGoods(dataGoods);
        int productNum = 0;
        for (BusinessGetRefundResponseRefundGoodInfo good : dataGoods) {
            productNum += good.getProductNum();
        }
        order.setProductNum(productNum);
        //endregion

        // attribute解析  try不影响主业务
        if (!ExtUtils.isNullOrEmptyX(tbOrder.getAttribute())) {
            try {
                // 判断是否有平台物流拦截字段
                if (tbOrder.getAttribute().contains("autoInterceptAgree:1")) {
                    if (tbOrder.getAttribute().contains("INTERCEPT_APPLY")) {
                        order.setPlatLogisticIntercept("1");
                    }
                    if (tbOrder.getAttribute().contains("INTERCEPT_SUCCESS")) {
                        order.setPlatLogisticIntercept("2");
                    }
                    if (tbOrder.getAttribute().contains("INTERCEPT_FAILED")) {
                        order.setPlatLogisticIntercept("3");
                    }
                    if (tbOrder.getAttribute().contains("INTERCEPT_SELLER_CONFIRM_SIGN")) {
                        order.setPlatLogisticIntercept("5");
                    }
                    if (tbOrder.getAttribute().contains("INTERCEPT_SELLER_REJECT_SIGN")) {
                        order.setPlatLogisticIntercept("6");
                    }
                }
                order.setTmallAttribute(new ArrayList<>(OmsApiConstant.ONE_INT));

                // 增加赔付单处理逻辑
                if (tbOrder.getAttribute().contains("disputeRequest:20")) {
                    order.getTmallAttribute().add("claim");
                }

                // 增加买贵必赔处理逻辑
                if (tbOrder.getAttribute().contains("jiacha:mgbp") || tbOrder.getAttribute().contains("special_refund_type:CASH_BACK")) {
                    order.getTmallAttribute().add("mgbp");
                }

                // 判断是否存平台介入退款
                if (tbOrder.getAttribute().contains("opRole:xiaoer")) {
                    order.getTmallAttribute().add("platInter");
                }

                // 判断是否存在积分服务费
                if (tbOrder.getAttribute().contains(";apply_subsidy_refund_fee:")) {
                    String applySidyRefundFee = tbOrder.getAttribute().split(";apply_subsidy_refund_fee:")[1].split(";")[0];
                    // 除以100，转化为单位元
                    BigDecimal subSidyRefundFee = new BigDecimal(applySidyRefundFee).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                    order.setApplySidyRefundFee(subSidyRefundFee);
                }

                // 判断是否存在小二介入修改退款金额
                if (tbOrder.getAttribute().contains(";xiaoerOriFee:")) {
                    String xiaoerOriFee = tbOrder.getAttribute().split(";xiaoerOriFee:")[1].split(";")[0];
                    // 除以100，转化为单位元
                    BigDecimal xiaoerFee = new BigDecimal(xiaoerOriFee).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                    order.setActualReturnAmount(xiaoerFee);
                }
                // 新版权益金处理
                BigDecimal tmallMcardAmount = BigDecimal.ZERO;
                try {
                    if (tbOrder.getAttribute().contains(";pInst:")) {
                        String pInstStr = tbOrder.getAttribute().split(";pInst:")[1].split(";")[0];
                        if (!ExtUtils.isNullOrEmptyX(pInstStr)) {
                            String[] splitArray = pInstStr.split("TMALL_MCARD_PZ_SP\\\\\"#3B\\{\\\\\"amount\\\\\"#3B\\\\\"");
                            if (splitArray.length > 1) {
                                for (int i = 1; i < splitArray.length; i++) {
                                    String amount = splitArray[i].split("\\\\\"")[0];
                                    if (!ExtUtils.isNullOrEmptyX(amount)) {
                                        tmallMcardAmount = tmallMcardAmount.add(new BigDecimal(amount));
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                }
                if (!ExtUtils.isNullOrZero(tmallMcardAmount)) {
                    order.setTmallMcardPzSp(tmallMcardAmount);
                    return order;
                }

                // 判断是否有权益金字段
                if (!tbOrder.getAttribute().contains("TMALL_MCARD_PZ_SP^")) {
                    return order;
                }
                // 旧版权益金处理
                String[] tmallSp = tbOrder.getAttribute().split("TMALL_MCARD_PZ_SP\\^");
                if (ExtUtils.isNullOrEmptyX(tmallSp[1])) {
                    return order;
                }
                String[] amounts = tmallSp[1].split("\\|");
                if (ExtUtils.isNullOrEmptyX(amounts[0])) {
                    return order;
                }

                // 单位分
                BigDecimal amount = new BigDecimal(amounts[0]);
                // 除以100，转化为单位元
                BigDecimal newAmount = amount.divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                order.setTmallMcardPzSp(newAmount);
            } catch (Exception ex) {
            }
        }

        return order;
    }

    /**
     * RDS售后订单实体转换
     *
     * @param entity       淘宝售后订单
     * @param refundStatus 如果抓退款单,需要的退款单类别
     * @return RDS标准订单
     */
    public static BusinessGetExchangeOrderResponseOrderItem convertToOrderForExchange(TBRefundOrderEntity entity, Business_RefundStatuEnum... refundStatus) {
        if (entity == null || entity.getRefund_get_response() == null || entity.getRefund_get_response().getRefund() == null) {
            return null;
        }
        BusinessGetExchangeOrderResponseOrderItem order = new BusinessGetExchangeOrderResponseOrderItem();
        TBRefundOrderEntity.Refund tbOrder = entity.getRefund_get_response().getRefund();
        Business_RefundStatuEnum refundStatuEnum = mapRefundStatus.getOrDefault(tbOrder.getStatus(), Business_RefundStatuEnum.JH_99);
        //如果本订单 的退款类型，不在需要的退款单类别中，则直接返回null
        if (refundStatus != null && !CoreUtils.arrayContains(refundStatus, refundStatuEnum)) {
            return null;
        }
        order.setBuyerLogisticName(tbOrder.getCompany_name());
        order.setBuyerLogisticNo(tbOrder.getSid());

        //region 订单属性转换
//        order.setHasGoodsReturn(tbOrder.isHas_good_return());
//        order.setRefundShopType(RefundShopTypeEnum.TAOBAO_NORMAL.toString());
//        order.setTotalAmount(ExtUtils.toDecimal(tbOrder.getTotal_fee()));

        order.setOrderStatus(refundEnumChangeExchaneEnum(refundStatuEnum));
        order.setIsEncrypt(true);
        order.setExchangeOrderNo(tbOrder.getRefund_id() + "");
        order.setCreateTime(DateTimeUtils.stringToTime(tbOrder.getCreated()));
        order.setUpdateTime(DateTimeUtils.stringToTime(tbOrder.getModified()));
        order.setReason(tbOrder.getReason());
        order.setPlatOrderNo(tbOrder.getTid() + "");
        order.setPlatSubOrderNo(tbOrder.getOid() + "");
        order.setDesc(tbOrder.getDesc());
        order.setRefundVersion(tbOrder.getRefund_version() + "");
        // TODO 用户信息
        order.setBuyerNick(tbOrder.getBuyer_nick());
        order.setBuyerAddress(tbOrder.getAddress());
        order.setBuyerName(null);
        order.setBuyerPhone(null);

        // TODO 地址信息
        order.setAddress(tbOrder.getAddress());
        order.setProvince(null);
        order.setCity(null);
        order.setTown(null);
        order.setArea(null);

        // TODO 商家信息
        order.setSellerNick(tbOrder.getSeller_nick());
        order.setSellerAddress(null);

        // TODO 物流信息需要确认
        order.setLogisticNo(tbOrder.getSid());
        order.setSellerLogisticName(null);
        order.setSellerLogisticNo(null);

        // TODO 退款信息
        order.setRefundAmount(ExtUtils.toDecimal(tbOrder.getRefund_fee()));
        order.setPayment(ExtUtils.toDecimal(tbOrder.getPayment()));


        //退货商品
        List<BusinessGetExchangeOrderResponseOrderItem.GoodsItem> returnGoods = new ArrayList<>();
        BusinessGetExchangeOrderResponseOrderItem.GoodsItem returnGoodInfo = new BusinessGetExchangeOrderResponseOrderItem.GoodsItem();
//        returnGoodInfo.setReason(tbOrder.getReason());
//        returnGoodInfo.setSubPlatOrderNo(tbOrder.getOid() + "");
        returnGoodInfo.setOuterId(tbOrder.getOuter_id());
        returnGoodInfo.setProductNum(tbOrder.getNum());
        returnGoodInfo.setPlatProductId(tbOrder.getNum_iid() + "");
        returnGoodInfo.setSku(getTBSKUId(tbOrder.getSku()));
        returnGoodInfo.setProductName(tbOrder.getTitle());
        returnGoodInfo.setPrice(tbOrder.getPrice());
        returnGoods.add(returnGoodInfo);
        order.setRefundGoods(returnGoods);

        return order;
    }

    /**
     * RDS售后订单实体集合转换
     *
     * @param dataOriginOrders 淘宝售后订单
     * @param refundStatus     如果抓退款单,需要的退款单类别
     * @return RDS标准订单
     */
    public static List<IRDSResponse> convertToOrderListForFxRefund(List<RDSOrderEntity> dataOriginOrders, Business_RefundStatuEnum... refundStatus) {
        List<IRDSResponse> dataResponse = new ArrayList<>();
        //解析售后订单数据
        for (RDSOrderEntity item : dataOriginOrders) {
            //为空则跳过
            if (ExtUtils.isNullOrEmpty(item.getJsonOrderData())) {
                continue;
            }

            DomainUtils.doTryCatch("[淘宝RDS抓单]解析退货退款订单json", ApplicationTypeEnum.TASK, () -> {
                //转为TB对应的订单实体
                TBFxRefundOrderEntity tbRefundOrder = JsonUtils.deJson(item.getJsonOrderData(), TBFxRefundOrderEntity.class);
                BusinessGetRefundOrderResponseOrderItem orderItem = convertToOrderForFxRefund(tbRefundOrder, refundStatus);
                if (orderItem == null) {
                    return true;
                }
                List<BusinessGetRefundOrderResponseOrderItem> orderItemList = new ArrayList<>();
                orderItemList.add(orderItem);
                RDSRefundOrderResponse orderResponse = new RDSRefundOrderResponse(orderItemList, item.getSellerNick(), item.getModifiedTime());

                dataResponse.add(orderResponse);
                return true;
            });
        }
        return dataResponse;
    }

    /**
     * RDS售后订单实体转换
     *
     * @param entity       淘宝售后订单
     * @param refundStatus 如果抓退款单,需要的退款单类别
     * @return RDS标准订单
     */
    public static BusinessGetRefundOrderResponseOrderItem convertToOrderForFxRefund(TBFxRefundOrderEntity entity, Business_RefundStatuEnum... refundStatus) {
        if (entity == null || entity.getFenxiao_refund_get_response() == null || entity.getFenxiao_refund_get_response().getRefund_detail() == null) {
            return null;
        }
        BusinessGetRefundOrderResponseOrderItem order = new BusinessGetRefundOrderResponseOrderItem();
        TBFxRefundOrderEntity.Refund_Detail tbOrder = entity.getFenxiao_refund_get_response().getRefund_detail();
        Business_RefundStatuEnum refundStatuEnum = fxRefundStatusMap.getOrDefault(tbOrder.getRefund_status(), Business_RefundStatuEnum.JH_99);
        //如果本订单 的退款类型，不在需要的退款单类别中，则直接返回null
        if (refundStatus != null && !CoreUtils.arrayContains(refundStatus, refundStatuEnum)) {
            return null;
        }
        //region 订单属性转换
        order.setIsEncrypt(false);
        order.setRefundStatus(refundStatuEnum);
        order.setRefundNo(tbOrder.getSub_order_id() + "");
        order.setCreateTime(DateTimeUtils.stringToTime(tbOrder.getRefund_create_time()));
        order.setUpdateTime(DateTimeUtils.stringToTime(tbOrder.getModified()));
        order.setReason(tbOrder.getRefund_reason());
        order.setPlatOrderNo(tbOrder.getSub_order_id() + "");
        order.setSubPlatOrderNo(tbOrder.getSub_order_id() + "");
        order.setHasGoodsReturn(tbOrder.isIs_return_goods());
        order.setDesc(tbOrder.getRefund_desc());
        order.setReason(tbOrder.getRefund_reason());
        order.setRefundAmount(ExtUtils.toDecimal(tbOrder.getRefund_fee()));
        order.setRefundShopType(RefundShopTypeEnum.TAOBAO_FX.toString());
        //endregion

        return order;
    }

    //region 私有方法

    /**
     * 提取淘宝SKUID
     *
     * @param strSku （格式：30004447689|颜色分类:军绿色;尺码:XS）
     * @return sku 如30004447689
     */
    private static String getTBSKUId(String strSku) {
        if (ExtUtils.isNullOrEmpty(strSku)) {
            return "";
        }

        return strSku.split("\\|")[0];
    }

    //endregion

    //endregion

    //endregion

    //region 删除和上次抓单重复的数据

    /**
     * 删除掉和上次抓单重复的数据
     *
     * @param shopOrders         当前订单列表
     * @param lastBoundaryOrders 上次边界ID列表
     * @param preLastOrderTime   上次边界时间
     * @return 删除的数据
     */
    public static <T extends IRDSResponse> int deleteCommonOrders(List<T> shopOrders, List<String> lastBoundaryOrders, LocalDateTime preLastOrderTime) {
        if (null == lastBoundaryOrders || lastBoundaryOrders.isEmpty()) {
            return 0;
        }

        //获取上一次抓单与截止时间相同的边界订单集合。
        List<Integer> beDeleteIndexs = new ArrayList<>();
        //从开始查找与上次抓单截止时间相同的订单,按时间升序遍历，如果不相同则步出循环。
        for (int i = 0; i < shopOrders.size(); i++) {
            if (shopOrders.get(i).getModifiedTime().isEqual(preLastOrderTime)) {
                //如果上次抓单集合包含当前ID，则加入将要删除的索引集合。
                String id = shopOrders.get(i).getUniqueID();
                if (lastBoundaryOrders.contains(id)) {
                    beDeleteIndexs.add(i);
                }
            } else {
                break;
            }
        }

        final int size = beDeleteIndexs.size();
        if (size > 0) {
            //倒序删除shopOrders 中的元素
            Collections.reverse(beDeleteIndexs);
            for (int index : beDeleteIndexs) {
                shopOrders.remove(index);
            }
        }

        return size;
    }

    //endregion

    //region 判断是否昵称重复

    /**
     * 是否是重复的昵称
     *
     * @param dataSellNickName 昵称集合
     * @param nickName         当前昵称
     * @return 是否是重复的昵称
     */
    public static boolean isRepeatNick(List<String> dataSellNickName, String nickName) {
        int index = dataSellNickName.indexOf(nickName);
        int lastIndex = dataSellNickName.lastIndexOf(nickName);
        return -1 != index && -1 != lastIndex && index != lastIndex;
    }
    //endregion

    //region 是否解密

    /**
     * 是否需要解密订单
     *
     * @return 是否需要
     */
    public static boolean isNeedDecryptOrder() {
        //如果是生产环境，必解密。否则按配置键值来判断
        if (!Boolean.TRUE.equals(LocalConfig.get().getIsTest())) {
            return true;
        }

        return "1".equals(YunConfigUtils.getDBConfig(ConfigKeyEnum.BUSINESS_DOWNLOADORDERRDS_ISDECRYPT));
    }
    //endregion

    // region 其他方法


    // reion 退款类型枚举映射为换货枚举类型

    /**
     * 退款类型枚举映射为换货枚举类型
     *
     * @param refundStatuEnum 退款枚举类型
     * @return 换货枚举类型
     */
    private static Business_ExchangeOrderStatusEnum refundEnumChangeExchaneEnum(Business_RefundStatuEnum refundStatuEnum) {

        switch (refundStatuEnum) {
            case JH_01:
                return Business_ExchangeOrderStatusEnum.JH_01;
            case JH_02:
                return Business_ExchangeOrderStatusEnum.JH_02;
            case JH_03:
                return Business_ExchangeOrderStatusEnum.JH_03;
            case JH_04:
                return Business_ExchangeOrderStatusEnum.JH_04;
            case JH_05:
                return Business_ExchangeOrderStatusEnum.JH_05;
            case JH_06:
                return Business_ExchangeOrderStatusEnum.JH_06;
            default:
                return Business_ExchangeOrderStatusEnum.JH_99;
        }
    }

    /**
     * 转换淘宝RDS订单退款信息（包括正常、经销、分销）
     *
     * @param apiCallResponseList 淘宝RDS订单退款信息列表
     * @return
     */
    public static List<List<RDSOrderBaseResponseDTO>> convertTaoBaoRDSOrderRefundInfoList(List<ApiCallResponse<PolyAPIBusinessGetOrderResponseBizData>> apiCallResponseList) {
        // 将菠萝派订单返回信息转换成淘宝RDS订单退款信息VO
        return apiCallResponseList.stream().map(RDSTBOrderUtils::convertTaoBaoRDSOrderRefundInfo).collect(Collectors.toList());
    }

    /**
     * 将菠萝派订单返回信息转换成淘宝RDS订单退款信息VO
     *
     * @param apiCallResponse 菠萝派下载订单请求返回结果
     * @return 淘宝RDS订单退款信息VO
     */
    private static List<RDSOrderBaseResponseDTO> convertTaoBaoRDSOrderRefundInfo(ApiCallResponse<PolyAPIBusinessGetOrderResponseBizData> apiCallResponse) {
        // 判空处理，取出RDS转后的订单列表信息
        final List<BusinessGetOrderResponseOrderItem> businessGetOrderResponseOrderItemList = Optional.ofNullable(apiCallResponse.getBizData())
                .map(PolyAPIBusinessGetOrderResponseBizData::getOrders).filter(org.apache.commons.collections.CollectionUtils::isNotEmpty)
                .orElse(null);
        if (CollectionUtils.isEmpty(businessGetOrderResponseOrderItemList)) {
            return Collections.emptyList();
        }
        // 转换淘宝RDS订单退款信息列表
        return businessGetOrderResponseOrderItemList.stream().map((businessGetOrderResponseOrderItem) -> {
            // 构建商品响应信息DTO
            final RDSNormalOrderRefundResponseDTO rdsNormalOrderRefundResponseVo = buildRDSNormalOrderRefundResponseDTO(apiCallResponse, businessGetOrderResponseOrderItem);
            // 获取订单商品信息
            final List<BusinessGetOrderResponseOrderItemGoodInfo> goodsList = businessGetOrderResponseOrderItem.getGoodInfos();
            if (CollectionUtils.isEmpty(goodsList)) {
                return rdsNormalOrderRefundResponseVo;
            }
            // 根据商品退款信息设置订单退款信息
            rdsNormalOrderRefundResponseVo.setRefundStatus(getOrderRefundByGoods(goodsList));
            // 设置商品退款信息
            List<RDSNormalGoodsRefundResponseDTO> rdsNormalGoodsRefundResponseDTOs = buildRDSNormalGoodsRefundResponseDTO(goodsList);
            rdsNormalOrderRefundResponseVo.setChildrenRefundStatus(rdsNormalGoodsRefundResponseDTOs);
            return rdsNormalOrderRefundResponseVo;
        }).collect(Collectors.toList());
    }

    /**
     * 构建淘宝RDS订单级退款信息
     *
     * @param apiCallResponse
     * @param businessGetOrderResponseOrderItem
     * @return
     */
    private static RDSNormalOrderRefundResponseDTO buildRDSNormalOrderRefundResponseDTO(ApiCallResponse<PolyAPIBusinessGetOrderResponseBizData> apiCallResponse, BusinessGetOrderResponseOrderItem businessGetOrderResponseOrderItem) {
        final RDSNormalOrderRefundResponseDTO rdsNormalOrderRefundResponseVo = new RDSNormalOrderRefundResponseDTO();
        // 设置请求订单响应信息
        rdsNormalOrderRefundResponseVo.setMessage(apiCallResponse.getMsg());
        rdsNormalOrderRefundResponseVo.setCode(apiCallResponse.getCode());
        rdsNormalOrderRefundResponseVo.setSubCode(apiCallResponse.getSubCode());
        rdsNormalOrderRefundResponseVo.setSubMessage(apiCallResponse.getSubMessage());
        rdsNormalOrderRefundResponseVo.setSuccess(apiCallResponse.getIsSuccess());
        // 设置订单基本信息
        rdsNormalOrderRefundResponseVo.setPlatOrderNo(businessGetOrderResponseOrderItem.getPlatOrderNo());
        rdsNormalOrderRefundResponseVo.setTradeStatus(businessGetOrderResponseOrderItem.getTradeStatus());
        rdsNormalOrderRefundResponseVo.setProvince(businessGetOrderResponseOrderItem.getProvince());
        rdsNormalOrderRefundResponseVo.setCity(businessGetOrderResponseOrderItem.getCity());
        rdsNormalOrderRefundResponseVo.setArea(businessGetOrderResponseOrderItem.getArea());
        rdsNormalOrderRefundResponseVo.setTown(businessGetOrderResponseOrderItem.getTown());
        rdsNormalOrderRefundResponseVo.setAddress(businessGetOrderResponseOrderItem.getAddress());
        rdsNormalOrderRefundResponseVo.setReceiverName(businessGetOrderResponseOrderItem.getReceiverName());
        rdsNormalOrderRefundResponseVo.setPhone(businessGetOrderResponseOrderItem.getPhone());
        rdsNormalOrderRefundResponseVo.setMobile(businessGetOrderResponseOrderItem.getMobile());
        return rdsNormalOrderRefundResponseVo;
    }

    /**
     * 构建淘宝RDS订单商品级退款信息
     *
     * @param goodsList
     * @return
     */
    private static List<RDSNormalGoodsRefundResponseDTO> buildRDSNormalGoodsRefundResponseDTO(List<BusinessGetOrderResponseOrderItemGoodInfo> goodsList) {
        List<RDSNormalGoodsRefundResponseDTO> rdsNormalGoodsRefundResponseVos = goodsList.stream().map((goodInfo) -> {
            final RDSNormalGoodsRefundResponseDTO rdsNormalGoodsRefundResponseVo = new RDSNormalGoodsRefundResponseDTO();
            // 子订单号
            rdsNormalGoodsRefundResponseVo.setSubOrderNo(goodInfo.getSubOrderNo());
            // 商品名称
            rdsNormalGoodsRefundResponseVo.setProductName(goodInfo.getTradeGoodsName());
            // 平台商品ID或skuId(skuId优先）
            rdsNormalGoodsRefundResponseVo.setPlatProductId(goodInfo.getProductId());
            // 外部商家编码或外部SKU编码(SKU编码优先)
            rdsNormalGoodsRefundResponseVo.setTradeGoodsNo(goodInfo.getTradeGoodsNo());
            // 商品退款状态
            rdsNormalGoodsRefundResponseVo.setRefundStatus(goodInfo.getRefundStatus());
            // 商品交易状态
            rdsNormalGoodsRefundResponseVo.setTradeStatus(goodInfo.getStatus());
            // 不可发货原因
            rdsNormalGoodsRefundResponseVo.setCantSendReason(goodInfo.getCantSendReason());
            // 退款数量
            rdsNormalGoodsRefundResponseVo.setRefundGoodNum(goodInfo.getRefundCount());
            // 特殊退款类型
            rdsNormalGoodsRefundResponseVo.setSpecialRefundType(goodInfo.getRefundTypeStr());
            return rdsNormalGoodsRefundResponseVo;
        }).collect(Collectors.toList());
        return rdsNormalGoodsRefundResponseVos;
    }

    /**
     * 根据商品退款状态获取订单退款状态
     *
     * @param goodsList 菠萝派订单商品列表
     * @return 订单退款状态
     */
    private static Business_RefundStatuEnum getOrderRefundByGoods(List<BusinessGetOrderResponseOrderItemGoodInfo> goodsList) {
        // 获取退款商品的数量
        final long refundCount = goodsList.stream().filter(t -> !SaveOrderConstant.NO_REFUND_GOODS_STATUS.contains(t.getRefundStatus().getEnValue())).count();
        // 没有退款
        if (0L == refundCount) {
            return Business_RefundStatuEnum.JH_07;
        }
        // 全部退款
        else if (refundCount == goodsList.size()) {
            return Business_RefundStatuEnum.JH_06;
        }
        // 部分退款
        else {
            return Business_RefundStatuEnum.JH_09;
        }
    }

    /**
     * 根据淘宝平台优惠信息构建平台标记列表
     *
     * @param tbOrder 淘宝推送库原始订单
     * @return 映射到的平台标记列表
     */
    private static List<BusinessGetOrderResponseOrderTag> promotionDetailsFlagConvert(TBNormalOrderEntity.Trade tbOrder) {
        // 补贴列表
        List<TBNormalOrderEntity.Promotion_Detail> promotionDetails = Optional.ofNullable(tbOrder.getPromotion_details())
                .map(TBNormalOrderEntity.Promotion_Details::getPromotion_detail).map(Arrays::asList).orElse(null);

        if (CollectionUtils.isEmpty(promotionDetails)) {
            return Collections.emptyList();
        }

        // 取出配置键淘宝补贴平台标记映射列表
        List<String> flagMappingStrList = Optional.ofNullable(YunConfigUtils.getDBConfig(ConfigKeyEnum.BUSINESS_TAO_BAO_PROMOTION_DETAILS_FLAG_LIST)).map(configValue -> configValue.split("\\|"))
                .map(Arrays::asList).orElse(null);

        if (CollectionUtils.isEmpty(flagMappingStrList)) {
            return Collections.emptyList();
        }

        // 映射到的平台标记列表
        List<BusinessGetOrderResponseOrderTag> orderTags = new ArrayList<>();

        // 遍历平台补贴信息映射对应平台标记code
        for (TBNormalOrderEntity.Promotion_Detail promotionDetail : promotionDetails) {

            // 取出平台优惠名称
            String promotionName = promotionDetail.getPromotion_name();
            if (StringUtils.isBlank(promotionName)) {
                continue;
            }

            // 取出匹配到当前平台优惠的关键字配置信息（例："|百补:billionsPromotion|百亿:billionsPromotion|"）只需映射一个code即可
            String mappingStr = flagMappingStrList.stream().filter(flagMappingStr -> {
                if (StringUtils.isBlank(flagMappingStr)) {
                    return false;
                }
                String[] flagMappingArr = flagMappingStr.split(":");

                // 旧逻辑兼容
                if (flagMappingArr.length == 2) {
                    return promotionName.contains(flagMappingArr[1]);

                } else if (flagMappingArr.length == 3) {
                    // 匹配规则
                    String matchSign = flagMappingArr[2];

                    // 模糊匹配
                    if (OmsApiConstant.CHAR_1.equals(matchSign)) {
                        return promotionName.contains(flagMappingArr[1]);
                    }

                    // 全匹配
                    if (OmsApiConstant.CHAR_2.equals(matchSign)) {
                        return Objects.equals(promotionName, flagMappingArr[1]);
                    }
                }

                return false;
            }).findFirst().orElse(null);

            // 构建平台标记对象
            if (StringUtils.isNotBlank(mappingStr)) {
                BusinessGetOrderResponseOrderTag orderTag = new BusinessGetOrderResponseOrderTag();
                orderTag.setKey(mappingStr.split(":")[0]);
                orderTag.setDesc(promotionName);
                orderTags.add(orderTag);
            }
        }
        //百亿补贴订单
        if (tbOrder.getOrders() != null) {
            TBNormalOrderEntity.Order[] orders = tbOrder.getOrders().getOrder();
            boolean bybtOrder = Arrays.stream(orders).anyMatch(o -> Boolean.TRUE.equals(o.getBybtOrder()));
            if (bybtOrder && orderTags.stream().noneMatch(t -> BILLION_PROMOTION_KEY.equals(t.getKey()))) {
                BusinessGetOrderResponseOrderTag orderTag = new BusinessGetOrderResponseOrderTag();
                orderTag.setKey(BILLION_PROMOTION_KEY);
                orderTag.setDesc(BILLION_PROMOTION_NAME);
                orderTags.add(orderTag);
            }
        }

        return orderTags;
    }

    /**
     * 是否为需要机构鉴定订单
     *
     * @param tbOrder 淘宝推送库原始订单
     * @return 布尔值
     */
    private static boolean isNeedIdentify(TBNormalOrderEntity.Trade tbOrder) {

        // 获取商品列表
        TBNormalOrderEntity.Order[] orders = Optional.ofNullable(tbOrder.getOrders()).map(TBNormalOrderEntity.Orders::getOrder).orElse(null);

        if (null == orders || orders.length == 0) {
            return false;
        }

        // 任一商品jewcc_no字段不为空则返回true
        return Arrays.stream(orders).anyMatch(order -> !StringUtils.isEmpty(order.getJewcc_no()));

    }

    /**
     * 获取分销订单商品退款状态
     *
     * @param tbGood 平台商品信息
     * @return 退款状态
     */
    private static Business_RefundStatuEnum getFxOrderGoodsRefundStatus(TBFXOrderEntity.Sub_Purchase_Order tbGood) {

        // 分销订单商品退款状态
        Business_RefundStatuEnum refundStatus = mapFXRefundStatus.getOrDefault(tbGood.getRefund_status(), Business_RefundStatuEnum.JH_07);

        // 分销订单商品退款状态为【正常】或者【其他】时 ，商品状态取取消费者退款状态
        if (Business_RefundStatuEnum.JH_07.equals(refundStatus) || Business_RefundStatuEnum.JH_99.equals(refundStatus)) {

            // 为空则返回原映射状态
            return mapFXConsumerRefundStatus.getOrDefault(tbGood.getOrder_200_status(), refundStatus);
        }

        // 默认取分销订单退款状态
        return refundStatus;
    }

    /**
     * 获取支付方式
     *
     * @param tbOrder
     * @return
     */
    private static BusinessGetOrderResponseOrderItem_PayTypeEnum getPayType(TBNormalOrderEntity.Trade tbOrder) {
        if ("cod".equalsIgnoreCase(tbOrder.getType()) || "b2c_cod".equalsIgnoreCase(tbOrder.getType()) || "super_market_cod_trade".equalsIgnoreCase(tbOrder.getType())) {
            return BusinessGetOrderResponseOrderItem_PayTypeEnum.JH_COD;
        }

        if ("alipay".equalsIgnoreCase(tbOrder.getPaymentMethod())) {
            return BusinessGetOrderResponseOrderItem_PayTypeEnum.JH_ALIPAY;
        }

        if ("wx".equalsIgnoreCase(tbOrder.getPaymentMethod())) {
            return BusinessGetOrderResponseOrderItem_PayTypeEnum.JH_WEIXIN;
        }

        return BusinessGetOrderResponseOrderItem_PayTypeEnum.JH_OTHER;
    }

    // endregion

    // endregion
}
