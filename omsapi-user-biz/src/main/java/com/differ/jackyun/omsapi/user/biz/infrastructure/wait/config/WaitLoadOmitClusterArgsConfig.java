package com.differ.jackyun.omsapi.user.biz.infrastructure.wait.config;

import com.differ.jackyun.framework.component.config.client.common.annotation.JKYConfigCallback;
import com.differ.jackyun.framework.component.config.client.common.annotation.JKYConfigField;
import com.differ.jackyun.framework.component.config.client.common.annotation.JKYConfigurator;

import java.util.function.BooleanSupplier;

/**
 * @Description Load下载订单排队-集群级参数业务配置
 * <AUTHOR>
 * @Date 2021/8/4 10:01
 */
@JKYConfigurator
public class WaitLoadOmitClusterArgsConfig {
    @JKYConfigField(configCode = "WaitLoadOmitCluster.maxExec", name = "执行中的任务上限，Load下载订单排队-集群级")
    public static int maxExec = 200;

    @JKYConfigCallback(configCode = "WaitLoadOmitCluster.maxExec")
    public static void maxExecCallback(int current, int newValue) {
        if (current != newValue) {
            maxExec = newValue;
            publish();
        }
    }

    @JKYConfigField(configCode = "WaitLoadOmitCluster.executingExpire", name = "执行超时时间(秒)，Load下载订单排队-集群级")
    public static int executingExpire =600;

    @JKYConfigCallback(configCode = "WaitLoadOmitCluster.executingExpire")
    public static void executingExpireCallback(int current, int newValue) {
        if (current != newValue) {
            executingExpire = newValue;
            publish();
        }
    }

    @JKYConfigField(configCode = "WaitLoadOmitCluster.waitDataExpire", name = "排队任务缓存键过期时间(秒)，Load下载订单排队-集群级")
    public static int waitDataExpire =86400;

    @JKYConfigCallback(configCode = "WaitLoadOmitCluster.waitDataExpire")
    public static void waitDataExpireCallback(int current, int newValue) {
        if (current != newValue) {
            waitDataExpire = newValue;
            publish();
        }
    }

    @JKYConfigField(configCode = "WaitLoadOmitCluster.gray", name = "灰度开关，Load下载订单排队-集群级,ALL或者用|会员|分隔的表示开启")
    public static String gray = "";

    @JKYConfigCallback(configCode = "WaitLoadOmitCluster.gray")
    public static void grayCallback(String current, String newValue) {
        if (current != newValue) {
            gray = newValue;
            publish();
        }
    }

    @JKYConfigField(configCode = "WaitLoadOmitCluster.debug", name = "调试开关，Load下载订单排队-集群级")
    public static boolean debug = false;

    @JKYConfigCallback(configCode = "WaitLoadOmitCluster.debug")
    public static void debugCallback(boolean current, boolean newValue) {
        if (current != newValue) {
            debug = newValue;
            publish();
        }
    }

    @JKYConfigField(configCode = "WaitLoadOmitCluster.insureMode", name = "保守模式开关，Load下载订单排队-集群级")
    public static boolean insureMode = false;

    @JKYConfigCallback(configCode = "WaitLoadOmitCluster.insureMode")
    public static void insureModeCallback(boolean current, boolean newValue) {
        if (current != newValue) {
            insureMode = newValue;
            publish();
        }
    }

    //region 简单事件发布订阅

    /**
     * 事件处理者
     */
    private static BooleanSupplier handler;

    /**
     * 订阅配置变更事件
     * @param eventHandler
     */
    public static void subscribe(BooleanSupplier eventHandler){
        handler = eventHandler;
    }

    /**
     * 发布配置变更事件
     */
    private static void publish(){
        if(handler != null){
            handler.getAsBoolean();
        }
    }

    //endregion

    //endregion

}