//package com.differ.jackyun.omsapi.user.biz.infrastructure.wait.handler.download.order.rds;
//
//import com.differ.jackyun.omsapi.component.redis.MultiRedis;
//import com.differ.jackyun.omsapi.component.wait.AbstractWaitEntity;
//import com.differ.jackyun.omsapi.component.wait.data.CommonWaitEntity;
//import com.differ.jackyun.omsapi.component.wait.data.WaitConfigArgs;
//import com.differ.jackyun.omsapi.component.wait.data.WaitContext;
//import com.differ.jackyun.omsapi.component.wait.impl.BaseUserWaitLineHandler;
//import com.differ.jackyun.omsapi.core.infrastructure.config.business.ConfigUtil;
//import com.differ.jackyun.omsapi.user.biz.infrastructure.cache.local.plugins.circuit.LoadOrderCircuitBreakLocalCache;
//import com.differ.jackyun.omsapi.user.biz.infrastructure.common.LogAdapter;
//import com.differ.jackyun.omsapi.user.biz.infrastructure.wait.WaitTypeEnum;
//import com.differ.jackyun.omsapi.user.biz.infrastructure.wait.config.WaitDownOrderNormalArgsConfig;
//import com.differ.jackyun.omsapi.user.biz.infrastructure.wait.executor.WaitRdsAutoDownOrderExecutor;
//import com.differ.jackyun.omsapi.user.biz.infrastructure.wait.subscriber.DefaultUserWaitNextPublisher;
//import com.differ.jackyun.omsapibase.infrastructure.utils.SpringResolveManager;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Lazy;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//
///**
// * @Description rds自动下载非正常订单的排队组件
// * <AUTHOR>
// * @Date 2021/11/26 16:27
// */
//@Component
//@Lazy
//public class WaitRdsAutoDownOrderUnNormalHandler extends BaseUserWaitLineHandler {
//
//    @Autowired
//    private MultiRedis multiRedis;
//
//    @Autowired
//    private WaitRdsAutoDownOrderExecutor executor;
//
//    @Autowired
//    private DefaultUserWaitNextPublisher subscriber;
//
//    @Autowired
//    private LoadOrderCircuitBreakLocalCache circuitBreakLocalCache;
//
//    public static WaitRdsAutoDownOrderUnNormalHandler get() {
//        return SpringResolveManager.resolve(WaitRdsAutoDownOrderUnNormalHandler.class);
//    }
//
//    @PostConstruct
//    public void init() {
//        WaitContext waitContext = new WaitContext(multiRedis, WaitTypeEnum.AUTO_DOWN_UN_NORMAL.getType(), executor, subscriber, true);
//        this.init(waitContext);
//        WaitDownOrderNormalArgsConfig.subscribe(this::onRefreshArgs);
//        this.funGetMaxExecCount = this::getMaxExecCountWithCircuitBreak;
//        this.funIsDebug = WaitDownOrderNormalArgsConfig::isConfigUserDebug;
//        onRefreshArgs();
//    }
//
//    @Override
//    public boolean putWaitOrExec(String user, AbstractWaitEntity entity) {
//        if (!ConfigUtil.isConfig(WaitDownOrderNormalArgsConfig.gray, user)) {
//            // 未开启时，直接执行任务
//            this.waitContext.getExecutor().exec(entity.getDataString());
//            return false;
//        }
//        return super.putWaitOrExec(user, entity);
//    }
//
//    /**
//     * 动态刷新配置
//     *
//     * @return
//     */
//    public Boolean onRefreshArgs() {
//        WaitConfigArgs args = this.waitContext.clone();
//        args.setWaitDataExpire(WaitDownOrderNormalArgsConfig.waitDataExpire);
//        args.setMaxExec(WaitDownOrderNormalArgsConfig.maxExec);
//        args.setExecutingExpire(WaitDownOrderNormalArgsConfig.executingExpire);
//        args.setInsureMode(WaitDownOrderNormalArgsConfig.insureMode);
//        this.refreshAllArgs(args);
//        return true;
//    }
//
//    /**
//     * 结合熔断获取会员的自动最大执行数
//     *
//     * @return
//     */
//    public Integer getMaxExecCountWithCircuitBreak(String user) {
//        if(!WaitDownOrderNormalArgsConfig.supportCircuitBreak){
//            // 不启用熔断时返回默认用户执行数
//            return WaitDownOrderNormalArgsConfig.getConfigUserMaxAutoExecCount(user);
//        }
//        try {
//            // 判断订单下载熔断
//            if(circuitBreakLocalCache.loadOrderBreak(user)) {
//                return WaitDownOrderNormalArgsConfig.circuitExec;
//            }
//        } catch (Exception ex) {
//            LogAdapter.writeSystemLog("排队自动取熔断执行异常", ex);
//        }
//        return WaitDownOrderNormalArgsConfig.getConfigUserMaxAutoExecCount(user);
//    }
//}
