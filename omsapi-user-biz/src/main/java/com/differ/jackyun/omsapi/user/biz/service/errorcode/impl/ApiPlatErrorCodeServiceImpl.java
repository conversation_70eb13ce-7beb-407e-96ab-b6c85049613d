package com.differ.jackyun.omsapi.user.biz.service.errorcode.impl;

import com.differ.jackyun.framework.component.pagehelper.util.StringUtil;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.errorcode.*;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.enums.ErrorCodeBizType;
import com.differ.jackyun.omsapi.user.biz.service.errorcode.IApiPlatErrorCodeService;
import com.differ.jackyun.omsapibase.data.core.PagingQueryResult;
import com.differ.jackyun.omsapibase.data.errorcode.ApiPlatErrorCodeEntity;
import com.differ.jackyun.omsapibase.data.errorcode.ApiPlatErrorCodeQueryCommand;
import com.differ.jackyun.omsapibase.infrastructure.utils.CollectionsUtil;
import com.differ.jackyun.omsapibase.infrastructure.utils.CoreUtils;
import com.differ.jackyun.omsapibase.repository.errorcode.ApiPlatErrorCodeRepository;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 平台错误编码服务实现类
 *
 * <AUTHOR>
 * @date 2023-11-27 16:39
 */
@Service
public class ApiPlatErrorCodeServiceImpl implements IApiPlatErrorCodeService {

    // region 变量

    /**
     * 仓储
     */
    @Autowired
    private ApiPlatErrorCodeRepository platErrorCodeRepository;

    // endregion

    // region 接口实现

    /**
     * 分页查询
     *
     * @param request 请求
     * @return 响应
     */
    @Override
    public PagingQueryResult<ApiPlatErrorCodeQueryResponse> pageQuery(ApiPlatErrorCodeQueryRequest request) {

        // 分页大小默认值
        request.setPageSize(request.getPageSize() == 0 ? 10 : request.getPageSize());

        // 封装查询指令
        ApiPlatErrorCodeQueryCommand command = new ApiPlatErrorCodeQueryCommand();
        command.setBizType(request.getBizType());
        command.setCode(request.getCode());
        command.setSubCode(request.getSubCode());
        command.setDescription(request.getDescription());
        command.setLimitClause(CoreUtils.getLimitClause(request.getPageIndex() - 1, request.getPageSize()));

        // 响应集合
        List<ApiPlatErrorCodeQueryResponse> responses = new ArrayList<>();

        // 查询数据库
        List<ApiPlatErrorCodeEntity> pageQueryResults = this.platErrorCodeRepository.pageQueryByCommand(command);
        pageQueryResults.forEach(t -> {
            ApiPlatErrorCodeQueryResponse response = new ApiPlatErrorCodeQueryResponse();
            response.setId(t.getId().toString());
            response.setBizType(t.getBizType());
            ErrorCodeBizType bizType = ErrorCodeBizType.create(t.getBizType());
            response.setBizTypeName(bizType == null ? "" : bizType.getName());
            response.setCode(t.getCode());
            response.setSubCode(t.getSubCode());
            response.setDescription(t.getDescription());
            response.setGmtCreate(t.getGmtCreate());
            response.setGmtModified(t.getGmtModified());
            responses.add(response);
        });

        // 查询总数
        int numTotalRecord = this.platErrorCodeRepository.queryCountByCommand(command);
        return new PagingQueryResult<>(responses, request.getPageSize(), request.getPageIndex(), numTotalRecord);
    }

    /**
     * 分组查询错误编码
     *
     * @param request 请求
     * @return 响应
     */
    @Override
    public ApiPlatErrorCodeGroupQueryResponse groupQuery(ApiPlatErrorCodeGroupQueryRequest request) {

        // 校验入参
        if (request == null) {
            return ApiPlatErrorCodeGroupQueryResponse.createFailed("请求为空");
        }

        // 校验业务类型
        if (StringUtil.isEmpty(request.getBizType()) || Objects.isNull(ErrorCodeBizType.create(request.getBizType()))) {
            return ApiPlatErrorCodeGroupQueryResponse.createFailed("业务类型不能为空或业务类型错误");
        }

        // 按业务类型查询
        List<ApiPlatErrorCodeEntity> entities = this.platErrorCodeRepository.getDataByBizType(request.getBizType());
        if (CollectionUtils.isEmpty(entities)) {
            return ApiPlatErrorCodeGroupQueryResponse.createSuccess();
        }

        // 结果集
        Map<String, List<ApiPlatErrorCodeGroupQueryResponse.SubCodeItem>> codeGroups = new HashMap<>();

        // 按错误编码分组
        Map<String, List<ApiPlatErrorCodeEntity>> groupByCode = entities.stream().collect(Collectors.groupingBy(ApiPlatErrorCodeEntity::getCode));
        groupByCode.forEach((code, items) -> {

            // 错误子编码结果集
            List<ApiPlatErrorCodeGroupQueryResponse.SubCodeItem> subCodeItems = new ArrayList<>();

            // 错误子编码去重集
            Set<String> subCodes = new HashSet<>();

            // 遍历实体
            items.forEach(item -> {

                // 去重
                if (subCodes.contains(item.getSubCode())) {
                    return;
                }

                // 添加到错误子编码结果集
                ApiPlatErrorCodeGroupQueryResponse.SubCodeItem subCodeItem = new ApiPlatErrorCodeGroupQueryResponse.SubCodeItem();
                subCodeItem.setSubCode(item.getSubCode());
                subCodeItem.setDescription(item.getDescription());
                subCodeItems.add(subCodeItem);

                // 添加到去重集
                subCodes.add(item.getSubCode());
            });

            // 添加到错误编码分组
            codeGroups.put(code, subCodeItems);
        });

        ApiPlatErrorCodeGroupQueryResponse response = ApiPlatErrorCodeGroupQueryResponse.createSuccess();
        response.setCodeGroups(codeGroups);
        return response;
    }

    /**
     * 新增数据
     *
     * @param request 请求
     * @return 响应
     */
    @Override
    public ApiPlatErrorCodeCommonResponse insertData(ApiPlatErrorCodeInsertRequest request) {

        // 校验入参
        if (request == null) {
            return ApiPlatErrorCodeCommonResponse.createFailed("请求为空");
        }

        // 校验业务类型
        if (StringUtil.isEmpty(request.getBizType()) || Objects.isNull(ErrorCodeBizType.create(request.getBizType()))) {
            return ApiPlatErrorCodeCommonResponse.createFailed("业务类型不能为空或业务类型错误");
        }

        // 校验错误编码
        if (StringUtil.isEmpty(request.getCode())) {
            return ApiPlatErrorCodeCommonResponse.createFailed("错误编码为空");
        }

        // 校验错误子编码
        if (StringUtil.isEmpty(request.getSubCode())) {
            return ApiPlatErrorCodeCommonResponse.createFailed("错误子编码为空");
        }

        // 根据名称或标识查询数据是否存在
        List<ApiPlatErrorCodeEntity> existEntityList = this.platErrorCodeRepository.getDataByCode(request.getBizType(), request.getCode(), request.getSubCode());
        if (CollectionsUtil.isNotBlank(existEntityList)) {
            return ApiPlatErrorCodeCommonResponse.createFailed("错误编码和子编码已存在");
        }

        // 封装实体
        ApiPlatErrorCodeEntity entity = new ApiPlatErrorCodeEntity();
        entity.setBizType(request.getBizType());
        entity.setCode(request.getCode());
        entity.setSubCode(request.getSubCode());
        entity.setDescription(request.getDescription());

        // 数据库插入数据
        this.platErrorCodeRepository.insert(entity);
        return ApiPlatErrorCodeCommonResponse.createSuccess();
    }

    /**
     * 更新数据
     *
     * @param request 请求
     * @return 响应
     */
    @Override
    public ApiPlatErrorCodeCommonResponse updateData(ApiPlatErrorCodeUpdateRequest request) {

        // 校验入参
        if (request == null) {
            return ApiPlatErrorCodeCommonResponse.createFailed("请求为空");
        }

        // 校验主键
        if (StringUtil.isEmpty(request.getId())) {
            return ApiPlatErrorCodeCommonResponse.createFailed("主键为空");
        }

        // 校验业务类型
        if (StringUtil.isEmpty(request.getBizType()) || Objects.isNull(ErrorCodeBizType.create(request.getBizType()))) {
            return ApiPlatErrorCodeCommonResponse.createFailed("业务类型不能为空或业务类型错误");
        }

        // 校验错误编码
        if (StringUtil.isEmpty(request.getCode())) {
            return ApiPlatErrorCodeCommonResponse.createFailed("错误编码为空");
        }

        // 校验错误子编码
        if (StringUtil.isEmpty(request.getSubCode())) {
            return ApiPlatErrorCodeCommonResponse.createFailed("错误子编码为空");
        }

        // 封装实体
        ApiPlatErrorCodeEntity entity = new ApiPlatErrorCodeEntity();
        entity.setId(Long.parseLong(request.getId()));
        entity.setBizType(request.getBizType());
        entity.setCode(request.getCode());
        entity.setSubCode(request.getSubCode());
        entity.setDescription(request.getDescription());

        // 根据名称或标识查询数据是否存在
        List<ApiPlatErrorCodeEntity> existEntityList = this.platErrorCodeRepository.getDataByCode(request.getBizType(), request.getCode(), request.getSubCode());
        existEntityList = existEntityList.stream().filter(t -> !t.getId().equals(entity.getId())).collect(Collectors.toList());
        if (CollectionsUtil.isNotBlank(existEntityList)) {
            return ApiPlatErrorCodeCommonResponse.createFailed("名称或限流标识已存在");
        }

        // 数据库更新数据
        this.platErrorCodeRepository.update(entity);
        return ApiPlatErrorCodeCommonResponse.createSuccess();
    }

    /**
     * 删除数据
     *
     * @param request 请求
     * @return 响应
     */
    @Override
    public ApiPlatErrorCodeCommonResponse deleteData(ApiPlatErrorCodeDeleteRequest request) {

        // 校验入参
        if (request == null) {
            return ApiPlatErrorCodeCommonResponse.createFailed("请求为空");
        }

        // 校验主键
        if (CollectionsUtil.isBlank(request.getIds())) {
            return ApiPlatErrorCodeCommonResponse.createFailed("非法入参");
        }

        // 数据库删除数据
        List<Long> ids = request.getIds().stream().map(Long::parseLong).collect(Collectors.toList());
        this.platErrorCodeRepository.delete(ids);
        return ApiPlatErrorCodeCommonResponse.createSuccess();
    }

    // endregion
}
