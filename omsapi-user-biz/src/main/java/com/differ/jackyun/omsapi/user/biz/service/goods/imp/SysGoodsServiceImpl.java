package com.differ.jackyun.omsapi.user.biz.service.goods.imp;

import com.differ.jackyun.framework.component.basic.member.member.MemberHolder;
import com.differ.jackyun.omsapi.component.util.anno.Out;
import com.differ.jackyun.omsapi.user.biz.domain.goods.utils.BizGoodsUtils;
import com.differ.jackyun.omsapi.user.biz.domain.match.QueryGoodsMatcher;
import com.differ.jackyun.omsapi.user.biz.domain.match.utils.GoodsMatchUtils;
import com.differ.jackyun.omsapi.user.biz.infrastructure.api.config.BizConfigUtils;
import com.differ.jackyun.omsapi.user.biz.infrastructure.api.config.enums.OpenConfigEnum;
import com.differ.jackyun.omsapi.user.biz.infrastructure.common.LogAdapter;
import com.differ.jackyun.omsapi.user.biz.infrastructure.condition.ConditionalOnSite;
import com.differ.jackyun.omsapi.user.biz.service.adapter.goods.IErpGoodsChangeAdapterService;
import com.differ.jackyun.omsapi.user.biz.service.goods.ISysGoodsService;
import com.differ.jackyun.omsapi.user.biz.service.tmallukura.ITuSysGoodsChangeService;
import com.differ.jackyun.omsapibase.dao.jackyunmultidb.goodsmatch.switchdb.ApiPlatGoodsMatchExtraDBSwitchMapper;
import com.differ.jackyun.omsapibase.dao.jackyunmultidb.omsonline.DaoBizGoodsSkuDBSwitchMapper;
import com.differ.jackyun.omsapibase.data.common.OmsApiResult;
import com.differ.jackyun.omsapibase.data.constant.OmsApiConstant;
import com.differ.jackyun.omsapibase.data.goods.OnSaleGoodsDTO;
import com.differ.jackyun.omsapibase.data.goodsmatch.ApiPlatGoodsMatchExtraEntity;
import com.differ.jackyun.omsapibase.infrastructure.constant.SiteTypeCodeConst;
import com.differ.jackyun.omsapibase.infrastructure.enums.ErrorCodes;
import com.differ.jackyun.omsapibase.infrastructure.enums.LogTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.exception.AppException;
import com.differ.jackyun.omsapibase.infrastructure.openapi.goods.ErpGoodsChangeEventRequestBizData;
import com.differ.jackyun.omsapibase.infrastructure.utils.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 系统货品服务类
 *
 * <AUTHOR>
 * @date 2022/9/7 16:27
 */
@ConditionalOnSite(sites = {SiteTypeCodeConst.OMS_API_BUSINESS})
@Service
public class SysGoodsServiceImpl implements ISysGoodsService {

    /**
     * 货品变更适配器
     */
    @Autowired
    private IErpGoodsChangeAdapterService goodsChangeAdapterService;

    /**
     * 优仓货品变更服务类
     */
    @Autowired
    private ITuSysGoodsChangeService tuChangeService;

    @Autowired
    protected DaoBizGoodsSkuDBSwitchMapper daoBizGoodsSkuDBSwitchMapper;

    @Autowired
    private QueryGoodsMatcher queryGoodsMatcher;

    /**
     * erp 货品变更处理
     *
     * @param requestBizData 请求参数
     * @return 成功or失败
     */
    @Override
    public Boolean erpGoodsChange(ErpGoodsChangeEventRequestBizData requestBizData) {

        // 执行货品变更处理
        goodsChangeAdapterService.adapterHandle(requestBizData);

        // 优仓货品变更处理
//        tuChangeService.sysGoodsChange(requestBizData);
        return true;
    }

    /**
     * 在售列表查询
     *
     * @param request 请求参数
     * @return
     */
    @Override
    public OnSaleGoodsDTO.OnSaleGoodsResponse onsaleGoodsList(OnSaleGoodsDTO.OnSaleGoodsRequest request) {
        String memberName = MemberHolder.getMemberName();
        // 设置分页offset
        request.setOffset((request.getPageIndex() - 1) * request.getPageSize());

        // 查询在售列表数据
        int numRecord = this.daoBizGoodsSkuDBSwitchMapper.countOnSaleGoodsList(memberName, request);
        List<OnSaleGoodsDTO.OnSaleShop> onSaleShopList;
        if (YunConfigUtils.isEnableGoodsMatchRestructure(memberName) || BizConfigUtils.matchValue(OpenConfigEnum.VERIFY_ERP_SHOP_PERMISSION_MEMBER, memberName)) {

            // 重新设置ERP权限店铺列表
            this.setErpAuthShopIds(memberName, request);

            // 新匹配查出platGoodsGuid后面再去查匹配扩展表
            onSaleShopList = this.daoBizGoodsSkuDBSwitchMapper.queryOnSaleGoodsList(memberName, request);
        } else {
            // 老匹配连表查出匹配表扩展信息
            onSaleShopList = this.daoBizGoodsSkuDBSwitchMapper.queryOnSaleGoodsListWithMatchExtra(memberName, request);
        }

        // 设置匹配标记
        if(CollectionUtils.isNotEmpty(onSaleShopList)){
            this.setGoodsMatchFlags(memberName, onSaleShopList);
        }


        // 设置响应对象
        return OnSaleGoodsDTO.OnSaleGoodsResponse.make(onSaleShopList, request.getPageIndex(), request.getPageSize(), numRecord);
    }

    /**
     * 设置匹配标记
     *
     * @param memberName     会员名
     * @param onSaleShopList 在售店铺列表数据
     */
    private void setGoodsMatchFlags(String memberName, List<OnSaleGoodsDTO.OnSaleShop> onSaleShopList) {
        if (YunConfigUtils.isEnableGoodsMatchRestructure(memberName)) {
            List<String> platGoodsGuidList = onSaleShopList.stream().flatMap(t -> t.getLstOnSaleGoods().stream()).map(OnSaleGoodsDTO.GoodsInfo::getPlatGoodsGuid).collect(Collectors.toList());
            // 查询匹配扩展表
            List<ApiPlatGoodsMatchExtraEntity> matchExtraItems = SpringResolveManager.resolve(ApiPlatGoodsMatchExtraDBSwitchMapper.class).findGoodsMatchExtraByGuid(memberName, platGoodsGuidList);
            if (CollectionsUtil.isBlank(matchExtraItems)) {
                return;
            }
            Map<String, ApiPlatGoodsMatchExtraEntity> matchExtraEntityMap = matchExtraItems.stream().collect(Collectors.toMap(ApiPlatGoodsMatchExtraEntity::getPlatGoodsGuid, t -> t));
            for (OnSaleGoodsDTO.OnSaleShop onSaleShop : onSaleShopList) {
                for (OnSaleGoodsDTO.GoodsInfo lstOnSaleGood : onSaleShop.getLstOnSaleGoods()) {
                    ApiPlatGoodsMatchExtraEntity apiPlatGoodsMatchExtraEntity = matchExtraEntityMap.get(lstOnSaleGood.getPlatGoodsGuid());
                    String goodsMatchExtraJson = apiPlatGoodsMatchExtraEntity == null ? OmsApiConstant.EMPTY_STR : apiPlatGoodsMatchExtraEntity.getExtraJson();
                    lstOnSaleGood.setGoodsMatchFlagList(GoodsMatchUtils.convertGoodsMatchFlag(goodsMatchExtraJson));
                }
            }
        } else {
            for (OnSaleGoodsDTO.OnSaleShop onSaleShop : onSaleShopList) {
                for (OnSaleGoodsDTO.GoodsInfo lstOnSaleGood : onSaleShop.getLstOnSaleGoods()) {
                    lstOnSaleGood.setGoodsMatchFlagList(GoodsMatchUtils.convertGoodsMatchFlag(lstOnSaleGood.getMatchExtra()));
                    lstOnSaleGood.setMatchExtra(null);
                }
            }
        }
    }

    /**
     * 重新设置ERP权限店铺列表
     * @param memberName 会员名
     * @param request 接口请求
     */
    private void setErpAuthShopIds(String memberName, @Out OnSaleGoodsDTO.OnSaleGoodsRequest request) {

        // 未开启权限过滤
        if (!request.getPermissionFilter()) {
            return;
        }

        // 接口店铺列表
        Long[] sourceShopIds = Optional.ofNullable(request.getShopIds()).map(shopIds -> shopIds.toArray(new Long[0])).orElse(new Long[0]);

        // 获取查询ERP权限控制的可查询店铺列表
        OmsApiResult<Long[]> intersectionRet = BizGoodsUtils.filterQueryGoodsPermissionShop(memberName, sourceShopIds);

        // 日志记录
        LogAdapter.writeSystemLog(memberName, BizGoodsUtils.PRODUCT_ERP_PERMISSION_LOG, () -> ExtUtils.stringBuilderAppend(String.format("最终待查询店铺结果：%s", JsonUtils.toJson(intersectionRet))), LogTypeEnum.TRACE);

        // 重新获取店铺列表失败
        if (!intersectionRet.isSuccess()) {
            AppException.throwException(ErrorCodes.LOGICERROR, intersectionRet.getMessage());
        }

        // 重新设置店铺列表
        request.setShopIds(Arrays.asList(intersectionRet.getData()));
    }

}
