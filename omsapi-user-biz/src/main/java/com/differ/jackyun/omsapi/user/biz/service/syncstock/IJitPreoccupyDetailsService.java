package com.differ.jackyun.omsapi.user.biz.service.syncstock;

import com.differ.jackyun.omsapi.user.biz.infrastructure.data.dto.syncstock.preoccupy.JitOccupyDetailsResponse;

import java.time.LocalDateTime;

/**
 * 唯品会 JIT 预占库存明细服务接口
 *
 * <AUTHOR>
 * @date 2023/6/9 10:20
 */
public interface IJitPreoccupyDetailsService {

    /**
     * 查询唯品会 JIT 预占明细
     *
     * @param memberName 会员名
     * @param matchGuid  匹配 GUID
     * @param startTime  起始时间
     * @param endTime    结束时间
     * @return 结果
     */
    JitOccupyDetailsResponse queryJitOccupyDetails(
            String memberName,
            String matchGuid,
            LocalDateTime startTime,
            LocalDateTime endTime
    );
}
