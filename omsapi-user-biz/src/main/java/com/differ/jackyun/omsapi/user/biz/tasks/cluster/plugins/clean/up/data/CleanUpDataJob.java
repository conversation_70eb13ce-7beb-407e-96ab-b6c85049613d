package com.differ.jackyun.omsapi.user.biz.tasks.cluster.plugins.clean.up.data;

import com.differ.jackyun.omsapi.component.task.single.core.JobExecTimeStrategy;
import com.differ.jackyun.omsapi.component.task.single.core.SingleJobParameter;
import com.differ.jackyun.omsapi.user.biz.infrastructure.cache.remote.hash.clean.up.data.CleanUpDataCache;
import com.differ.jackyun.omsapi.user.biz.infrastructure.common.LogAdapter;
import com.differ.jackyun.omsapi.user.biz.infrastructure.condition.ConditionalOnJobParam;
import com.differ.jackyun.omsapi.user.biz.tasks.cluster.core.BaseClusterJob;
import com.differ.jackyun.omsapi.user.biz.tasks.cluster.core.ClusterJobParameter;
import com.differ.jackyun.omsapi.user.biz.tasks.cluster.plugins.clean.up.data.imp.BusinessCleanUpDataProcess;
import com.differ.jackyun.omsapi.user.biz.tasks.cluster.strategy.plugins.JobNameHashExecStrategy;
import com.differ.jackyun.omsapibase.infrastructure.constant.SiteTypeCodeConst;
import com.differ.jackyun.omsapibase.infrastructure.enums.ConfigKeyEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.SiteTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.utils.CollectionsUtil;
import com.differ.jackyun.omsapibase.infrastructure.utils.LocalConfig;
import com.differ.jackyun.omsapibase.infrastructure.utils.SpringResolveManager;
import com.differ.jackyun.omsapibase.infrastructure.utils.YunConfigUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.List;
import java.util.Map;

/**
 * @Decription 清除数据任务
 * <AUTHOR>
 * @Date 2024-03-05 16:04
 **/
@ConditionalOnJobParam
@SingleJobParameter(
        sitesToRun = {SiteTypeCodeConst.OMS_API_BUSINESS},
        jobName = "CleanUpDataJob",
        cron = "0 0/5 * * * ?"
)
@ClusterJobParameter(jobExecStrategy = JobNameHashExecStrategy.class)
public class CleanUpDataJob extends BaseClusterJob {

    // region 变量

    /**
     * 工作任务执行频率(单位:秒)
     */
    private long jobFrequency = 5;

    private static final String LOG_CAPPTION="清除数据任务";

    // endregion

    // region 重写方法


    /**
     * 设置实际执行频率
     *
     * @return 执行时间策略
     */
    @Override
    protected JobExecTimeStrategy getExecTimeStrategy() {
        this.execTimeStrategy.setRunFrequency(NumberUtils.toLong(YunConfigUtils.getDBConfig(ConfigKeyEnum.CLEAN_UP_DATA_JOB_FREQUENCY), 3600));
        return execTimeStrategy;
    }

    /**
     * 业务处理
     */
    @Override
    protected void doWork() {

        // 实例化处理器
        ICleanUpDataProcess process = createInstance();
        if (null == process) {
            return;
        }

        // 获取任务
        Map<String, List<Long>> memberShopMap = CleanUpDataCache.get().getCacheData();
        if (null == memberShopMap || memberShopMap.isEmpty()) {
            return;
        }

        for (Map.Entry<String, List<Long>> memberShopMapItem : memberShopMap.entrySet()) {

            // 是否允许，不允许需要将缓存数据清空
            if (!YunConfigUtils.contains(memberShopMapItem.getKey(), ConfigKeyEnum.CHANNEL_DELETE_EVENT_CLEAN_UP_DATA)) {
                CleanUpDataCache.get().removeCacheData(memberShopMapItem.getKey());
                return;
            }
            LogAdapter.writeSystemLog(LOG_CAPPTION,String.format("吉客号:%s开始清除数据,",memberShopMapItem.getKey()));

            // 是否完成任务
            boolean isComplete = false;
            if (CollectionsUtil.isBlank(memberShopMapItem.getValue())) {
                isComplete = process.doCleanUpData(memberShopMapItem.getKey());
            } else {
                isComplete = process.doCleanUpData(memberShopMapItem.getKey(), memberShopMapItem.getValue());
            }

            // 如果完成任务需要将缓存数据删除
            if (isComplete) {
                LogAdapter.writeSystemLog(LOG_CAPPTION,String.format("吉客号:%s清除数据完成.",memberShopMapItem.getKey()));
                CleanUpDataCache.get().removeCacheData(memberShopMapItem.getKey(), memberShopMapItem.getValue());
            }
        }
    }

    // endregion

    // region 私有方法

    private ICleanUpDataProcess createInstance() {
        SiteTypeEnum currentSiteType = LocalConfig.get().getSiteType();
        ICleanUpDataProcess process = null;
        switch (currentSiteType) {
            case BUSINESS:
                process = SpringResolveManager.resolve(BusinessCleanUpDataProcess.class, "BusinessCleanUpDataProcess");
                break;
            default:
                break;
        }
        return process;

    }

    // endregion

}
