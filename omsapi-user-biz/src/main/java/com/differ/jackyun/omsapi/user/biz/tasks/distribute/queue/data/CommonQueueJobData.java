package com.differ.jackyun.omsapi.user.biz.tasks.distribute.queue.data;

import java.time.LocalDateTime;

/**
 * 通用队列任务数据
 *
 * <AUTHOR>
 * @date 2025-03-05 16:25
 */
public class CommonQueueJobData extends AbstractQueueJobData {

    /**
     * 任务 ID
     */
    private String taskId;

    /**
     * 任务数据
     */
    private String taskData;

    /**
     * 会员名
     */
    private String memberName;

    /**
     * 总执行次数
     */
    private Integer totalExecTimes;

    /**
     * 总失败次数
     */
    private Integer totalFailedTimes;

    /**
     * 连续失败次数
     */
    private Integer consecutiveFailedTimes;

    /**
     * 上次执行时间
     */
    private LocalDateTime lastExecTime;

    /**
     * 唯一标识
     *
     * @return 唯一标识
     */
    @Override
    public String uniqueSign() {
        return this.taskId;
    }

    /**
     * 成功时更新
     *
     * @param nextTaskData 下次任务数据
     */
    public void updateWhenSuccess(String nextTaskData) {

        // 更新任务数据
        this.taskData = nextTaskData;

        // 更新上次执行时间
        this.updateLastExecTime();

        // 累加总执行次数
        this.incrementTotalExecTimes();

        // 清空连续失败次数
        this.clearConsecutiveFailedTimes();
    }

    /**
     * 失败时更新
     */
    public void updateWhenFailed() {

        // 更新上次执行时间
        this.updateLastExecTime();

        // 累加总执行次数
        this.incrementTotalExecTimes();

        // 累加总失败次数
        this.incrementTotalFailedTimes();

        // 累加连续失败次数
        this.incrementConsecutiveFailedTimes();
    }

    /**
     * 更新上次执行时间
     */
    private void updateLastExecTime() {
        this.lastExecTime = LocalDateTime.now();
    }

    /**
     * 累加总执行次数
     */
    private void incrementTotalExecTimes() {
        if (this.totalExecTimes == null) {
            this.totalExecTimes = 1;
        } else {
            this.totalExecTimes++;
        }
    }

    /**
     * 累加总失败次数
     */
    private void incrementTotalFailedTimes() {
        if (this.totalFailedTimes == null) {
            this.totalFailedTimes = 1;
        } else {
            this.totalFailedTimes++;
        }
    }

    /**
     * 累加连续失败次数
     */
    private void incrementConsecutiveFailedTimes() {
        if (this.consecutiveFailedTimes == null) {
            this.consecutiveFailedTimes = 1;
        } else {
            this.consecutiveFailedTimes++;
        }
    }

    /**
     * 清空连续失败次数
     */
    private void clearConsecutiveFailedTimes() {
        this.consecutiveFailedTimes = 0;
    }

    // region getter & setter

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskData() {
        return taskData;
    }

    public void setTaskData(String taskData) {
        this.taskData = taskData;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public Integer getTotalExecTimes() {
        return totalExecTimes;
    }

    public void setTotalExecTimes(Integer totalExecTimes) {
        this.totalExecTimes = totalExecTimes;
    }

    public Integer getTotalFailedTimes() {
        return totalFailedTimes;
    }

    public void setTotalFailedTimes(Integer totalFailedTimes) {
        this.totalFailedTimes = totalFailedTimes;
    }

    public Integer getConsecutiveFailedTimes() {
        return consecutiveFailedTimes;
    }

    public void setConsecutiveFailedTimes(Integer consecutiveFailedTimes) {
        this.consecutiveFailedTimes = consecutiveFailedTimes;
    }

    public LocalDateTime getLastExecTime() {
        return lastExecTime;
    }

    public void setLastExecTime(LocalDateTime lastExecTime) {
        this.lastExecTime = lastExecTime;
    }

    // endregion
}
