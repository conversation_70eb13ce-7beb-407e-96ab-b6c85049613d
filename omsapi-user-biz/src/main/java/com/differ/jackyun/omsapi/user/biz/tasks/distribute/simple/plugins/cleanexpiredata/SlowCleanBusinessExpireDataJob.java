package com.differ.jackyun.omsapi.user.biz.tasks.distribute.simple.plugins.cleanexpiredata;

import com.differ.jackyun.omsapi.component.task.single.core.SingleJobParameter;
import com.differ.jackyun.omsapi.user.biz.infrastructure.condition.ConditionalOnJobParam;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.enums.BusinessExpireDataCleanJobEnum;
import com.differ.jackyun.omsapi.user.biz.tasks.distribute.simple.core.SimpleDistributeJobParameter;
import com.differ.jackyun.omsapi.user.biz.tasks.distribute.simple.strategy.sharding.plugins.ConsistentHashJobShardingStrategy;
import com.differ.jackyun.omsapibase.infrastructure.constant.SiteTypeCodeConst;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 定时删除过期业务数据（慢频 每天凌晨一点执行）
 *
 * <AUTHOR>
 * @date 2024-10-08 10:55
 */
@ConditionalOnJobParam
@SingleJobParameter(
        sitesToRun = {SiteTypeCodeConst.OMS_API_BUSINESS},
        jobName = "SlowCleanBusinessExpireDataJob",
        cron = "0 0 1 * * ?"
)
@SimpleDistributeJobParameter(jobShardingStrategy = ConsistentHashJobShardingStrategy.class)
public class SlowCleanBusinessExpireDataJob extends BaseCleanBusinessExpireDataJob {

    /**
     * 获取待清除过期数据业务表
     */
    @Override
    protected List<BusinessExpireDataCleanJobEnum> getCleanExpireDataTableEnum() {
        return Arrays.stream(BusinessExpireDataCleanJobEnum.values()).filter(expireDataCleanJob -> Boolean.FALSE.equals(expireDataCleanJob.getQuickData())).collect(Collectors.toList());
    }
}
