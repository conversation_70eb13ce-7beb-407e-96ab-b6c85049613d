package com.differ.jackyun.omsapi.user.biz.tasks.distribute.tts.plugins.stock;

import com.differ.jackyun.omsapi.component.tts.core.TTSJobParameter;
import com.differ.jackyun.omsapi.user.biz.infrastructure.cache.local.plugins.AlarmIntervalLocalCache;
import com.differ.jackyun.omsapi.user.biz.infrastructure.common.LogAdapter;
import com.differ.jackyun.omsapi.user.biz.infrastructure.data.enums.AlarmIntervalTypeEnum;
import com.differ.jackyun.omsapi.user.biz.tasks.distribute.tts.core.BaseUserDistributeJob;
import com.differ.jackyun.omsapibase.dao.jackyunmultidb.stock.match.ApiGoodsMatchDBSwitchMapper;
import com.differ.jackyun.omsapibase.infrastructure.constant.SiteTypeCodeConst;
import com.differ.jackyun.omsapibase.infrastructure.enums.ConfigKeyEnum;
import com.differ.jackyun.omsapibase.infrastructure.enums.LogTypeEnum;
import com.differ.jackyun.omsapibase.infrastructure.utils.YunConfigUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description 监控匹配商品数据量大的客户, 每天凌晨3点执行
 * <AUTHOR>
 * @since 2021/10/22 10:49
 */
//@TTSJobParameter(
//        jobName = "BigGoodsMatchMonitorJob",
//        cron = "0 0 3 * * ? *",
//        shardingTotalCount = 10,
//        sitesToRun = {SiteTypeCodeConst.OMS_API_STOCK}
//)
@Deprecated
public class BigGoodsMatchMonitorJob extends BaseUserDistributeJob {

    @Autowired
    private ApiGoodsMatchDBSwitchMapper apiGoodsMatchDBSwitchMapper;

    @Override
    public void executeUserTask(String memberName) {
        //已经处理过的会员直接return
        if (YunConfigUtils.contains(memberName, ConfigKeyEnum.BIG_GOODS_MATCH_HAS_BEEN_DEAL_MEMBER)) {
            return;
        }

        //匹配数据监控数量
        int bigGoodsMatchMonitorNum = Integer.parseInt(YunConfigUtils.getDBConfig(ConfigKeyEnum.BIG_GOODS_MATCH_MONITOR_NUM));
        //查询匹配表的数据量，若大于bigGoodsMatchMonitorNum，则进行微信报警并记录系统日志
        int apiGoodsMatchEntitiesCount = apiGoodsMatchDBSwitchMapper.getApiGoodsMatchEntitiesCount(memberName, null);
        if (apiGoodsMatchEntitiesCount >= bigGoodsMatchMonitorNum) {
            String alarmText = String.format("会员%s商品匹配数据量有%s，超过%s", memberName, apiGoodsMatchEntitiesCount, bigGoodsMatchMonitorNum);
            AlarmIntervalLocalCache.get().alarmInterval(AlarmIntervalTypeEnum.BIG_GOODS_MATCH_MONITOR, memberName, alarmText);
            LogAdapter.writeSystemLog("大商品匹配数据报警", alarmText, LogTypeEnum.SUPERWARNING);
        }
    }
}
