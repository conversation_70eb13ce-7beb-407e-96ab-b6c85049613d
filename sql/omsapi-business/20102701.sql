drop procedure if exists `g_sys_move_online2his`;

create procedure `g_sys_move_online2his`(
    in time_rang int,
    in count_rang int,
    out return_num int
    /*
    | 1> IN time_rang INT（时间范围：归档time_range天之前的原始单数据）
    | 2> IN count_rang INT（数量范围：当数量>count_rang万时才执行归档）
    | 3> OUT return_num INT （此次归档调用影响主记录数）
    */
)
begin
    /**
    | his表清单：j_trade_online_his j_trade_online_goods_his j_trade_online_goods_delivery_his
    |
    */
    declare _code char(5) default '00000';
    declare _msg text;
    declare _errflag int default 0;
    declare _result varchar(2000) default 'success';
    declare _table_name varchar(100);
    declare _database varchar(50);
    declare _cols text;
    declare _cols_flag text;
    declare _table_tag varchar(5) default '_his';
    declare _table_loop_id int default 0;
    declare _trade_loop_id int default 0;
    declare _max_trade_loop_id int default 0;
    declare _step int default 1000;
    declare continue handler for sqlexception
    begin
        get diagnostics condition 1 _code = returned_sqlstate, _msg = message_text;
        set _errflag = 1;
    end;
    select database() into _database;
    set return_num = 0;
    if count_rang < 10000 then
        set count_rang = count_rang * 10000;
    end if;
    exit_label: begin
        create temporary table if not exists temp_table_name_online(
            id int auto_increment primary key,
            table_name varchar(100) comment '表名'
        ) comment '表清单';
        create temporary table if not exists temp_table_cols_online(
            id int auto_increment primary key,
            table_name varchar(100) comment '表名',
            cols text comment '列名',
            cols_flag text comment '列名含别名'
        ) comment '表字段清单';
        truncate table temp_table_name_online;
        truncate table temp_table_cols_online;
        # j_trade_online_goods_delivery依赖j_trade_online_goods
        insert into temp_table_name_online(table_name)
        values ('j_trade_online_goods_delivery'),('j_trade_online_goods'),('j_trade_online');
        # 归集表对应字段名，后续循环过程不需反复查
        select id, table_name into _table_loop_id, _table_name from temp_table_name_online where id > _table_loop_id order by id limit 1;
        while ifnull(_table_name, '') <> '' do
            set _cols = f_gettable_columns(_database, _table_name, '', 'gmt_modified');
            set _cols_flag = f_gettable_columns(_database, _table_name, 'a', 'gmt_modified');
            insert into temp_table_cols_online(table_name, cols, cols_flag)
            values (_table_name, _cols, _cols_flag);
            set _table_name = '';
            select id, table_name into _table_loop_id, _table_name from temp_table_name_online where id > _table_loop_id order by id limit 1;
        end while;
        drop table if exists tmp_online_id_to_his_total;
        create table if not exists tmp_online_id_to_his_total(
             auto_id int not null auto_increment,
             trade_id bigint not null,
             insert_time  datetime default current_timestamp,
             primary key(auto_id),
             unique key uk_tmp_online_id_to_his_total(trade_id)
        ) engine=innodb;
        truncate table tmp_online_id_to_his_total;
        /**
            | 提取满足条件的订单清单
            | 已完成的前n天；已取消（含被拆分合并的）前n天
        */
        insert into tmp_online_id_to_his_total(trade_id, insert_time)
        select a.trade_id, now()
        from j_trade_online a
        where cur_status >= 3000 and gmt_modified < date_sub(now(), interval time_rang day);
        if _code <> '00000' then
            set _result = concat('insert into tmp_online_id_to_his_total, error = ', _code, ', message = ',_msg);
            select _result;
            leave exit_label;
        end if;
        set return_num = row_count();
        # 不满足归档记录条数
        if return_num < count_rang then
            set return_num = 0;
            set _result = concat('不满足归档数量要求');
            select _result;
            leave exit_label;
        end if;
        # 遍历表按1000单每批处理
        set _step = 1000;
        select min(auto_id), max(auto_id) into _trade_loop_id, _max_trade_loop_id from tmp_online_id_to_his_total;
        while _trade_loop_id <= _max_trade_loop_id do
            # 遍历表按_step单每批处理
            start transaction;
            set _table_loop_id = 0;
            select id, table_name, cols, cols_flag into _table_loop_id, _table_name, _cols, _cols_flag
            from temp_table_cols_online where id > _table_loop_id order by id limit 1;
            while ifnull(_table_name, '') <> '' do
                set @my_sql = concat('insert into ', _table_name, _table_tag, '(', _cols, ')
                    select ', _cols_flag, ' from ', _table_name,
                    ' a join tmp_online_id_to_his_total b on a.trade_id = b.trade_id where b.auto_id >= ',
                    _trade_loop_id, ' and b.auto_id < ', _trade_loop_id + _step, ';');
                if find_in_set('trade_id', _cols) = 0 then
                    set @my_sql = concat('insert into ', _table_name, _table_tag, '(', _cols, ')
                        select ', _cols_flag, ' from ', _table_name,
                        ' a join j_trade_online_goods g on a.sub_trade_id = g.sub_trade_id
                            join tmp_online_id_to_his_total b on g.trade_id = b.trade_id where b.auto_id >= ',
                        _trade_loop_id, ' and b.auto_id < ', _trade_loop_id + _step, ';');
                end if;
                if @my_sql is null then
                    rollback;
                    set _result = concat('insert a from ', _table_name, _table_tag, ', error = stmt null');
                    select _result;
                    leave exit_label;
                end if;
                prepare stmt from @my_sql;
                execute stmt;
                deallocate prepare stmt;
                if _code <> '00000' then
                    rollback;
                    set _result = concat('insert into ', _table_name, _table_tag, ', error = ', _code, ', message = ',_msg);
                    select _result;
                    leave exit_label;
                end if;
                # 删除时保守起见与原表记录做关联
                set @my_sql = concat('delete a from ', _table_name,
                    ' a join tmp_online_id_to_his_total b on a.trade_id = b.trade_id join ', _table_name,
                    _table_tag, ' h on a.trade_id = h.trade_id where b.auto_id >= ',
                    _trade_loop_id, ' and b.auto_id < ', _trade_loop_id + _step, ';');
                if find_in_set('trade_id', _cols) = 0 then
                    set @my_sql = concat('delete a from ', _table_name,
                        ' a join j_trade_online_goods g on a.sub_trade_id = g.sub_trade_id
                            join tmp_online_id_to_his_total b on g.trade_id = b.trade_id join ', _table_name,
                        _table_tag, ' h on a.sub_trade_id = h.sub_trade_id where b.auto_id >= ',
                        _trade_loop_id, ' and b.auto_id < ', _trade_loop_id + _step, ';');
                end if;
                if @my_sql is null then
                    rollback;
                    set _result = concat('delete a from ', _table_name, ', error = stmt null');
                    select _result;
                    leave exit_label;
                end if;
                prepare stmt from @my_sql;
                execute stmt;
                deallocate prepare stmt;
                if _code <> '00000' then
                    rollback;
                    set _result = concat('delete ', _table_name, ', error = ', _code, ', message = ',_msg);
                    select _result;
                    leave exit_label;
                end if;
                set _table_name = '';
                select id, table_name, cols, cols_flag into _table_loop_id, _table_name, _cols, _cols_flag
                from temp_table_cols_online where id > _table_loop_id order by id limit 1;
            end while;
            commit;
            set _trade_loop_id = _trade_loop_id + _step;
        end while;
        select _result;
    end exit_label;
end;