# 新建j_trade_online_log_extra表
CREATE TABLE IF NOT EXISTS `j_trade_online_log_extra`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `parent_id` int NOT NULL COMMENT '父级日志id',
  `detail_info` varchar(500) NOT NULL COMMENT '明细信息',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`id`),
  INDEX `idx_parent_id`(`parent_id`) USING BTREE COMMENT 'parent_id索引'
);

# j_trade_online_log表新增字段
CALL sp_AddColumnUnlessExists('j_trade_online_log', 'has_extra', 'tinyint(4) NOT NULL DEFAULT 0 COMMENT  \'是否有额外明细\' after log_detail');


