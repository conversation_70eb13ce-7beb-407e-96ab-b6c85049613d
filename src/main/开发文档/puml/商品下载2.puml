@startuml


class GoodsMatchEventHandler
class BaseGoodsMatchProcess
class BaseGoodsMatchProcessProfile
class GoodsMatchByDbProcess

class DownloadOrderGoodsMatchProcess
class DownloadProductGoodsMatchProcess
class DownloadRefundGoodsMatchProcess


GoodsMatchEventHandler ..> BaseGoodsMatchProcess : 依赖(局部变量)
BaseGoodsMatchProcess ..> GoodsMatchByDbProcess : 关联(成员变量)
BaseGoodsMatchProcess ..> BaseGoodsMatchProcessProfile : 关联(成员变量)
DownloadOrderGoodsMatchProcess --|> BaseGoodsMatchProcess : 继承
DownloadProductGoodsMatchProcess --|> BaseGoodsMatchProcess : 继承
DownloadRefundGoodsMatchProcess --|> BaseGoodsMatchProcess : 继承

class BaseGoodsMatchProcess{
 public void handle(); 匹配入口
 protected ApiGoodsMatchTypeEnum getApiGoodsMatchTypeEnum(); 获取匹配方式
 protected boolean isNeedInvalidGoodsMatch(); 匹配方式是否需要作废匹配关系
}

class DownloadOrderGoodsMatchProcess{
 protected ApiGoodsMatchTypeEnum getApiGoodsMatchTypeEnum(); 获取匹配方式
}

class DownloadProductGoodsMatchProcess{
 protected ApiGoodsMatchTypeEnum getApiGoodsMatchTypeEnum(); 获取匹配方式
}

class DownloadRefundGoodsMatchProcess{
 protected ApiGoodsMatchTypeEnum getApiGoodsMatchTypeEnum(); 获取匹配方式
 protected boolean isNeedInvalidGoodsMatch(); 匹配方式是否需要作废匹配关系
}

class BaseGoodsMatchProcessProfile{
 public String getGoodsMatchRedisKey(GoodsMatchRequestDto.GoodsMatchRequestItemDto goodsMatchRequestItemDto); 获取匹配缓存唯一键
 protected String getGoodsMatchRedisKey(OnlineGoodsMatchEntity onlineGoodsMatchEntity); 获取匹配缓存唯一键
}

@enduml