package com.differ.wdgj.api.user.biz.domain.aftersale.save.data;

import com.differ.wdgj.api.user.biz.infrastructure.data.dto.bizfeature.plugins.AfterSalesConfigContent;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.bizfeature.plugins.LoadAfterSalesConfigContent;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.AfterSalesShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.DownloadOrderShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.load.OrderTriggerTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shop.ShopTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.plat.AfterSaleConfigBizFeatureUtils;
import com.differ.wdgj.api.user.biz.infrastructure.utils.plat.LoadAfterSaleBizFeatureUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 订单保存的上下文
 *
 * <AUTHOR>
 * @date 2024-05-30 14:16
 */
public class AfterSaleSaveContext {
    //region 属性
    /**
     * 会员名。
     */
    private String memberName;

    /**
     * 店铺id
     * 会员库g_cfg_shopList
     */
    private Integer shopId;

    /**
     * 平台
     */
    private PolyPlatEnum plat;

    /**
     * 订单触发方式。
     */
    private OrderTriggerTypeEnum orderTriggerType;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 是否写入业务日志。
     */
    private boolean writeBusinessLog;

    /**
     * 菠萝派请求id。
     */
    private String polyApiRequestId;

    /**
     * 下载时间。
     */
    private LocalDateTime loadTime;

    /**
     * 店铺基础信息
     */
    private ApiShopBaseDto apiShopBaseInfo;

    /**
     * 售后店铺配置
     */
    private AfterSalesShopConfig afterSalesShopConfig;

    /**
     * 下载订单店铺配置
     */
    private DownloadOrderShopConfig downLoadOrderShopConfig;
    //endregion

    //region 常量
    /**
     * 下载售后单平台业务特性
     */
    private final Map<String, LoadAfterSalesConfigContent> downloadPlatFeatureMap = new HashMap<>();
    /**
     * 售后单配置平台业务特性
     */
    private final Map<String, AfterSalesConfigContent> configPlatFeatureMap = new HashMap<>();
    //endregion

    //region 公共方法

    //region 平台业务特性 - 售后单下载
    /**
     * 获取下载售后平台业务特性</p>
     *
     * @return 售后平台业务特性
     */
    public LoadAfterSalesConfigContent getDownloadAfterSalesPlatFeature() {
        return getDownloadAfterSalesPlatFeature(null);
    }

    /**
     * 获取下载售后平台业务特性</p>
     *
     * @param restraintKey 业务配置键
     * @return 售后平台业务特性
     */
    public LoadAfterSalesConfigContent getDownloadAfterSalesPlatFeature(String restraintKey) {
        if (apiShopBaseInfo == null) {
            return null;
        }
        // default 是由于platFeatureMap的key不能为空字符串
        if(StringUtils.isEmpty(restraintKey)){
            restraintKey = ShopTypeEnum.DEFAULT.getCode();
        }
        return downloadPlatFeatureMap.computeIfAbsent(restraintKey, key -> {
            // 反向替换default
            String keyNoDefault = ShopTypeEnum.DEFAULT.getCode().equalsIgnoreCase(key)
                    ? StringUtils.EMPTY
                    : key;
            return LoadAfterSaleBizFeatureUtils.getFeature(apiShopBaseInfo, keyNoDefault);
        });
    }
    //endregion

     //region 平台业务特性 - 售后单配置
    /**
     * 获取售后单配置平台业务特性</p>
     * 优先级高于店铺配置{@link AfterSalesShopConfig}
     *
     * @return 售后平台业务特性
     */
    public AfterSalesConfigContent getAfterSalesConfigPlatFeature(String restraintKey) {
        if (apiShopBaseInfo == null) {
            return null;
        }
        // default 是由于platFeatureMap的key不能为空字符串
        if(StringUtils.isEmpty(restraintKey)){
            restraintKey = ShopTypeEnum.DEFAULT.getCode();
        }
        return configPlatFeatureMap.computeIfAbsent(restraintKey, key -> {
            // 反向替换default
            String keyNoDefault = ShopTypeEnum.DEFAULT.getCode().equalsIgnoreCase(key)
                    ? StringUtils.EMPTY
                    : key;
            return AfterSaleConfigBizFeatureUtils.getFeature(apiShopBaseInfo, keyNoDefault);
        });
    }
    //endregion

    //endregion

    //region get/set

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public OrderTriggerTypeEnum getOrderTriggerType() {
        return orderTriggerType;
    }

    public void setOrderTriggerType(OrderTriggerTypeEnum orderTriggerType) {
        this.orderTriggerType = orderTriggerType;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public boolean isWriteBusinessLog() {
        return writeBusinessLog;
    }

    public void setWriteBusinessLog(boolean writeBusinessLog) {
        this.writeBusinessLog = writeBusinessLog;
    }

    public String getPolyApiRequestId() {
        return polyApiRequestId;
    }

    public void setPolyApiRequestId(String polyApiRequestId) {
        this.polyApiRequestId = polyApiRequestId;
    }

    public ApiShopBaseDto getApiShopBaseInfo() {
        return apiShopBaseInfo;
    }

    public void setApiShopBaseInfo(ApiShopBaseDto apiShopBaseInfo) {
        this.apiShopBaseInfo = apiShopBaseInfo;
    }

    public AfterSalesShopConfig getAfterSalesShopConfig() {
        return afterSalesShopConfig;
    }

    public void setAfterSalesShopConfig(AfterSalesShopConfig afterSalesShopConfig) {
        this.afterSalesShopConfig = afterSalesShopConfig;
    }

    public DownloadOrderShopConfig getDownLoadOrderShopConfig() {
        return downLoadOrderShopConfig;
    }

    public void setDownLoadOrderShopConfig(DownloadOrderShopConfig downLoadOrderShopConfig) {
        this.downLoadOrderShopConfig = downLoadOrderShopConfig;
    }

    public PolyPlatEnum getPlat() {
        return plat;
    }

    public void setPlat(PolyPlatEnum plat) {
        this.plat = plat;
    }

    public LocalDateTime getLoadTime() {
        return loadTime;
    }

    public void setLoadTime(LocalDateTime loadTime) {
        this.loadTime = loadTime;
    }

    public Map<String, LoadAfterSalesConfigContent> getDownloadPlatFeatureMap() {
        return downloadPlatFeatureMap;
    }
    //endregion
}
