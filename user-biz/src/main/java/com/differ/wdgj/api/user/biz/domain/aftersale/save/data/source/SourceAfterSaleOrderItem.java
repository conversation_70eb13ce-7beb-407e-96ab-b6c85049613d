package com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source;

/**
 * 平台订单数据源 + 已存在订单数据信息
 *
 * <AUTHOR>
 * @date 2024-06-06 20:54
 */
public class SourceAfterSaleOrderItem<T> {
    //region 构造
    /**
     * 构造
     *
     * @param afterSaleNo 售后单号
     * @param plotOrder   菠萝派售后单
     */
    public SourceAfterSaleOrderItem(String afterSaleNo, String platOrderNo, T plotOrder) {
        this.afterSaleNo = afterSaleNo;
        this.platOrderNo = platOrderNo;
        this.ployOrder = plotOrder;
        this.dbOrderExt = new DbAfterSaleOrderExtItem();
    }
    /**
     * 构造
     *
     * @param afterSaleNo 售后单号
     * @param bizType     售后业务类型
     * @param plotOrder   菠萝派售后单
     */
    public SourceAfterSaleOrderItem(String afterSaleNo, String platOrderNo, AfterSaleSaveBizType bizType, T plotOrder) {
        this.afterSaleNo = afterSaleNo;
        this.platOrderNo = platOrderNo;
        this.bizType = bizType;
        this.ployOrder = plotOrder;
        this.dbOrderExt = new DbAfterSaleOrderExtItem();
    }
    //endregion

    /**
     * 售后单号
     */
    private final String afterSaleNo;

    /**
     * 原始单号
     */
    private final String platOrderNo;
    /**
     * 平台订单数据源
     */
    private final T ployOrder;

    /**
     * 售后业务类型
     */
    private AfterSaleSaveBizType bizType;

    /**
     * 基础已存在售后单信息
     */
    private DbAfterSaleOrderItem dbOrder;

    /**
     * 扩展已存在售后单信息
     */
    private DbAfterSaleOrderExtItem dbOrderExt;

    /**
     * 售后单hash对象
     */
    private AfterSaleOrderHashItem hashCode;

    //region get/set

    public String getAfterSaleNo() {
        return afterSaleNo;
    }

    public String getPlatOrderNo() {
        return platOrderNo;
    }

    public T getPloyOrder() {
        return ployOrder;
    }

    public DbAfterSaleOrderItem getDbOrder() {
        return dbOrder;
    }

    public void setDbOrder(DbAfterSaleOrderItem dbOrder) {
        this.dbOrder = dbOrder;
    }

    public AfterSaleSaveBizType getBizType() {
        return bizType;
    }

    public void setBizType(AfterSaleSaveBizType bizType) {
        this.bizType = bizType;
    }

    public AfterSaleOrderHashItem getHashCode() {
        return hashCode;
    }

    public void setHashCode(AfterSaleOrderHashItem hashCode) {
        this.hashCode = hashCode;
    }

    public DbAfterSaleOrderExtItem getDbOrderExt() {
        return dbOrderExt;
    }

    public void setDbOrderExt(DbAfterSaleOrderExtItem dbOrderExt) {
        this.dbOrderExt = dbOrderExt;
    }
    //endregion
}
