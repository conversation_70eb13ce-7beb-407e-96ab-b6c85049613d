package com.differ.wdgj.api.user.biz.domain.aftersale.save.operation;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.RefundPayOrderSendStatusEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.DbAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundGoodsStatusEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolySpecialRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.order.PolyOrderStatusEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundResponseRefundGoodInfo;
import com.differ.wdgj.api.user.biz.domain.order.common.data.OrderGoodsSendInfoDto;
import com.differ.wdgj.api.user.biz.domain.order.common.data.OrderSendInfoDto;
import com.differ.wdgj.api.user.biz.domain.order.common.operation.OrderSendStatusOperation;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeGoodsDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.bizfeature.plugins.AfterSalesConfigContent;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shop.ShopTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiAfterSaleTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 退货退款单/仅退款单类型转换操作类
 *
 * <AUTHOR>
 * @date 2025/6/18 14:04
 */
public class RefundTypeCovertOperation {
    //region 构造
    private RefundTypeCovertOperation() {
    }
    //endregion

    //region 单例
    /**
     * 枚举单例
     *
     * @return 配置键Redis缓存单例
     */
    public static RefundTypeCovertOperation singleton() {
        return RefundTypeCovertOperation.SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        /**
         * 单例
         */
        SINGLETON;

        private final RefundTypeCovertOperation instance;

        private SingletonEnum() {
            instance = new RefundTypeCovertOperation();
        }
    }
    //endregion

    //region 公共方法
    /**
     * 转换api售后业务类型
     *
     * @param context   上下文
     * @param shopType  店铺类别
     * @param ployOrder 菠萝派售后单
     * @param dbOrder   数据库订单信息
     * @return api售后业务类型
     */
    public ApiAfterSaleTypeEnum covertApiAfterSaleType(AfterSaleSaveContext context, ShopTypeEnum shopType, BusinessGetRefundOrderResponseOrderItem ployOrder, DbAfterSaleOrderItem dbOrder) {
        // 特殊售后类型（SpecialRefundType）
        ApiAfterSaleTypeEnum apiAfterSaleType = polySpecialRefundTypeCovert(ployOrder);

        // 基础售后类型转换
        if (apiAfterSaleType == null) {
            apiAfterSaleType = polyRefundTypeCovert(context, shopType, ployOrder);
        }

        // 仅退款区分已发货/未发货兼容逻辑
        if (apiAfterSaleType == ApiAfterSaleTypeEnum.REFUND_PAY) {
            AfterSalesConfigContent afterSalesConfigPlatFeature = context.getAfterSalesConfigPlatFeature(shopType.getCode());
            if (afterSalesConfigPlatFeature != null && afterSalesConfigPlatFeature.getBDistinguishShippedAndUnshipped()) {
                RefundPayOrderSendStatusEnum refundPayOrderSendStatusEnum = checkIsOrderSend(ployOrder, dbOrder.getApiTrade(), dbOrder.getApiTradeGoodsList());
                switch (refundPayOrderSendStatusEnum) {
                    case SEND:
                        apiAfterSaleType = ApiAfterSaleTypeEnum.REFUND_PAY_SEND;
                        break;
                    case NOT_SEND:
                        apiAfterSaleType = ApiAfterSaleTypeEnum.REFUND_PAY_NOT_SEND;
                        break;
                }
            }
        }

        return apiAfterSaleType;
    }
    //endregion

    //region 私有方法
    /**
     * 特殊售后类型（SpecialRefundType）转换
     *
     * @param ployOrder 菠萝派售后单
     * @return api售后业务类型
     */
    private ApiAfterSaleTypeEnum polySpecialRefundTypeCovert(BusinessGetRefundOrderResponseOrderItem ployOrder) {
        String specialRefundType = ployOrder.getSpecialRefundType();

        // 价保订单
        if (StringUtils.equalsIgnoreCase(specialRefundType, PolySpecialRefundTypeEnum.JH_PRICE_PROTECT.getCode())) {
            return ApiAfterSaleTypeEnum.REFUND_BJ;
        }

        return null;
    }

    /**
     * 基础售后类型（RefundType）转换
     *
     * @param context   上下文
     * @param shopType  店铺类别
     * @param ployOrder 菠萝派售后单
     * @return api售后业务类型
     */
    private ApiAfterSaleTypeEnum polyRefundTypeCovert(AfterSaleSaveContext context, ShopTypeEnum shopType, BusinessGetRefundOrderResponseOrderItem ployOrder) {
        // 未知refundType
        PolyRefundTypeEnum polyRefundType = PolyRefundTypeEnum.create(ployOrder.getRefundType());
        if (polyRefundType == null) {
            return ployOrder.getHasGoodsReturn()
                    ? ApiAfterSaleTypeEnum.REFUND
                    : ApiAfterSaleTypeEnum.REFUND_PAY;
        }

        // 商品状态以及订单状态转换
        PolyRefundGoodsStatusEnum polyGoodsStatus = PolyRefundGoodsStatusEnum.create(ployOrder.getGoodsStatus());
        PolyOrderStatusEnum polyOrderStatus = PolyOrderStatusEnum.create(ployOrder.getOrderStatus());

        // 退货单
        if (polyRefundType == PolyRefundTypeEnum.JH_02) {
            return ApiAfterSaleTypeEnum.REFUND_GOODS;
        }
        // 退货退款单
        if (polyRefundType == PolyRefundTypeEnum.JH_04) {
            return ApiAfterSaleTypeEnum.REFUND;
        }
        // 仅退款
        if (polyRefundType == PolyRefundTypeEnum.JH_01 || polyRefundType == PolyRefundTypeEnum.JH_03) {
            // 特定平台场景下，菠萝派会将JH_03/JH_01视为已发货仅退款，JH_BEFORE_SEND_REFUND视为未发货仅退款
            AfterSalesConfigContent afterSalesConfigPlatFeature = context.getAfterSalesConfigPlatFeature(shopType.getCode());
            if (afterSalesConfigPlatFeature != null && afterSalesConfigPlatFeature.getBPolyRefundPayRegardedRefundPaySend()) {
                return ApiAfterSaleTypeEnum.REFUND_PAY_SEND;
            }

            // 是否发货判断
            if (polyOrderStatus != PolyOrderStatusEnum.OTHER) {
                if (polyOrderStatus == PolyOrderStatusEnum.WAITING_SELLER_SHIPMENT) {
                    return ApiAfterSaleTypeEnum.REFUND_PAY_NOT_SEND;
                } else if (polyOrderStatus == PolyOrderStatusEnum.WAITING_BUYER_CONFIRM ||
                        polyOrderStatus == PolyOrderStatusEnum.TRADE_SUCCESS) {
                    return ApiAfterSaleTypeEnum.REFUND_PAY_SEND;
                }
            }

            // 是否收货判断
            if (polyGoodsStatus != PolyRefundGoodsStatusEnum.OTHER) {
                if (polyGoodsStatus == PolyRefundGoodsStatusEnum.BUYER_NOT_RECEIVED) {
                    // 在途仅退款
                    return ApiAfterSaleTypeEnum.REFUND_PAY_TRANSIT;
                } else if (polyGoodsStatus == PolyRefundGoodsStatusEnum.BUYER_RECEIVED ||
                        polyGoodsStatus == PolyRefundGoodsStatusEnum.BUYER_RETURNED) {
                    // 已收货仅退款
                    return ApiAfterSaleTypeEnum.REFUND_PAY_RECEIVE;
                }
            }
            return ApiAfterSaleTypeEnum.REFUND_PAY;
        }
        // 未发货仅退款
        if (polyRefundType == PolyRefundTypeEnum.JH_BEFORE_SEND_REFUND) {
            return ApiAfterSaleTypeEnum.REFUND_PAY_NOT_SEND;
        }
        // 价保售后单
        if (polyRefundType == PolyRefundTypeEnum.JH_10) {
            return ApiAfterSaleTypeEnum.REFUND_BJ;
        }
        // 补寄售后单
        if (polyRefundType == PolyRefundTypeEnum.JH_SUPPLEMENT_SEND) {
            return ApiAfterSaleTypeEnum.REFUND_SUPPLEMENT;
        }
        // 维修单
        if (polyRefundType == PolyRefundTypeEnum.JH_WEIXIU) {
            return ApiAfterSaleTypeEnum.REPAIR;
        }

        return null;
    }

    /**
     * 检查仅退款单是否发货
     *
     * @param ployOrder         菠萝派售后单
     * @param apiTrade          原始单
     * @param apiTradeGoodsList 原始单商品列表
     * @return 结果
     */
    private RefundPayOrderSendStatusEnum checkIsOrderSend(BusinessGetRefundOrderResponseOrderItem ployOrder, ApiTradeDO apiTrade, List<ApiTradeGoodsDO> apiTradeGoodsList) {
        // 基础数据校验
        if (apiTrade == null || CollectionUtils.isEmpty(apiTradeGoodsList)) {
            return RefundPayOrderSendStatusEnum.UNKNOWN;
        }

        // 获取订单发货同步结果
        OrderSendInfoDto orderSendInfo = OrderSendStatusOperation.getOrderSendStatus(apiTrade, apiTradeGoodsList);
        // 全部发货则返回true
        if (orderSendInfo.isbAllSend()) {
            return RefundPayOrderSendStatusEnum.SEND;
        }
        // 未全部发货检测商品级，注意：当前逻辑部分发货视为未发货
        if (CollectionUtils.isEmpty(ployOrder.getRefundGoods())) {
            return RefundPayOrderSendStatusEnum.UNKNOWN;
        }
        List<Boolean> goodsSendResults = new ArrayList<>();
        for (BusinessGetRefundResponseRefundGoodInfo refundGoods : ployOrder.getRefundGoods()) {
            // 查找原始单商品
            ApiTradeGoodsDO apiTradeGoods = apiTradeGoodsList.stream().filter(x -> AfterSaleGoodsMatchOperation.isRefundMatchApiTradeGoods(x, ployOrder, refundGoods)).findFirst().orElse(null);
            if (apiTradeGoods == null) {
                return RefundPayOrderSendStatusEnum.UNKNOWN;
            }
            // 查找原始单商品发货结果
            OrderGoodsSendInfoDto orderGoodsSendInfo = orderSendInfo.getOrderGoodsSendStatusList().stream().filter(x -> x.getRecId() == apiTradeGoods.getRecId()).findFirst().orElse(null);
            if (orderGoodsSendInfo == null) {
                return RefundPayOrderSendStatusEnum.UNKNOWN;
            }
            // 发货结果赋值
            goodsSendResults.add(orderGoodsSendInfo.isbAllSend());
        }

        return goodsSendResults.stream().allMatch(BooleanUtils::isTrue)
                ? RefundPayOrderSendStatusEnum.SEND
                : RefundPayOrderSendStatusEnum.NOT_SEND;
    }
    //endregion
}
