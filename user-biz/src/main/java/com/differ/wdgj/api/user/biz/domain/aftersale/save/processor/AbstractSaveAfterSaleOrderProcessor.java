package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.PolyAfterSaleBasicInfo;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.AbstractAfterSaleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.SaveAfterSaleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.*;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.AfterSaleSaveBizType;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.DbAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetOutNoticeItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetSaveOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.notice.IOutAfterSaleNoticeOperation;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.notice.OutAfterSaleNoticeOperation;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.notice.handle.IAfterSaleNotice;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.savedata.AfterSaleDataOperation;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.savedata.IAfterSaleDataOperation;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 保存售后订单基础处理器
 *
 * <AUTHOR>
 * @date 2024-06-06 11:51
 */
public abstract class AbstractSaveAfterSaleOrderProcessor<T> implements ISaveAfterSaleOrder<T> {
    //region 常量

    /**
     * 上下文
     */
    protected AfterSaleSaveContext context;

    /**
     * 数据落地操作类
     */
    protected IAfterSaleDataOperation afterSaleDataOperation;

    /**
     * 订单外部消息通知器
     */
    protected IOutAfterSaleNoticeOperation outNoticeOperation;

    //endregion

    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    protected AbstractSaveAfterSaleOrderProcessor(AfterSaleSaveContext context) {
        init(context);
    }
    //endregion

    //region 接口实现

    /**
     * 初始化
     */
    @Override
    public void init(AfterSaleSaveContext context) {
        this.context = context;
        this.afterSaleDataOperation = new AfterSaleDataOperation(this.context);
        this.outNoticeOperation = new OutAfterSaleNoticeOperation(this.context);
    }

    /**
     * 保存订单
     *
     * @param ployOrders 订单数据
     * @return 订单保存结果
     */
    @Override
    public final SaveAfterSaleResult<List<SaveOrderResultComposite>> saveOrder(List<T> ployOrders) {
        // 初始化
        Map<String, SaveOrderResultComposite> compositeMap = new HashMap<>(ployOrders.size());
        Map<String, SourceAfterSaleOrderItem<T>> sourceOrderMap = new HashMap<>(ployOrders.size());
        ployOrders.forEach(ployOrder -> {
            PolyAfterSaleBasicInfo polyBasicInfo = getPolyAfterSaleBasicInfo(ployOrder);
            if (polyBasicInfo != null && StringUtils.isNotEmpty(polyBasicInfo.getAfterSaleNo())) {
                // 售后单基础信息
                String afterSaleNo = polyBasicInfo.getAfterSaleNo();
                String platOrderNo = polyBasicInfo.getPlatOrderNo();
                // 返回结果
                compositeMap.put(afterSaleNo, new SaveOrderResultComposite(afterSaleNo));
                sourceOrderMap.put(afterSaleNo, new SourceAfterSaleOrderItem<>(afterSaleNo, platOrderNo, ployOrder));
            }
        });

        // 1、hashCode校验
        SaveAfterSaleResult<List<CheckAfterSaleHashCodeResult>> checkHashCodeResults = afterSaleDataOperation.checkPolyOrderHashCode(new ArrayList<>(sourceOrderMap.values()));
        if (checkHashCodeResults.isFailed()) {
            return SaveAfterSaleResult.failed(checkHashCodeResults.getMessage());
        }
        // 获取hashCode校验通过的售后单数据
        List<CheckAfterSaleHashCodeResult> checkSuccessHashCodeResults = checkHashCodeResults.getContent().stream().filter(AbstractAfterSaleResult::isSuccess).collect(Collectors.toList());

        // 2、历史基础数据查询
        List<SourceAfterSaleOrderItem<T>> needQueryDbOrders = combineNeedQueryDbOrder(checkSuccessHashCodeResults, sourceOrderMap);
        SaveAfterSaleResult<List<QueryDbOrderResult>> getOldDbOrderResults = afterSaleDataOperation.getOldDbOrderItems(needQueryDbOrders);
        if (getOldDbOrderResults.isFailed()) {
            return SaveAfterSaleResult.failed(getOldDbOrderResults.getMessage());
        }

        // 3、外部订单数据过滤/转换
        List<SourceAfterSaleOrderItem<T>> needCovertSourceOrders = combineNeedCovertOrder(checkSuccessHashCodeResults, getOldDbOrderResults.getContent(), sourceOrderMap);
        SaveAfterSaleResult<List<FilterAndConvertOrderResult>> filterAndConvertResults = filterAndConvertPolyOrder(needCovertSourceOrders);
        if (filterAndConvertResults.isFailed()) {
            return SaveAfterSaleResult.failed(filterAndConvertResults.getMessage());
        }

        // 4、订单数据落地
        List<TargetSaveOrderItem> needSaveOrder = combineNeedSaveOrder(filterAndConvertResults.getContent(), sourceOrderMap);
        SaveAfterSaleResult<List<SaveOrderDataResult>> saveDataResults = afterSaleDataOperation.saveData(needSaveOrder);
        if (saveDataResults.isFailed()) {
            return SaveAfterSaleResult.failed(saveDataResults.getMessage());
        }

        // 5、异步外部消息通知
        List<IAfterSaleNotice> outNoticeOrders = new ArrayList<>();
        filterAndConvertResults.getContent().stream().filter(x -> x.getTargetOrder() != null).forEach(x -> outNoticeOrders.addAll(x.getTargetOrder().getNotices()));
        outNoticeOperation.notice(new TargetOutNoticeItem(outNoticeOrders, saveDataResults.getContent()));

        // 6、订单级结果整合
        compositeMap.forEach((afterSaleNo, composite) -> {
            CheckAfterSaleHashCodeResult checkHashCodeResult = checkHashCodeResults.getContent().stream().filter(x -> StringUtils.equals(afterSaleNo, x.getAfterSaleNo())).findFirst().orElse(null);
            if (checkHashCodeResult != null) {
                composite.setCheckAfterSaleHashCodeResult(checkHashCodeResult);
            }
            QueryDbOrderResult getOldDbOrderResult = getOldDbOrderResults.getContent().stream().filter(x -> StringUtils.equals(afterSaleNo, x.getAfterSaleNo())).findFirst().orElse(null);
            if (getOldDbOrderResult != null) {
                composite.setGetOldDbOrderResult(getOldDbOrderResult);
            }
            FilterAndConvertOrderResult filterAndConvertResult = filterAndConvertResults.getContent().stream().filter(x -> StringUtils.equals(afterSaleNo, x.getAfterSaleNo())).findFirst().orElse(null);
            if (filterAndConvertResult != null) {
                composite.setConvertOrderResult(filterAndConvertResult);
            }
            SaveOrderDataResult saveOrderDataResult = saveDataResults.getContent().stream().filter(x -> StringUtils.equals(afterSaleNo, x.getAfterSaleNo())).findFirst().orElse(null);
            if (saveOrderDataResult != null) {
                composite.setSaveOrderDataResult(saveOrderDataResult);
            }
        });

        return SaveAfterSaleResult.success(new ArrayList<>(compositeMap.values()));
    }

    //endregion

    //region 供子类重写方法

    /**
     * 获取订单级业务类型
     *
     * @param orderItem 菠萝派售后单信息
     * @param dbOrder   数据库订单信息
     * @return 售后单号
     */
    protected AfterSaleSaveBizType getBizType(T orderItem, DbAfterSaleOrderItem dbOrder) {
        return AfterSaleSaveBizType.getDefaultBizType();
    }

    /**
     * 获取菠萝派售后单基础信息
     *
     * @param orderItem 菠萝派售后单
     * @return 菠萝派售后单基础信息
     */
    protected abstract PolyAfterSaleBasicInfo getPolyAfterSaleBasicInfo(T orderItem);

    /**
     * 获取售后订单
     *
     * @param sourceAfterSaleOrders 原始售后单列表
     * @return 售后订单列表
     */
    protected abstract SaveAfterSaleResult<List<FilterAndConvertOrderResult>> filterAndConvertPolyOrder(List<SourceAfterSaleOrderItem<T>> sourceAfterSaleOrders);

    //endregion

    //region 私有方法

    /**
     * 组合需要查询db订单信息订单列表
     *
     * @param checkSuccessHashCodeResults hash校验通过的售后单
     * @param sourceOrderMap              原始售后单Map
     * @return 需要查询db订单信息订单列表
     */
    private List<SourceAfterSaleOrderItem<T>> combineNeedQueryDbOrder(List<CheckAfterSaleHashCodeResult> checkSuccessHashCodeResults, Map<String, SourceAfterSaleOrderItem<T>> sourceOrderMap) {
        List<SourceAfterSaleOrderItem<T>> needQueryDbOrders = new ArrayList<>();
        checkSuccessHashCodeResults.forEach(x -> {
            SourceAfterSaleOrderItem<T> sourceOrderItem = sourceOrderMap.get(x.getAfterSaleNo());
            if (sourceOrderItem != null) {
                needQueryDbOrders.add(sourceOrderItem);
            }
        });

        return needQueryDbOrders;
    }

    /**
     * 组合需要转换原始售后单列表
     *
     * @param checkSuccessHashCodeResults hash校验通过的售后单
     * @param oldDbOrderItems             历史订单数据
     * @param sourceOrderMap              原始售后单Map
     * @return 原始售后单列表
     */
    private List<SourceAfterSaleOrderItem<T>> combineNeedCovertOrder(List<CheckAfterSaleHashCodeResult> checkSuccessHashCodeResults, List<QueryDbOrderResult> oldDbOrderItems, Map<String, SourceAfterSaleOrderItem<T>> sourceOrderMap) {
        List<SourceAfterSaleOrderItem<T>> needCovertSourceOrders = new ArrayList<>();
        checkSuccessHashCodeResults.forEach(x -> {
            SourceAfterSaleOrderItem<T> sourceOrderItem = sourceOrderMap.get(x.getAfterSaleNo());
            if (sourceOrderItem != null) {
                // 注入hashCode
                sourceOrderItem.setHashCode(x.getOrderHashCode());
                // 写入历史订单信息
                QueryDbOrderResult queryDbOrderResult = oldDbOrderItems.stream().filter(y -> y.getAfterSaleNo().equals(x.getAfterSaleNo())).findFirst().orElse(null);
                if (queryDbOrderResult != null) {
                    sourceOrderItem.setDbOrder(queryDbOrderResult.getDbOrder());
                }
                // 写入业务类型
                sourceOrderItem.setBizType(getBizType(sourceOrderItem.getPloyOrder(), sourceOrderItem.getDbOrder()));
                needCovertSourceOrders.add(sourceOrderItem);
            }
        });

        return needCovertSourceOrders;
    }

    /**
     * 组合需要保存的售后单信息
     *
     * @param convertOrderResults 转换售后单结果
     * @param sourceOrderMap      原始售后单Map
     * @return 需要保存的售后单信息
     */
    private List<TargetSaveOrderItem> combineNeedSaveOrder(List<FilterAndConvertOrderResult> convertOrderResults, Map<String, SourceAfterSaleOrderItem<T>> sourceOrderMap) {
        List<TargetSaveOrderItem> needSaveOrder = new ArrayList<>();
        convertOrderResults.forEach(covertResult -> {
            // 转换成功地售后单才保存
            if (covertResult.isFailed()) {
                return;
            }
            SourceAfterSaleOrderItem<T> sourceOrderItem = sourceOrderMap.get(covertResult.getAfterSaleNo());
            if (sourceOrderItem == null) {
                return;
            }

            needSaveOrder.add(TargetSaveOrderItem.create(covertResult.getTargetOrder(), sourceOrderItem.getHashCode()));
        });
        return needSaveOrder;
    }

    //endregion
}
