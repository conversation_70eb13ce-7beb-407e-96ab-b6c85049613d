package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.PolyAfterSaleBasicInfo;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.AfterSaleProcessTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.SaveAfterSaleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.GoodsConvertHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.FilterAndConvertOrderResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.*;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.operation.AfterSaleGoodsMatchOperation;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.AbstractSaveAfterSaleOrderProcessor;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.batch.BatchQueryEncryptHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.batch.BatchQuerySysMatchHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.convert.goodsexchange.ExchangeGoodsCovert;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.convert.goodsexchange.ExchangeGoodsJammingCodeHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.convert.goodsexchange.ExchangeGoodsMatchHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.convert.goodsexchange.ExchangeGoodsPlatSpecialHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.convert.goodsrefund.RefundGoodsCovertToExchangeHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.convert.goodsrefund.RefundGoodsJammingCodeToExchangeHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.convert.goodsrefund.RefundGoodsMatchToExchangeHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.convert.goodsrefund.RefundGoodsPlatSpecialToExchangeHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.convert.order.ExchangeOrderCovertHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.convert.order.ExchangePlatSpecialOrderCovertHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.convert.post.ExchangeOrderChangeProcessHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.convert.post.ExchangeOrderGoodsDeleteHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.convert.post.ExchangePlatSpecialPostProcessHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.filter.ExchangeHisFilterHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.filter.ExchangePlatSpecialFilterHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.filter.ExchangeTypeFilterHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.filter.ExchangePolyErrorFilterHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.ExchangeHandleComposite;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.*;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeResponseExchangeGoodInfo;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeResponseRefundGoodInfo;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailTwoDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeGoodsDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shop.ShopTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiAfterSaleTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * 保存换货单处理器
 *
 * <AUTHOR>
 * @date 2024/8/7 上午11:14
 */
public class BaseSaveExchangeOrderProcessor extends AbstractSaveAfterSaleOrderProcessor<BusinessGetExchangeOrderResponseOrderItem> {
    //region 变量
    /**
     * 日志
     */
    private final Logger log = LoggerFactory.getLogger(BaseSaveExchangeOrderProcessor.class);

    /**
     * 插件组合
     */
    private ExchangeHandleComposite<BusinessGetExchangeOrderResponseOrderItem, BusinessGetExchangeResponseRefundGoodInfo, BusinessGetExchangeResponseExchangeGoodInfo> exchangeHandleComposite;
    //endregion

    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public BaseSaveExchangeOrderProcessor(AfterSaleSaveContext context) {
        super(context);
    }

    //endregion

    //region 重写基类方法

    /**
     * 初始化
     *
     * @param context 上下文
     */
    @Override
    public void init(AfterSaleSaveContext context) {
        super.init(context);

        //region 插件初始化
        // 前置批量查询插件列表
        List<IPerBatchProcessOrderHandle<BusinessGetExchangeOrderResponseOrderItem>> perBatchQueryOrders = createPerBatchQueryHandles();
        // 构建前置过滤插件列表
        List<IPreFiltrationOrderHandle<BusinessGetExchangeOrderResponseOrderItem>> subPreFilters = createPreFiltrationHandles();
        // 构建售后单单转换插件列表
        List<IOrderConvertHandle<BusinessGetExchangeOrderResponseOrderItem>> orderConverts = createOrderConvertHandles();
        // 售后单退货商品转换插件列表
        List<IRefundGoodsConvertHandle<BusinessGetExchangeOrderResponseOrderItem, BusinessGetExchangeResponseRefundGoodInfo>> refundGoodsConverts = createReturnGoodsConvertsHandles();
        // 售后单换货商品转换插件列表
        List<IExchangeGoodsConvertHandle<BusinessGetExchangeOrderResponseOrderItem, BusinessGetExchangeResponseExchangeGoodInfo>> exchangeGoodsConverts = createExchangeGoodsConvertsHandles();
        // 后置处理插件列表
        List<IPostProcessOrderHandle<BusinessGetExchangeOrderResponseOrderItem>> postProcesses = createPostProcessHandles();
        // 插件注入 插件组合
        exchangeHandleComposite = new ExchangeHandleComposite<>(perBatchQueryOrders, subPreFilters, orderConverts, refundGoodsConverts, exchangeGoodsConverts, postProcesses);
        //endregion
    }

    /**
     * 获取菠萝派售后单基础信息
     *
     * @param orderItem 菠萝派售后单
     * @return 菠萝派售后单基础信息
     */
    @Override
    protected PolyAfterSaleBasicInfo getPolyAfterSaleBasicInfo(BusinessGetExchangeOrderResponseOrderItem orderItem) {
        return new PolyAfterSaleBasicInfo(orderItem.getExchangeOrderNo(), orderItem.getPlatOrderNo());
    }

    /**
     * 获取订单级业务类型
     *
     * @param orderItem 菠萝派售后单信息
     * @return 售后单号
     */
    @Override
    protected AfterSaleSaveBizType getBizType(BusinessGetExchangeOrderResponseOrderItem orderItem) {
        return AfterSaleSaveBizType.build(ShopTypeEnum.DEFAULT, ApiAfterSaleTypeEnum.EXCHANGE);
    }

    /**
     * 过滤/转换售后订单列表
     *
     * @param sourceAfterSaleOrders 原始售后单列表
     * @return 结果
     */
    @Override
    protected final SaveAfterSaleResult<List<FilterAndConvertOrderResult>> filterAndConvertPolyOrder(List<SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem>> sourceAfterSaleOrders) {
        if (CollectionUtils.isEmpty(sourceAfterSaleOrders)) {
            return SaveAfterSaleResult.success(new ArrayList<>());
        }

        ArrayList<FilterAndConvertOrderResult> orderResults = new ArrayList<>();
        // 前置批量处理
        AfterSaleHandleResult preQueryResult = exchangeHandleComposite.preBatchProcess(sourceAfterSaleOrders);
        if (preQueryResult.isFailed()) {
            return SaveAfterSaleResult.failed(preQueryResult.getMessage());
        }

        // 订单级数据处理
        for (SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder : sourceAfterSaleOrders) {
            orderResults.add(convertPolyOrder(sourceOrder));
        }

        return SaveAfterSaleResult.success(orderResults);
    }

    //endregion

    //region 主流程

    /**
     * 过滤/转换订单级数据
     *
     * @param sourceOrder 原始售后单列表
     * @return 结果
     */
    private FilterAndConvertOrderResult convertPolyOrder(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder) {
        // 基础数据
        BusinessGetExchangeOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
        String afterSaleNo = ployOrder.getExchangeOrderNo();
        try {
            TargetCovertOrderItem targetOrder = new TargetCovertOrderItem();
            // 1、前置过滤订单
            AfterSaleHandleResult perFilterResult = exchangeHandleComposite.preFiltrationOrder(sourceOrder, targetOrder);
            if (perFilterResult.isFailed()) {
                return FilterAndConvertOrderResult.failed(afterSaleNo, perFilterResult.getMessage(), targetOrder.isNeedBizRetry());
            }

            // 2、转换订单级信息
            AfterSaleHandleResult orderConvertResult = exchangeHandleComposite.orderConvert(sourceOrder, targetOrder);
            if (orderConvertResult.isFailed()) {
                return FilterAndConvertOrderResult.failed(afterSaleNo, orderConvertResult.getMessage(), targetOrder.isNeedBizRetry());
            }

            // 3、转换退货商品级信息
            List<ApiReturnDetailDO> targetRefundGoodsList = new ArrayList<>();
            AfterSaleHandleResult convertRefundGoodsResult = convertPolyRefundGoodsList(sourceOrder, targetOrder, targetRefundGoodsList);
            if (convertRefundGoodsResult.isFailed()) {
                return FilterAndConvertOrderResult.failed(afterSaleNo, orderConvertResult.getMessage(), targetOrder.isNeedBizRetry());
            }
            targetOrder.getRefundGoods().addAll(targetRefundGoodsList);

            // 4、转换换货商品级信息
            List<ApiReturnDetailTwoDO> targetExchangeGoodsList = new ArrayList<>();
            AfterSaleHandleResult convertExchangeGoodsResult = convertPolyExchangeGoodsList(sourceOrder, targetOrder, targetExchangeGoodsList);
            if (convertExchangeGoodsResult.isFailed()) {
                return FilterAndConvertOrderResult.failed(afterSaleNo, orderConvertResult.getMessage(), targetOrder.isNeedBizRetry());
            }
            targetOrder.getExchangeGoods().addAll(targetExchangeGoodsList);

            // 5、后置处理
            AfterSaleHandleResult postProcessesResult = exchangeHandleComposite.postProcess(sourceOrder, targetOrder);
            if (postProcessesResult.isFailed()) {
                return FilterAndConvertOrderResult.failed(afterSaleNo, postProcessesResult.getMessage(), targetOrder.isNeedBizRetry());
            }

            // 结果整合
            AfterSaleProcessTypeEnum processType = targetOrder.getProcessType();
            boolean needBizRetry = targetOrder.isNeedBizRetry();
            return FilterAndConvertOrderResult.success(afterSaleNo, processType, targetOrder, needBizRetry);
        } catch (Exception e) {
            String logContent = String.format("【%s】【%s】换货单[%s]转换失败，失败原因:%s", context.getMemberName(), context.getShopId(), afterSaleNo, e.getMessage());
            log.error(logContent, e);
            return FilterAndConvertOrderResult.failed(afterSaleNo, logContent);
        }
    }

    /**
     * 批量转换退货商品
     *
     * @param sourceOrder           原始售后单数据
     * @param targetOrder           售后单
     * @param targetRefundGoodsList 目标售后退货商品列表
     * @return 结果
     */
    private AfterSaleHandleResult convertPolyRefundGoodsList(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder, final List<ApiReturnDetailDO> targetRefundGoodsList) {
        BusinessGetExchangeOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
        DbAfterSaleOrderItem dbOrder = sourceOrder.getDbOrder();
        List<BusinessGetExchangeResponseRefundGoodInfo> refundGoodsList = ployOrder.getRefundGoods();
        if (CollectionUtils.isNotEmpty(refundGoodsList)) {
            // 循环退货商品级转换商品信息
            for (BusinessGetExchangeResponseRefundGoodInfo refundGoods : refundGoodsList) {
                // 基础数据
                ApiReturnDetailDO targetRefundGoods = new ApiReturnDetailDO();
                ApiTradeGoodsDO apiTradeGoods = CollectionUtils.isNotEmpty(dbOrder.getApiTradeGoodsList())
                        ? dbOrder.getApiTradeGoodsList().stream().filter(x -> isMatchApiTradeGoods(x, refundGoods)).findFirst().orElse(null)
                        : null;
                SourceRefundGoodsItem<BusinessGetExchangeResponseRefundGoodInfo> sourceRefundsGoods = new SourceRefundGoodsItem<>(refundGoods, apiTradeGoods);
                // 转化商品
                GoodsConvertHandleResult goodsConvertResult = exchangeHandleComposite.refundGoodsConvert(sourceOrder, sourceRefundsGoods, targetOrder, targetRefundGoods);
                if (goodsConvertResult.isFailed()) {
                    return AfterSaleHandleResult.failed(goodsConvertResult.getMessage());
                }
                if (!goodsConvertResult.isSaveGoods()) {
                    continue;
                }
                targetRefundGoodsList.add(targetRefundGoods);
            }
        }
        return AfterSaleHandleResult.success();
    }

    /**
     * 批量转换换货商品
     *
     * @param sourceOrder           原始售后单数据
     * @param targetOrder           售后单
     * @param targetRefundGoodsList 目标售后换货商品列表
     * @return 结果
     */
    private AfterSaleHandleResult convertPolyExchangeGoodsList(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder, final List<ApiReturnDetailTwoDO> targetRefundGoodsList) {
        DbAfterSaleOrderExtItem dbOrderExt = sourceOrder.getDbOrderExt();
        BusinessGetExchangeOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
        List<BusinessGetExchangeResponseExchangeGoodInfo> exchangeGoodsList = ployOrder.getExchangeGoods();
        if (CollectionUtils.isNotEmpty(exchangeGoodsList)) {
            // 循环换货商品级转换商品信息
            for (BusinessGetExchangeResponseExchangeGoodInfo exchangeGoods : exchangeGoodsList) {
                // 基础数据
                ApiReturnDetailTwoDO targetExchangeGoods = new ApiReturnDetailTwoDO();
                AfterSaleGoodsSysMatchItem goodsSysMatchItem = dbOrderExt != null && CollectionUtils.isNotEmpty(dbOrderExt.getGoodsSysMatch())
                        ? dbOrderExt.getGoodsSysMatch().stream().filter(x -> isMatchApiGoodsSysMatch(x, exchangeGoods)).findFirst().orElse(null)
                        : null;
                SourceExchangeGoodsItem<BusinessGetExchangeResponseExchangeGoodInfo> sourceExchangeGoods = new SourceExchangeGoodsItem<>(exchangeGoods, goodsSysMatchItem);
                // 转化换货商品
                GoodsConvertHandleResult goodsConvertResult = exchangeHandleComposite.exchangeGoodsConvert(sourceOrder, sourceExchangeGoods, targetOrder, targetExchangeGoods);
                if (goodsConvertResult.isFailed()) {
                    return AfterSaleHandleResult.failed(goodsConvertResult.getMessage());
                }
                if (!goodsConvertResult.isSaveGoods()) {
                    continue;
                }
                targetRefundGoodsList.add(targetExchangeGoods);
            }
        }
        return AfterSaleHandleResult.success();
    }

    //endregion

    //region 子类可重写方法

    //region 前置批量查询插件

    /**
     * 创建前置批量查询插件
     */
    protected List<IPerBatchProcessOrderHandle<BusinessGetExchangeOrderResponseOrderItem>> createPerBatchQueryHandles() {
        List<IPerBatchProcessOrderHandle<BusinessGetExchangeOrderResponseOrderItem>> perBatchProcessList = new ArrayList<>();
        // 查询商品匹配信息
        perBatchProcessList.add(new BatchQuerySysMatchHandle(context));
        // 查询密文信息
        perBatchProcessList.add(new BatchQueryEncryptHandle(context));
        return perBatchProcessList;
    }
    //endregion

    //region 前置过滤

    /**
     * 创建前置过滤插件
     */
    protected List<IPreFiltrationOrderHandle<BusinessGetExchangeOrderResponseOrderItem>> createPreFiltrationHandles() {
        List<IPreFiltrationOrderHandle<BusinessGetExchangeOrderResponseOrderItem>> preFiltrationHandles = new ArrayList<>();
        // 换货单菠萝派返回错误过滤器
        preFiltrationHandles.add(new ExchangePolyErrorFilterHandle(context));
        // 换货单类别过滤器
        preFiltrationHandles.add(new ExchangeTypeFilterHandle(context));
        // 换货单归档过滤器
        preFiltrationHandles.add(new ExchangeHisFilterHandle(context));
        // 平台级特殊处理
        ExchangePlatSpecialFilterHandle platSpecialHandle = new ExchangePlatSpecialFilterHandle(context);
        platSpecialHandle.setCoveringMethod(this::preFiltrationOrder);
        preFiltrationHandles.add(platSpecialHandle);
        return preFiltrationHandles;
    }

    /**
     * 前置过滤订单
     *
     * @param sourceOrder 原始售后单数据
     * @return 结果
     */
    protected AfterSaleHandleResult preFiltrationOrder(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        // 供子类重写
        return AfterSaleHandleResult.success();
    }
    //endregion

    //region 转换订单级信息

    /**
     * 创建订单级数据转换插件
     */
    protected List<IOrderConvertHandle<BusinessGetExchangeOrderResponseOrderItem>> createOrderConvertHandles() {
        List<IOrderConvertHandle<BusinessGetExchangeOrderResponseOrderItem>> orderConvertHandles = new ArrayList<>();
        // 订单级基础数据转换
        orderConvertHandles.add(new ExchangeOrderCovertHandle(context));
        // 平台级特殊处理
        ExchangePlatSpecialOrderCovertHandle platSpecialHandle = new ExchangePlatSpecialOrderCovertHandle(context);
        platSpecialHandle.setCoveringMethod(this::orderConvert);
        orderConvertHandles.add(platSpecialHandle);
        return orderConvertHandles;
    }

    /**
     * 转换订单级信息
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    protected AfterSaleHandleResult orderConvert(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        // 供子类重写
        return AfterSaleHandleResult.success();
    }
    //endregion

    //region 转换退货商品级信息

    /**
     * 创建退货商品数据转换插件
     */
    protected List<IRefundGoodsConvertHandle<BusinessGetExchangeOrderResponseOrderItem, BusinessGetExchangeResponseRefundGoodInfo>> createReturnGoodsConvertsHandles() {
        List<IRefundGoodsConvertHandle<BusinessGetExchangeOrderResponseOrderItem, BusinessGetExchangeResponseRefundGoodInfo>> returnGoodsConverts = new ArrayList<>();
        // 换货单-退货商品基础数据转换
        returnGoodsConverts.add(new RefundGoodsCovertToExchangeHandle(context));
        // 平台级特殊处理
        RefundGoodsPlatSpecialToExchangeHandle platSpecialHandle = new RefundGoodsPlatSpecialToExchangeHandle(context);
        platSpecialHandle.setCoveringHandle(this::refundGoodsConvert);
        returnGoodsConverts.add(platSpecialHandle);
        // 换货单-退货商品编码掩码处理
        returnGoodsConverts.add(new RefundGoodsJammingCodeToExchangeHandle(context));
        // 换货单-退货商品商品匹配插件
        returnGoodsConverts.add(new RefundGoodsMatchToExchangeHandle(context));
        return returnGoodsConverts;
    }

    /**
     * 退货商品是否匹配原始单货品
     *
     * @param apiTradeGoods 原始单商品
     * @param refundGoods   退货商品
     * @return 结果
     */
    protected boolean isMatchApiTradeGoods(ApiTradeGoodsDO apiTradeGoods, BusinessGetExchangeResponseRefundGoodInfo refundGoods) {
        return AfterSaleGoodsMatchOperation.isExchangeMatchApiTradeGoods(apiTradeGoods, refundGoods);
    }

    /**
     * 转换退货商品级信息
     *
     * @param sourceOrder 原始售后单数据
     * @param sourceGoods 原始售后退货商品数据
     * @param targetOrder 目标售后单数据
     * @param refundGoods 目标售后退货商品数据
     * @return 结果
     */
    protected GoodsConvertHandleResult refundGoodsConvert(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder, SourceRefundGoodsItem<BusinessGetExchangeResponseRefundGoodInfo> sourceGoods, TargetCovertOrderItem targetOrder, ApiReturnDetailDO refundGoods) {
        // 供子类重写
        return GoodsConvertHandleResult.success();
    }

    //endregion

    //region 转换换货商品级信息

    /**
     * 创建换货商品数据转换插件
     */
    protected List<IExchangeGoodsConvertHandle<BusinessGetExchangeOrderResponseOrderItem, BusinessGetExchangeResponseExchangeGoodInfo>> createExchangeGoodsConvertsHandles() {
        List<IExchangeGoodsConvertHandle<BusinessGetExchangeOrderResponseOrderItem, BusinessGetExchangeResponseExchangeGoodInfo>> exchangeGoodsConverts = new ArrayList<>();
        // 换货单-换货商品基础信息转换
        exchangeGoodsConverts.add(new ExchangeGoodsCovert(context));
        // 平台级特殊处理
        ExchangeGoodsPlatSpecialHandle platSpecialHandle = new ExchangeGoodsPlatSpecialHandle(context);
        platSpecialHandle.setCoveringHandle(this::exchangeGoodsConvert);
        exchangeGoodsConverts.add(platSpecialHandle);
        // 换货单-换货商品编码掩码处理
        exchangeGoodsConverts.add(new ExchangeGoodsJammingCodeHandle(context));
        // 换货单-换货商品商品匹配插件
        exchangeGoodsConverts.add(new ExchangeGoodsMatchHandle(context));
        return exchangeGoodsConverts;
    }

    /**
     * 换货商品是否匹配商品匹配数据
     *
     * @param goodsSysMatchItem 商品匹配数据
     * @param exchangeGoods     换货商品
     * @return 结果
     */
    protected boolean isMatchApiGoodsSysMatch(AfterSaleGoodsSysMatchItem goodsSysMatchItem, BusinessGetExchangeResponseExchangeGoodInfo exchangeGoods) {
        return AfterSaleGoodsMatchOperation.isExchangeMatchGoodsSysMatch(goodsSysMatchItem, exchangeGoods);
    }

    /**
     * 转换换货商品信息
     *
     * @param sourceOrder   原始售后单数据
     * @param sourceGoods   原始售后换货商品数据
     * @param targetOrder   目标售后单数据
     * @param exchangeGoods 目标售后换货商品数据
     * @return 结果
     */
    protected GoodsConvertHandleResult exchangeGoodsConvert(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder, SourceExchangeGoodsItem<BusinessGetExchangeResponseExchangeGoodInfo> sourceGoods, TargetCovertOrderItem targetOrder, ApiReturnDetailTwoDO exchangeGoods) {
        // 供子类重写
        return GoodsConvertHandleResult.success();
    }

    //endregion

    //region 后置处理

    /**
     * 创建后置处理插件
     *
     * @return 后置处理插件列表
     */
    protected List<IPostProcessOrderHandle<BusinessGetExchangeOrderResponseOrderItem>> createPostProcessHandles() {
        List<IPostProcessOrderHandle<BusinessGetExchangeOrderResponseOrderItem>> postProcesses = new ArrayList<>();
        // 换货单商品级删除处理插件
        postProcesses.add(new ExchangeOrderGoodsDeleteHandle(context));
        // 换货单单变化处理
        postProcesses.add(new ExchangeOrderChangeProcessHandle(context));
        // 平台级特殊处理
        ExchangePlatSpecialPostProcessHandle platSpecialHandle = new ExchangePlatSpecialPostProcessHandle(context);
        platSpecialHandle.setCoveringMethod(this::postProcess);
        postProcesses.add(platSpecialHandle);
        return postProcesses;
    }

    /**
     * 后置处理订单信息
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    protected AfterSaleHandleResult postProcess(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        return AfterSaleHandleResult.success();
    }
    //endregion

    //endregion
}
