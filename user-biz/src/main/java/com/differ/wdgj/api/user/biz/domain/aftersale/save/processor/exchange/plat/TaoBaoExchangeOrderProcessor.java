package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.plat;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.GoodsConvertHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceExchangeGoodsItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.BaseSaveExchangeOrderProcessor;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.utils.AfterSaleCovertUtils;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.downloadProduct.BusinessDownloadProductResponseGoodInfo;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.downloadProduct.BusinessDownloadProductResponseGoodInfoSku;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeResponseExchangeGoodInfo;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.RdsGoodsDomain;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailTwoDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnListDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeGoodsDO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Collections;
import java.util.List;

/**
 * 淘宝 - 保存换货单处理器
 *
 * <AUTHOR>
 * @date 2025/4/11 上午10:50
 */
public class TaoBaoExchangeOrderProcessor extends BaseSaveExchangeOrderProcessor {
    //region 常量
    /**
     * 淘宝密文类型（和下载普通订单保持一致）
     */
    private static final int TAOBAO_ENCRYPT_TYPE = 4;
    //endregion


    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public TaoBaoExchangeOrderProcessor(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    /**
     * 转换订单级信息
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    @Override
    protected AfterSaleHandleResult orderConvert(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        // 基础信息
        BusinessGetExchangeOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
        ApiReturnListDO targetExchangeOrder = targetOrder.getAfterSaleOrder();

        // 是否使用换货单新发货接口进行发货
        targetExchangeOrder.setbNewReturnFlag(NumberUtils.toInt(ployOrder.getNewExchangeRepair(), 0));

        // 密文处理
        AfterSaleHandleResult desensitizationResult = processDesensitization(targetOrder);
        if(desensitizationResult.isFailed()){
            return desensitizationResult;
        }

        return AfterSaleHandleResult.success();
    }

    /**
     * 转换换货商品信息
     *
     * @param sourceOrder   原始售后单数据
     * @param sourceGoods   原始售后换货商品数据
     * @param targetOrder   目标售后单数据
     * @param exchangeGoods 目标售后换货商品数据
     * @return 结果
     */
    @Override
    protected GoodsConvertHandleResult exchangeGoodsConvert(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder, SourceExchangeGoodsItem<BusinessGetExchangeResponseExchangeGoodInfo> sourceGoods, TargetCovertOrderItem targetOrder, ApiReturnDetailTwoDO exchangeGoods) {
        // 已知淘宝换货商品只能换货同一商品下的规格
        ApiTradeGoodsDO apiTradeGoods = sourceOrder.getDbOrder().getApiTradeGoodsList().stream().filter(x -> StringUtils.equals(x.getOid(), exchangeGoods.getOid())).findFirst().orElse(null);
        if(apiTradeGoods != null && StringUtils.isNotEmpty(apiTradeGoods.getPlatGoodsID())){
            // 基于推送库数据补全换货商品基础信息，报文较大不适用单独前置插件批量获取
            GoodsConvertHandleResult taobaoRdsResult = getTaobaoRdsSku(apiTradeGoods.getPlatGoodsID(), exchangeGoods);
            if(taobaoRdsResult.isFailed()){
                return taobaoRdsResult;
            }
        }
        return GoodsConvertHandleResult.success();
    }

    //region 私有方法

    /**
     * 密文处理
     *
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    private AfterSaleHandleResult processDesensitization(TargetCovertOrderItem targetOrder) {
        // 基础信息
        ApiReturnListDO targetExchangeOrder = targetOrder.getAfterSaleOrder();

        // 密文处理
        // 基础信息处理
        targetExchangeOrder.setEncryptType(TAOBAO_ENCRYPT_TYPE);
        // 1、密文格式化
        String flag = "tb";
        targetExchangeOrder.setChgSndTo(AfterSaleCovertUtils.formatDesensitizationInfo(targetExchangeOrder.getChgSndTo(), flag));
        targetExchangeOrder.setChgTel(AfterSaleCovertUtils.formatDesensitizationInfo(targetExchangeOrder.getChgTel(), flag));
        targetExchangeOrder.setChangeAdr(AfterSaleCovertUtils.formatDesensitizationInfo(targetExchangeOrder.getChangeAdr(), flag));

        return AfterSaleHandleResult.success();
    }


    private GoodsConvertHandleResult getTaobaoRdsSku(String platGoodsId, ApiReturnDetailTwoDO exchangeGoods){
        RdsGoodsDomain rdsGoodsDomain = new RdsGoodsDomain();
        // 查询推送库商品信息
        List<BusinessDownloadProductResponseGoodInfo> polyGoodsList = rdsGoodsDomain.getPolyProductByPlatGoodIds(context.getMemberName(), Collections.singletonList(platGoodsId));
        if(CollectionUtils.isNotEmpty(polyGoodsList)){
            BusinessDownloadProductResponseGoodInfo polyGoods = polyGoodsList.stream().filter(x -> StringUtils.equals(x.platProductId, platGoodsId)).findFirst().orElse(null);
            if(polyGoods != null){
                BusinessDownloadProductResponseGoodInfoSku ploySku = polyGoods.getSkus().stream().filter(x -> StringUtils.equals(String.valueOf(x.getSkuId()), exchangeGoods.getPlatSkuId())).findFirst().orElse(null);
                if(ploySku != null){
                    // 补全换货商品信息
                    exchangeGoods.setPlatGoodsId(platGoodsId);
                    exchangeGoods.setSku(ploySku.getSkuName());
                    // 优先使用规格级编码，其次使用商品级编码
                    exchangeGoods.setOuterId(StringUtils.defaultIfEmpty(ploySku.getSkuOuterId(), polyGoods.getOuterId()));
                }
            }
        }

        return GoodsConvertHandleResult.success();
    }
    //endregion
}
