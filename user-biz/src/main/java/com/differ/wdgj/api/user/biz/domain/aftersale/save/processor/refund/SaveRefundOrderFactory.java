package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.ISaveAfterSaleOrder;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.plat.*;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;

/**
 * 保存退货退款单 - 业务工厂
 *
 * <AUTHOR>
 * @date 2024-06-27 14:05
 */
public class SaveRefundOrderFactory {
    //region 构造
    private SaveRefundOrderFactory() {
    }
    //endregion

    /**
     * 创建保存退货退款单处理器
     *
     * @param context 上下文
     * @return 实例
     */
    public static ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> createProcessor(AfterSaleSaveContext context) {
        switch (context.getPlat()) {
            case BUSINESS_TmallGJZY:
                return new TmallGJZYSaveRefundOrderProcessor(context);
            case BUSINESS_DouDianSupermarket:
                return new DouDianSupermarketSaveRefundOrderProcessor(context);
            case BUSINESS_DeWu:
                return new DeWuSaveRefundOrderProcessor(context);
            case BUSINESS_AliDaYaoFang:
            case BUSINESS_AliJianKangDaYaoFang:
                return new AliJianKangDaYaoFangSaveRefundOrderProcessor(context);
            case BUSINESS_1688ShuZiXiaoDian:
                return new ShuZiXiaoDian1688SaveRefundOrderProcessor(context);
            case BUSINESS_Chengxintong:
                return new ChengxintongSaveRefundOrderProcessor(context);
            case BUSINESS_PolyMall:
                return new PolyMallSaveRefundOrderProcessor(context);
            case BUSINESS_JD:
                return new JDSaveRefundOrderProcessor(context);
            case BUSINESS_Taobao:
                return new TaoBaoSaveRefundOrderProcessor(context);
            case BUSINESS_FangXinGou:
            case BUSINESS_LuBan:
                return new ByteDanceSaveRefundOrderProcessor(context);
            case BUSINESS_KuaiShouShop:
                return new KuaiShouShopSaveRefundOrderProcessor(context);
            case BUSINESS_JuBaoZan:
                //聚宝赞
                return new JvBaoZanSaveRefundOrderProcessor(context);
//            case BUSINESS_KuaiMaPiFa: //管家会员较少，菠萝派售后单商品和原始单商品字段对不上，暂时不考虑迁移JAVA
//                //快马批发
//                return new KuaiMaPiFaSaveRefundOrderProcessor(context);
            case BUSINESS_Yangkeduo:
                //拼多多
                return new PddSaveRefundOrderProcessor(context);

            default:
                return new BaseSaveRefundOrderProcessor(context);
        }
    }
}
