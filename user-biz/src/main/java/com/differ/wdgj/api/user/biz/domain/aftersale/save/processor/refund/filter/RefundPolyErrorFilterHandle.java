package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.filter;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.AfterSaleSaveBizType;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractPreFiltrationOrderHandle;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.AfterSalesShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiAfterSaleTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.AfterSalesShopConfigUtils;
import net.logstash.logback.encoder.org.apache.commons.lang.BooleanUtils;
import net.logstash.logback.encoder.org.apache.commons.lang.StringUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 退货退款单菠萝派返回错误过滤器
 *
 * <AUTHOR>
 * @date 2025-04-24 13:40
 */
public class RefundPolyErrorFilterHandle extends AbstractPreFiltrationOrderHandle<BusinessGetRefundOrderResponseOrderItem> {

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    public String caption() {
        return "退货退款单菠萝派返回错误过滤器";
    }

    //region 构造
    public RefundPolyErrorFilterHandle(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    //region 常量

    //endregion

    //region 实现基类方法
    /**
     * 前置过滤
     *
     * @param orderItem 原始售后单列表
     * @return 过滤结果
     */
    @Override
    public AfterSaleHandleResult preFiltrationOrder(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> orderItem, TargetCovertOrderItem targetOrder) {
        // 基础数据
        BusinessGetRefundOrderResponseOrderItem polyOrder = orderItem.getPloyOrder();
        if (StringUtils.isNotEmpty(polyOrder.getSubCode()) && ConfigKeyUtils.isActionWdgj(ConfigKeyEnum.AFTERSALE_NEEDRETRY_SUBCODE, polyOrder.getSubCode())) {
            targetOrder.setNeedBizRetry(true);
            return AfterSaleHandleResult.failed(String.format("平台报错：%s", polyOrder.getSubMessage()));
        }
        return AfterSaleHandleResult.success();
    }

    //endregion
}
