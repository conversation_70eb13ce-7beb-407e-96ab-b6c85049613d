package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.plat;

import com.differ.wdgj.api.component.util.tools.SpecialStringUtils;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.WdgjRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.GoodsConvertHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.DbAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceRefundGoodsItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.BaseSaveRefundOrderProcessor;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundResponseRefundGoodInfo;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailTwoDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnListDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeGoodsDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shop.ShopTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiAfterSaleTypeEnum;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 放心购、鲁班 - 保存退货退款单处理器
 *
 * <AUTHOR>
 * @date 2025/4/28 11:53
 */
public class ByteDanceSaveRefundOrderProcessor extends BaseSaveRefundOrderProcessor {
    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public ByteDanceSaveRefundOrderProcessor(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    /**
     * 获取api售后单类型
     *
     * @param orderItem 菠萝派售后单信息
     * @param dbOrder   数据库订单信息
     * @param shopType  店铺类型
     * @return api售后单类型
     */
    @Override
    protected ApiAfterSaleTypeEnum getApiAfterSaleType(BusinessGetRefundOrderResponseOrderItem orderItem, DbAfterSaleOrderItem dbOrder, ShopTypeEnum shopType) {
        // 获取菠萝派返回售后类型
        PolyRefundTypeEnum polyRefundType = PolyRefundTypeEnum.create(orderItem.getRefundType());
        if (polyRefundType != null) {
            // 未发货仅退款
            if (polyRefundType == PolyRefundTypeEnum.JH_08) {
                return ApiAfterSaleTypeEnum.REFUND_PAY_NOT_SEND;
            }
            // 已发货仅退款
            if (polyRefundType == PolyRefundTypeEnum.JH_03) {
                return ApiAfterSaleTypeEnum.REFUND_PAY_SEND;
            }
        }

        //普通售后类型走基类
        return super.getApiAfterSaleType(orderItem, dbOrder, shopType);
    }

    /**
     * 转换订单级信息
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    @Override
    protected AfterSaleHandleResult orderConvert(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        BusinessGetRefundOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
        DbAfterSaleOrderItem dbOrder = sourceOrder.getDbOrder();
        ApiReturnListDO afterSaleOrder = targetOrder.getAfterSaleOrder();

        // 物流公司编码
        afterSaleOrder.setParamCode(ployOrder.getLogisticCode());

        //记录卖家收退货的地址id
        if (!StringUtils.equals(ployOrder.getSellerReceiveAddressId(), "0")) {
            afterSaleOrder.setReserved1(ployOrder.getSellerReceiveAddressId());
        }

        //customerId保持与原始单一致
        if (dbOrder != null && dbOrder.getApiTrade() != null) {
            afterSaleOrder.setCustomerId(dbOrder.getApiTrade().getCustomerID());
        }

        // 补寄单
        if (StringUtils.equals(ployOrder.getRefundType(), PolyRefundTypeEnum.JH_SUPPLEMENT_SEND.getCode())) {
            afterSaleOrder.setType(WdgjRefundTypeEnum.REFUND_SUPPLEMENT.getValue());
        }

        //已发货仅退款单
        if (StringUtils.equals(ployOrder.getRefundType(), PolyRefundTypeEnum.JH_03.getCode())) {
            afterSaleOrder.setRemark(SpecialStringUtils.AddStringToEnd(afterSaleOrder.getRemark(), "【已发货仅退款】", ","));
        }

        return AfterSaleHandleResult.success();
    }

    /**
     * 转换商品级信息
     *
     * @param orderItem   原始售后单数据
     * @param goodsItem   原始售后退货商品数据
     * @param targetOrder 目标售后单数据
     * @param refundGoods 目标售后退货商品数据
     * @return 结果
     */
    @Override
    protected GoodsConvertHandleResult refundGoodsConvert(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> orderItem, SourceRefundGoodsItem<BusinessGetRefundResponseRefundGoodInfo> goodsItem, TargetCovertOrderItem targetOrder, ApiReturnDetailDO refundGoods) {
        BusinessGetRefundResponseRefundGoodInfo ployRefundGoods = goodsItem.getPloyRefundGoods();

        // 有退款数量时进行修正
        if (ployRefundGoods.getRefundProductNum() > 0) {
            BigDecimal refundGoodsCount = BigDecimal.valueOf(ployRefundGoods.getRefundProductNum());
            refundGoods.setGoodsCount(refundGoodsCount);
            refundGoods.setPrice(ployRefundGoods.getRefundAmount().divide(refundGoodsCount, 2, RoundingMode.HALF_EVEN));
        }

        return GoodsConvertHandleResult.success();
    }

    /**
     * 是否匹配原始单货品(通常情况下子类只需要考虑商品级)
     *
     * @param apiTradeGoods 原始单货品
     * @param refundOrder   售后单
     * @param refundGoods   售后单货品
     * @return 结果
     */
    @Override
    protected boolean isMatchApiTradeGoods(ApiTradeGoodsDO apiTradeGoods, BusinessGetRefundOrderResponseOrderItem refundOrder, BusinessGetRefundResponseRefundGoodInfo refundGoods) {
        Boolean baseResult = super.isMatchApiTradeGoods(apiTradeGoods, refundOrder, refundGoods);

        //赠品SubTradeNo与原始单商品Oid字段对应
        if (!baseResult && BooleanUtils.isTrue(refundGoods.getIsGift())) {
            if (StringUtils.isNotEmpty(refundGoods.getPlatProductId())) {
                String apiTradeGoodsOid = StringUtils.defaultIfEmpty(apiTradeGoods.getOid(), StringUtils.EMPTY);
                String refundGoodsSubTradeNo = StringUtils.defaultIfEmpty(refundGoods.getSubTradeNo(), StringUtils.EMPTY);

                return StringUtils.equals(apiTradeGoods.getPlatGoodsID(), refundGoods.getPlatProductId()) && StringUtils.equals(apiTradeGoodsOid, refundGoodsSubTradeNo);
            }
        }

        return baseResult;
    }

    /**
     * 后置处理订单信息
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    @Override
    protected AfterSaleHandleResult postProcess(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        BusinessGetRefundOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();

        // 补寄单 - 退货商品 转换为 换货商品
        if (StringUtils.equals(ployOrder.getRefundType(), PolyRefundTypeEnum.JH_SUPPLEMENT_SEND.getCode()) && targetOrder.getRefundGoods() != null && targetOrder.getRefundGoods().size() > 0) {
            List<ApiReturnDetailDO> targetRefundGoodsList = targetOrder.getRefundGoods();
            List<ApiReturnDetailTwoDO> targetExchangeGoodsList = new ArrayList<>();

            for (ApiReturnDetailDO targetRefundGoods : targetRefundGoodsList) {
                ApiReturnDetailTwoDO targetExchangeGoods = new ApiReturnDetailTwoDO();

                //【转换】
                BeanUtils.copyProperties(targetRefundGoods, targetExchangeGoods);

                targetExchangeGoodsList.add(targetExchangeGoods);
            }

            targetOrder.getExchangeGoods().addAll(targetExchangeGoodsList);
            targetOrder.getRefundGoods().removeAll(targetOrder.getRefundGoods());
        }

        return AfterSaleHandleResult.success();
    }

    //endregion

}
