package com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.batchSyncStock;

/**
 * 菠萝派库存同步商品级请求对象
 *
 * <AUTHOR>
 * @date 2024-03-01 19:26
 */
public class BusinessBatchSyncStockRequestGoodInfo {

    /**
     * 平台商品ID
     */
    private String platProductId;

    /**
     * 平台子规格ID
     */
    private String skuId;

    /**
     * 外部商家编码
     */
    private String outerId;

    /**
     * 库存数量
     */
    private Integer quantity;

    /**
     * 实际库存数量
     */
    private Integer realQuantity;

    /**
     * 库存总数量
     */
    private Integer totalQuantity;

    /**
     * 库存更新方式
     */
    private String syncStockType;

    /**
     * 店铺类型
     */
    private String shopType;

    /**
     * 外部商家SKU编号。
     * (特奢汇,诚信通,聚美优品,天虹商场专用)
     */
    private String outSkuId;

    /**
     * 后端商品ID(天猫后端商品专用)
     */
    private String scItemId;

    /**
     * erp商品ID
     */
    private String erpGoodId;

    /**
     * 可用库存数量
     */
    private Integer availableQuantity;

    /**
     * erp商品仓库编码
     */
    private String erpWhseCode;

    /**
     * 扩展字段(用于扩展属性)
     */
    private String extProperty;

    /**
     * 商品仓库编号。 (融易购,达令网、唯品会、美囤妈妈专用)
     */
    private String whseCode;

    /**
     * 合作编码。(唯品会专用)
     */
    private String vipCooperationNo;

    /**
     * 同步商品状态
     */
    private String status;

    /**
     * 商品类型。 (当当网专用)
     */
    private String productType;

    /**
     * 站点(亚马逊接口地址)
     */
    private String webSite;

    /**
     * 交易序号(亚马逊按数字序号赋值)(雅虎、亚马逊专用)
     */
    private String transactionId;

    /**
     * 平台门店Id。 (京东到家专用)
     */
    private String platStoreId;

    /**
     * 仓库类型。
     */
    private String storeType;

    /**
     * 仓库名称。
     */
    private String whseName;

    /**
     * 规格名称。(国美B2B专用)
     */
    private String skuName;

    /**
     * 供应商编码
     */
    private String suppliercode;

    /**
     * 平台商品名称
     */
    private String productName;

    /**
     * SIPShopId
     */
    private String sipShopId;

    /**
     * 连锁门店ID
     */
    private String nodeShopID;

    /**
     * 类目ID
     */
    private String categoryID;

    /**
     * 仓库编码标识 0：全国逻辑仓或7大仓；1：省仓 默认为全国逻辑仓或7大仓 (唯品会JIT专用)
     */
    private Integer warehouseFlag;

    /**
     * 详细数量（菠萝派商城专用）
     */
    private String detailQuantity;

    /**
     * 阶梯库存（抖店专用）
     */
    private Integer secondQuantity;

    /**
     * 平台店铺ID
     */
    private String shopId;

    //region 非菠萝派字段

    /**
     * 店铺下的门店Id
     */
    private String storeId;

    //endregion

    //region get/set
    public String getPlatProductId() {
        return platProductId;
    }

    public void setPlatProductId(String platProductId) {
        this.platProductId = platProductId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Integer getRealQuantity() {
        return realQuantity;
    }

    public void setRealQuantity(Integer realQuantity) {
        this.realQuantity = realQuantity;
    }

    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    public String getSyncStockType() {
        return syncStockType;
    }

    public void setSyncStockType(String syncStockType) {
        this.syncStockType = syncStockType;
    }

    public String getShopType() {
        return shopType;
    }

    public void setShopType(String shopType) {
        this.shopType = shopType;
    }

    public String getOutSkuId() {
        return outSkuId;
    }

    public void setOutSkuId(String outSkuId) {
        this.outSkuId = outSkuId;
    }

    public String getScItemId() {
        return scItemId;
    }

    public void setScItemId(String scItemId) {
        this.scItemId = scItemId;
    }

    public String getErpGoodId() {
        return erpGoodId;
    }

    public void setErpGoodId(String erpGoodId) {
        this.erpGoodId = erpGoodId;
    }

    public Integer getAvailableQuantity() {
        return availableQuantity;
    }

    public void setAvailableQuantity(Integer availableQuantity) {
        this.availableQuantity = availableQuantity;
    }

    public String getErpWhseCode() {
        return erpWhseCode;
    }

    public void setErpWhseCode(String erpWhseCode) {
        this.erpWhseCode = erpWhseCode;
    }

    public String getExtProperty() {
        return extProperty;
    }

    public void setExtProperty(String extProperty) {
        this.extProperty = extProperty;
    }

    public String getWhseCode() {
        return whseCode;
    }

    public void setWhseCode(String whseCode) {
        this.whseCode = whseCode;
    }

    public String getVipCooperationNo() {
        return vipCooperationNo;
    }

    public void setVipCooperationNo(String vipCooperationNo) {
        this.vipCooperationNo = vipCooperationNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getWebSite() {
        return webSite;
    }

    public void setWebSite(String webSite) {
        this.webSite = webSite;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getPlatStoreId() {
        return platStoreId;
    }

    public void setPlatStoreId(String platStoreId) {
        this.platStoreId = platStoreId;
    }

    public String getStoreType() {
        return storeType;
    }

    public void setStoreType(String storeType) {
        this.storeType = storeType;
    }

    public String getWhseName() {
        return whseName;
    }

    public void setWhseName(String whseName) {
        this.whseName = whseName;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public String getSuppliercode() {
        return suppliercode;
    }

    public void setSuppliercode(String suppliercode) {
        this.suppliercode = suppliercode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSipShopId() {
        return sipShopId;
    }

    public void setSipShopId(String sipShopId) {
        this.sipShopId = sipShopId;
    }

    public String getNodeShopID() {
        return nodeShopID;
    }

    public void setNodeShopID(String nodeShopID) {
        this.nodeShopID = nodeShopID;
    }

    public String getCategoryID() {
        return categoryID;
    }

    public void setCategoryID(String categoryID) {
        this.categoryID = categoryID;
    }

    public Integer getWarehouseFlag() {
        return warehouseFlag;
    }

    public void setWarehouseFlag(Integer warehouseFlag) {
        this.warehouseFlag = warehouseFlag;
    }

    public String getDetailQuantity() {
        return detailQuantity;
    }

    public void setDetailQuantity(String detailQuantity) {
        this.detailQuantity = detailQuantity;
    }

    public Integer getSecondQuantity() {
        return secondQuantity;
    }

    public void setSecondQuantity(Integer secondQuantity) {
        this.secondQuantity = secondQuantity;
    }

    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    //endregion
}
