package com.differ.wdgj.api.user.biz.domain.stock.data.enums;

import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.NameEnum;
import com.differ.wdgj.api.component.util.enums.ValueEnum;

/**
 * 库存同步数量详情
 *
 * <AUTHOR>
 * @date 2024-03-14 19:20
 */
public enum StockDetailCountTypeEnum implements NameEnum, ValueEnum {
    /**
     * 未付款数量
     */
    UnPay(0, "未付款数量"),

    /**
     * 库存量
     */
    InStock(1, "库存量"),

    /**
     * 订购量
     */
    OrderQuantity(2, "订购量"),

    /**
     * 待发货量
     */
    waitSend(3, "待发货量"),

    /**
     * 采购在途数量
     */
    PurchaseTransit(4, "采购在途数量"),

    /**
     * 调入在途数量
     */
    CallInTransit(5, "调入在途数量"),

    /**
     * 批次库存
     */
    BatchInventory(6, "批次库存"),

    /**
     * 3天内已付款和货到付款原始单占用量
     */
    ThreeDayOccupy(7, "3天内已付款和货到付款原始单占用量"),

    /**
     * 唯品会占用量
     */
    WphOccupy(8, "唯品会占用量"),

    /**
     * 云集POP占用量
     */
    YjPopOccupy(9, "云集POP占用量"),

    /**
     * 阶梯库存量
     */
    LadderInventory(10, "阶梯库存量"),




    /**
     * 阶梯库存量
     */
    LadderInventory(10, "阶梯库存量"),

    ;

    /**
     * 构造
     * @param value 库存同步数量详情类型值
     * @param name 库存同步数量详情类型名
     */
    StockDetailCountTypeEnum(Integer value, String name){
        this.value = value;
        this.name = name;
    }

    /**
     * 库存同步数量详情类型值
     */
    private Integer value;

    /**
     * 库存同步数量详情类型名
     */
    private String name;


    /**
     * 获取库存同步数量详情类型值
     *
     * @return 库存同步数量详情类型值
     */
    @Override
    public Integer getValue() {
        return this.value;
    }

    /**
     * 获取库存同步数量详情类型名
     *
     * @return 库存同步数量详情类型名
     */
    @Override
    public String getName() {
        return this.name;
    }


    //region 静态方法

    /**
     * 根据管家商品的平台值获取对应的枚举。（subCode优先）
     *
     * @param value 库存同步数量详情类型值
     * @return 对应的枚举
     */
    public static StockDetailCountTypeEnum create(Integer value) {
        return EnumConvertCacheUtil.convert(value, StockDetailCountTypeEnum.class, EnumConvertType.VALUE);
    }

    //endregion
}
