package com.differ.wdgj.api.user.biz.domain.stock.data.enums.plat;

/**
 * 库存同步-库存数量计算模式
 *
 * <AUTHOR>
 * @date 2025/6/16 13:55
 */
public enum StockGoodscalCulationModeEnum {
    /**
     * 普通商品计算模式
     */
    NORMAL_GOODS("NORMAL_GOODS", "普通商品", "普通商品库存计算模式，计算结果：%s"),
    /**
     * 组合商品库存计算模式
     */
    FIT_GOODS("FIT_GOODS", "组合商品", "组合商品库存计算模式，计算结果：%s")
;
    //region 字段
    /**
     * 编码
     */
    private final String code;
    /**
     * 描述
     */
    private final String explain;
    /**
     * 日志格式
     */
    private final String logFormatter;
    //endregion

    // region 构造器

    StockGoodscalCulationModeEnum(String code, String explain, String logFormatter) {
        this.code = code;
        this.explain = explain;
        this.logFormatter = logFormatter;
    }
    // endregion

    //region getter
    public String getCode() {
        return code;
    }

    public String getExplain() {
        return explain;
    }

    public String getLogFormatter() {
        return logFormatter;
    }
    //endregion
}
