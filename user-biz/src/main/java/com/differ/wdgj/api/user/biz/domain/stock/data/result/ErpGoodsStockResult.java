package com.differ.wdgj.api.user.biz.domain.stock.data.result;

import java.math.BigDecimal;

/**
 * erp商品库存结果
 *
 * <AUTHOR>
 * @date 2025/6/16 20:05
 */
public class ErpGoodsStockResult {
    /**
     * 是否成功
     */
    protected boolean success;

    /**
     * 错误信息
     */
    protected String message;

    /**
     * 库存数量
     */
    private int stockCount;

    /**
     * 库存详情
     */
    private String detailCount;

    //region 静态方法

    /**
     * 成功结果
     *
     * @param stockCount 库存数量
     * @param detailCount       菠萝派库存详情库存同步商品级别列表
     * @return 结果
     */
    public static ErpGoodsStockResult success(BigDecimal stockCount, String detailCount) {
        ErpGoodsStockResult result = new ErpGoodsStockResult();
        result.setSuccess(true);
        result.setStockCount(stockCount);
        result.setDetailCount(detailCount);
        return result;
    }

    /**
     * 失败结果
     *
     * @param message 错误信息
     * @return 结果
     */
    public static ErpGoodsStockResult failed(String message) {
        ErpGoodsStockResult result = new ErpGoodsStockResult();
        result.setSuccess(false);
        result.setMessage(message);
        return result;
    }
    //endregion

    //region 公共方法
    public boolean isFailed() {
        return !success;
    }
    //endregion

    //region get/set
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public int getStockCount() {
        return stockCount;
    }

    public void setStockCount(int stockCount) {
        this.stockCount = stockCount;
    }

    public String getDetailCount() {
        return detailCount;
    }

    public void setDetailCount(String detailCount) {
        this.detailCount = detailCount;
    }
    //endregion
}
