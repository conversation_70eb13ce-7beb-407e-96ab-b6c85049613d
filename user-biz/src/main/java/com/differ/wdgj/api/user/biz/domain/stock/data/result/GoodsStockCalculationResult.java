package com.differ.wdgj.api.user.biz.domain.stock.data.result;

import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockDetailCountTypeEnum;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 商品库存计算结果
 *
 * <AUTHOR>
 * @date 2025/6/16 20:05
 */
public class GoodsStockCalculationResult {
    /**
     * 是否成功
     */
    protected boolean success;

    /**
     * 错误信息
     */
    protected String message;

    /**
     * 库存数量
     */
    private BigDecimal stockCount;

    /**
     * 库存详情
     */
    private Map<StockDetailCountTypeEnum, BigDecimal> stockDetailMap;

    //region 静态方法

    /**
     * 成功结果
     *
     * @param stockCount     库存数量
     * @param stockDetailMap 菠萝派库存详情库存同步商品级别列表
     * @return 结果
     */
    public static GoodsStockCalculationResult success(BigDecimal stockCount, Map<StockDetailCountTypeEnum, BigDecimal> stockDetailMap) {
        GoodsStockCalculationResult result = new GoodsStockCalculationResult();
        result.setSuccess(true);
        result.setStockCount(stockCount);
        result.setStockDetailMap(stockDetailMap);
        return result;
    }

    /**
     * 失败结果
     *
     * @param message 错误信息
     * @return 结果
     */
    public static GoodsStockCalculationResult failed(String message) {
        GoodsStockCalculationResult result = new GoodsStockCalculationResult();
        result.setSuccess(false);
        result.setMessage(message);
        return result;
    }
    //endregion

    //region 公共方法
    public boolean isFailed() {
        return !success;
    }
    //endregion

    //region get/set
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public BigDecimal getStockCount() {
        return stockCount;
    }

    public void setStockCount(BigDecimal stockCount) {
        this.stockCount = stockCount;
    }

    public Map<StockDetailCountTypeEnum, BigDecimal> getStockDetailMap() {
        return stockDetailMap;
    }

    public void setStockDetailMap(Map<StockDetailCountTypeEnum, BigDecimal> stockDetailMap) {
        this.stockDetailMap = stockDetailMap;
    }

    //endregion
}
