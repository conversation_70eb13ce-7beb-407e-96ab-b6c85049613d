package com.differ.wdgj.api.user.biz.domain.stock.data.result;

import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncGoodsRequestPair;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncMatchExecuteResult;

import java.util.List;

/**
 * 库存同步请求构建结果
 *
 * <AUTHOR>
 * @date 2024-05-14 10:00
 */
public class StockSyncBuildRequestResult {
    /**
     * 是否成功
     */
    protected boolean success;

    /**
     * 错误信息
     */
    protected String message;

    /**
     * 构建失败结果列表
     */
    private List<StockSyncMatchExecuteResult> buildFailedResults;

    /**
     * 菠萝派库存同步请求项列表
     */
    private List<StockSyncGoodsRequestPair> requestGoodsItems;

    //region 静态方法

    /**
     * 成功结果
     *
     * @param buildFailedResults 构建失败结果列表
     * @param requestGoodsItems       菠萝派库存同步商品级别列表
     * @return 结果
     */
    public static StockSyncBuildRequestResult success(List<StockSyncMatchExecuteResult> buildFailedResults, List<StockSyncGoodsRequestPair> requestGoodsItems) {
        StockSyncBuildRequestResult result = new StockSyncBuildRequestResult();
        result.setSuccess(true);
        result.setBuildFailedResults(buildFailedResults);
        result.setRequestGoodsItems(requestGoodsItems);
        return result;
    }

    /**
     * 失败结果
     *
     * @param message 错误信息
     * @return 结果
     */
    public static StockSyncBuildRequestResult failed(String message) {
        StockSyncBuildRequestResult result = new StockSyncBuildRequestResult();
        result.setSuccess(false);
        result.setMessage(message);
        return result;
    }
    //endregion

    //region 公共方法
    public boolean isFailed() {
        return !success;
    }
    //endregion

    //region get/set
    public List<StockSyncMatchExecuteResult> getBuildFailedResults() {
        return buildFailedResults;
    }

    public void setBuildFailedResults(List<StockSyncMatchExecuteResult> buildFailedResults) {
        this.buildFailedResults = buildFailedResults;
    }

    public List<StockSyncGoodsRequestPair> getRequestGoodsItems() {
        return requestGoodsItems;
    }

    public void setRequestGoodsItems(List<StockSyncGoodsRequestPair> requestGoodsItems) {
        this.requestGoodsItems = requestGoodsItems;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
    //endregion
}
