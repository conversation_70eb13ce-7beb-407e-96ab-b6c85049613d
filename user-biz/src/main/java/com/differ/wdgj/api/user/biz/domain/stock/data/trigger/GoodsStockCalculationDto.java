package com.differ.wdgj.api.user.biz.domain.stock.data.trigger;

import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ShopConfigStockSyncRuleEnum;

import java.util.List;

/**
 * 商品库存计算数据
 *
 * <AUTHOR>
 * @date 2025/6/16 20:42
 */
public class GoodsStockCalculationDto {
    /**
     * ERP仓库代码列表
     */
    private List<Integer> erpWarehouseIds;

    /**
     * ERP商品ID
     */
    private Integer erpGoodsId;

    /**
     * ERP规格ID
     */
    private Integer erpSpecId;

    /**
     * 是否组合装
     */
    private Integer goodsType;

    /**
     * 管家店铺ID
     */
    private Integer shopId;


    /**
     * 店铺配制计算规则
     */
    private ShopConfigStockSyncRuleEnum shopConfigStockSyncRule;

    //region get/set

    public List<Integer> getErpWarehouseIds() {
        return erpWarehouseIds;
    }

    public void setErpWarehouseIds(List<Integer> erpWarehouseIds) {
        this.erpWarehouseIds = erpWarehouseIds;
    }
    public Integer getErpGoodsId() {
        return erpGoodsId;
    }

    public void setErpGoodsId(Integer erpGoodsId) {
        this.erpGoodsId = erpGoodsId;
    }

    public Integer getErpSpecId() {
        return erpSpecId;
    }

    public void setErpSpecId(Integer erpSpecId) {
        this.erpSpecId = erpSpecId;
    }

    public Integer getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(Integer goodsType) {
        this.goodsType = goodsType;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }
    //endregion
}
