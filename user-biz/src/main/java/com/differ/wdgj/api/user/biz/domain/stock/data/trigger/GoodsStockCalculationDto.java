package com.differ.wdgj.api.user.biz.domain.stock.data.trigger;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/16 20:42
 */
public class GoodsStockCalculationDto {

    /**
     * 仓库代码列表
     */
    private List<Integer> warehouseCodes;

    /**
     * ERP商品ID
     */
    private Integer erpGoodsId;

    /**
     * ERP规格ID
     */
    private Integer erpSpecId;

    /**
     * 是否组合装
     */
    private Integer goodsType;

    /**
     * 管家店铺ID
     */
    private Integer shopId;


    //region get/set
    public List<Integer> getWarehouseCodes() {
        return warehouseCodes;
    }

    public void setWarehouseCodes(List<Integer> warehouseCodes) {
        this.warehouseCodes = warehouseCodes;
    }

    public Integer getErpGoodsId() {
        return erpGoodsId;
    }

    public void setErpGoodsId(Integer erpGoodsId) {
        this.erpGoodsId = erpGoodsId;
    }

    public Integer getErpSpecId() {
        return erpSpecId;
    }

    public void setErpSpecId(Integer erpSpecId) {
        this.erpSpecId = erpSpecId;
    }

    public Integer getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(Integer goodsType) {
        this.goodsType = goodsType;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }
    //endregion
}
