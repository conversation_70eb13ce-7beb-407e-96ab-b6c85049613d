package com.differ.wdgj.api.user.biz.domain.stock.data.trigger;

import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.batchSyncStock.BusinessBatchSyncStockRequestGoodInfo;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.batchSyncStock.BusinessBatchSyncStockResponseGoodSyncStockResultItem;
import com.differ.wdgj.api.user.biz.domain.stock.data.GoodsMatchEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.MatchIdEnhance;

/**
 * 库存同步匹配执行结果
 *
 * <AUTHOR>
 * @date 2024-05-22 10:00
 */
public class StockSyncMatchExecuteResult {
    /**
     * 匹配信息唯一标识
     */
    private MatchIdEnhance matchId;

    /**
     * 匹配数据
     */
    private GoodsMatchEnhance matchEnhance;

    /**
     * 库存同步量详情
     */
    private String detailCount;

    /**
     * 平台请求
     */
    private BusinessBatchSyncStockRequestGoodInfo platRequest;

    /**
     * 平台响应
     */
    private BusinessBatchSyncStockResponseGoodSyncStockResultItem platResponse;

    /**
     * 是否成功
     */
    protected boolean success;

    /**
     * 错误信息
     */
    protected String message;

    //region 构造
    private StockSyncMatchExecuteResult() {
    }
    //endregion

    //region 公共静态方法

    public static StockSyncMatchExecuteResult successResult(GoodsMatchEnhance matchEnhance, String detailCount, BusinessBatchSyncStockRequestGoodInfo platRequest, BusinessBatchSyncStockResponseGoodSyncStockResultItem platResponse) {
        StockSyncMatchExecuteResult result = new StockSyncMatchExecuteResult();
        result.setMatchId(MatchIdEnhance.convert(matchEnhance));
        result.setMatchEnhance(matchEnhance);
        result.setDetailCount(detailCount);
        result.setPlatRequest(platRequest);
        result.setPlatResponse(platResponse);
        result.setSuccess(true);
        return result;
    }

    public static StockSyncMatchExecuteResult filedResult(GoodsMatchEnhance matchEnhance, String message) {
        StockSyncMatchExecuteResult result = new StockSyncMatchExecuteResult();
        result.setMatchId(MatchIdEnhance.convert(matchEnhance));
        result.setMatchEnhance(matchEnhance);
        result.setSuccess(false);
        result.setMessage(message);
        return result;
    }

    //endregion

    //region get/set
    public MatchIdEnhance getMatchId() {
        return matchId;
    }

    public void setMatchId(MatchIdEnhance matchId) {
        this.matchId = matchId;
    }

    public GoodsMatchEnhance getMatchEnhance() {
        return matchEnhance;
    }

    public void setMatchEnhance(GoodsMatchEnhance matchEnhance) {
        this.matchEnhance = matchEnhance;
    }

    public String getDetailCount() {
        return detailCount;
    }

    public void setDetailCount(String detailCount) {
        this.detailCount = detailCount;
    }

    public BusinessBatchSyncStockRequestGoodInfo getPlatRequest() {
        return platRequest;
    }

    public void setPlatRequest(BusinessBatchSyncStockRequestGoodInfo platRequest) {
        this.platRequest = platRequest;
    }

    public BusinessBatchSyncStockResponseGoodSyncStockResultItem getPlatResponse() {
        return platResponse;
    }

    public void setPlatResponse(BusinessBatchSyncStockResponseGoodSyncStockResultItem platResponse) {
        this.platResponse = platResponse;
    }
    public boolean isSuccess() {
        return success;
    }
    public boolean isFiled() {
        return !success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
    //endregion
}
