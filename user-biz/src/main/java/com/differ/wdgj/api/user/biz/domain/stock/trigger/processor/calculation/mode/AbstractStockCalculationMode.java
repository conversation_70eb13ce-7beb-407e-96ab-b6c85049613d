package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.mode;

import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.GoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.GoodsStockCalculationDto;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.subdomain.calculation.IErpGoodsStockCalculationService;
import com.differ.wdgj.api.user.biz.domain.stock.subdomain.calculation.impl.ErpGoodsStockCalculationService;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;

/**
 * 库存计算模式抽象基类
 *
 * <AUTHOR>
 * @date 2024-12-26 10:00
 */
public abstract class AbstractStockCalculationMode implements IStockCalculationMode {
    //region 常量
    /**
     * 全局上下文
     */
    protected final StockSyncContext context;

    /**
     * erp商品库存计算服务
     */
    protected final IErpGoodsStockCalculationService erpGoodsStockCalculationService;

    /**
     * 日志标题
     */
    protected final String Caption = "库存计算模式";
    //endregion

    //region Description
    public AbstractStockCalculationMode(StockSyncContext context) {
        this.context = context;
        this.erpGoodsStockCalculationService = new ErpGoodsStockCalculationService();
    }
    //endregion

    /**
     * 获取实际库存数量
     *
     * @param calculateParam 计算参数
     * @return 实际库存数量
     */
    @Override
    public GoodsStockCalculationResult getActualStock(GoodsStockCalculationDto calculateParam) {
        try {
            // 执行具体的库存获取逻辑
            return doGetActualStock(calculateParam);

        } catch (Exception e) {
            String message = String.format("【%s】计算模式[%s]获取实际库存失败，原因：%s",
                    context.getVipUser(), getModeName(), e.getMessage());
            LogFactory.error(Caption, message, e);
            return StockContentResult.failed(message);
        }
    }

    /**
     * 执行具体的库存获取逻辑
     * 子类需要实现此方法
     *
     * @param calculateParam 计算参数
     * @return 实际库存数量
     */
    protected abstract StockContentResult<Integer> doGetActualStock(GoodsStockCalculationDto calculateParam);
}
