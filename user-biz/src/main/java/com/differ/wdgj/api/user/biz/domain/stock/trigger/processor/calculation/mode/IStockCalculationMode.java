package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.mode;

import com.differ.wdgj.api.user.biz.domain.stock.data.result.GoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.GoodsStockCalculationDto;

/**
 * 库存计算模式接口
 * 定义不同的计算模式，可通过外部子类注入
 *
 * <AUTHOR>
 * @date 2024-12-26 10:00
 */
public interface IStockCalculationMode {

    /**
     * 获取实际库存数量
     *
     * @param calculateParam 计算参数
     * @return 实际库存数量
     */
    GoodsStockCalculationResult getActualStock(GoodsStockCalculationDto calculateParam);

    /**
     * 获取计算模式名称
     *
     * @return 模式名称
     */
    String getModeName();
}
