package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.mode;

import com.differ.wdgj.api.component.util.tools.BigDecimalUtils;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockDetailCountTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.GoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.GoodsStockCalculationDto;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 普通库存计算模式
 *
 * <AUTHOR>
 * @date 2024-12-26 10:00
 */
public class NormalStockCalculationMode extends AbstractStockCalculationMode {
    //region 构造
    public NormalStockCalculationMode(StockSyncContext context) {
        super(context);
    }
    //endregion

    //region 重写基类方法
    /**
     * 执行具体的库存获取逻辑
     *
     * @param calculateParam 计算参数
     * @return 实际库存数量
     */
    @Override
    protected GoodsStockCalculationResult doGetActualStock(GoodsStockCalculationDto calculateParam) {
        // 云端存储过程获取库存
        GoodsStockCalculationResult goodsStockResult = erpGoodsStockCalculationService.calculationNormalStock(calculateParam.getShopId(), calculateParam.getErpGoodsId(), calculateParam.getErpSpecId(), calculateParam.getErpWarehouseIds());
        if(goodsStockResult.isSuccess()){
            // 解析库存详情
            Map<StockDetailCountTypeEnum, BigDecimal> stockDetailMap = convertDetailCount(goodsStockResult.getDetailCount());

        }
        return goodsStockResult;
    }
    
    /**
     * 获取计算模式名称
     *
     * @return 模式名称
     */
    @Override
    public String getModeName() {
        return "普通库存计算模式";
    }
    //endregion


    /**
     * 库存同步详情转换
     *
     * @param detailCount 原始详细库存量
     * @return 详细库存量
     */
    private Map<StockDetailCountTypeEnum, BigDecimal> convertDetailCount(String detailCount) {
        Map<StockDetailCountTypeEnum, BigDecimal> stockDetailMap = new HashMap<>();
        if (StringUtils.isEmpty(detailCount)) {
            return stockDetailMap;
        }

        if (StringUtils.isNotEmpty(detailCount)) {
            String[] detailArray = detailCount.split(";");
            for (String stockCounts : detailArray) {
                String[] stockCount = stockCounts.split(":");
                if (stockCount.length > 1) {
                    Integer countTypeValue = Integer.parseInt(stockCount[0]);
                    StockDetailCountTypeEnum countType = StockDetailCountTypeEnum.create(countTypeValue);
                    if(countType != null){
                        BigDecimal count = BigDecimalUtils.safeParse(stockCount[1]);
                        if(count == null){
                            count = BigDecimal.ZERO;
                        }

                        stockDetailMap.put(countType, count);
                    }
                }
            }
        }

        return stockDetailMap;
    }
}
