package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.mode.plugins;

import com.differ.wdgj.api.user.biz.domain.stock.data.result.ErpGoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.GoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.GoodsStockCalculationDto;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.mode.AbstractStockCalculationMode;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashMap;

/**
 * 组合库存计算模式
 *
 * <AUTHOR>
 * @date 2025/6/17 10:14
 */
public class FitStockCalculationMode extends AbstractStockCalculationMode {
    //region 构造
    public FitStockCalculationMode(StockSyncContext context) {
        super(context);
    }
    //endregion

    //region 重写基类方法

    /**
     * 执行具体的库存获取逻辑
     *
     * @param calculateParam 计算参数
     * @return 实际库存数量
     */
    @Override
    protected GoodsStockCalculationResult doGetActualStock(GoodsStockCalculationDto calculateParam) {

        if(CollectionUtils.isEmpty(calculateParam.getErpWarehouseIds())){
            return GoodsStockCalculationResult.failed("仓库列表为空");
        }

        // 云端存储过程获取库存
        ErpGoodsStockCalculationResult erpGoodsStockResult = getErpGoodsStockCalculationService().calculationFitStock(calculateParam.getShopConfigStockSyncRule().getValue(), calculateParam.getErpGoodsId(), calculateParam.getErpWarehouseIds());
        if (erpGoodsStockResult.isFailed()) {
            return GoodsStockCalculationResult.failed(erpGoodsStockResult.getMessage());
        }
        return GoodsStockCalculationResult.success(erpGoodsStockResult.getStockCount(), new HashMap<>());
    }

    /**
     * 获取计算模式名称
     *
     * @return 模式名称
     */
    @Override
    public String getModeName() {
        return "组合商品库存计算模式";
    }
    //endregion

}
