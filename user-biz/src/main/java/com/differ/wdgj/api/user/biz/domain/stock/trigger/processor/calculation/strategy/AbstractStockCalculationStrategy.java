package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.strategy;

import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.GoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.rule.StockCalculationRule;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;

import java.math.BigDecimal;

/**
 * 库存计算策略抽象基类
 *
 * <AUTHOR>
 * @date 2024-12-26 10:00
 */
public abstract class AbstractStockCalculationStrategy implements IStockCalculationStrategy {
    //region 常量
    /**
     * 全局上下文
     */
    protected final StockSyncContext context;

    /**
     * 日志标题
     */
    protected final String Caption = "库存计算策略";
    //endregion

    //region Description
    public AbstractStockCalculationStrategy(StockSyncContext context) {
        this.context = context;
    }
    //endregion
    /**
     * 计算同步数量
     *
     * @param rule        计算规则
     * @param actualStock 实际库存数量
     * @return 计算后的同步数量
     */
    @Override
    public GoodsStockCalculationResult calculateSyncQuantity(StockCalculationRule rule, BigDecimal actualStock) {
        try {
            // 执行具体的计算逻辑
            return doCalculateSyncQuantity(rule, actualStock);
        } catch (Exception e) {
            String message = String.format("【%s】计算策略[%s]计算同步数量失败，原因：%s",
                    context.getVipUser(), getStrategyName(), e.getMessage());
            LogFactory.error(Caption, message, e);
            return StockContentResult.failed(message);
        }
    }
    
    /**
     * 执行具体的计算逻辑
     * 子类需要实现此方法
     *
     * @param rule        计算规则
     * @param actualStock 实际库存数量
     * @return 计算后的同步数量
     */
    protected abstract GoodsStockCalculationResult doCalculateSyncQuantity(StockCalculationRule rule, BigDecimal actualStock);
}
