package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.strategy;

import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.rule.StockCalculationRule;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockNumRuleDto;

/**
 * 默认库存计算策略
 * 基于同步数量规则进行计算
 *
 * <AUTHOR>
 * @date 2024-12-26 10:00
 */
public class DefaultStockCalculationStrategy extends AbstractStockCalculationStrategy {
    //region 构造
    public DefaultStockCalculationStrategy(StockSyncContext context) {
        super(context);
    }
    //endregion

    /**
     * 执行具体的计算逻辑
     *
     * @param rule        计算规则
     * @param actualStock 实际库存数量
     * @return 计算后的同步数量
     */
    @Override
    protected StockContentResult<Integer> doCalculateSyncQuantity(StockCalculationRule rule, Integer actualStock) {
        SyncStockNumRuleDto syncRule = rule.getSyncStockNumRule();
        if (syncRule == null) {
            return StockContentResult.failed("同步数量规则为空");
        }
        
        log.info("【{}】默认库存计算策略 - 计算同步数量，实际库存：{}，同步百分比：{}",
                context.getVipUser(),
                actualStock,
                syncRule.getSyncNumPercentage());
        
        // TODO: 实现具体的计算逻辑
        // 1. 根据同步数量规则计算
        // 2. 考虑未付款数量、采购在途数量、调入在途数量等因素
        // 3. 应用同步百分比
        // 4. 处理库存占用量
        
        int syncQuantity = calculateBaseSyncQuantity(actualStock, syncRule);
        
        return StockContentResult.success(syncQuantity);
    }
    
    /**
     * 计算基础同步数量
     *
     * @param actualStock 实际库存
     * @param syncRule    同步规则
     * @return 基础同步数量
     */
    private int calculateBaseSyncQuantity(Integer actualStock, SyncStockNumRuleDto syncRule) {
        if (actualStock == null || actualStock <= 0) {
            return 0;
        }
        
        // 应用同步百分比
        int percentage = syncRule.getSyncNumPercentage();
        if (percentage <= 0 || percentage > 100) {
            percentage = 100; // 默认100%
        }
        
        int syncQuantity = (actualStock * percentage) / 100;
        
        // TODO: 后续补充其他计算逻辑
        // - 扣减未付款数量
        // - 扣减库存占用量
        // - 加上采购在途数量
        // - 加上调入在途数量
        
        return Math.max(0, syncQuantity);
    }
    
    /**
     * 是否支持当前计算规则
     *
     * @param rule 计算规则
     * @return 是否支持
     */
    @Override
    public boolean supports(StockCalculationRule rule) {
        // 默认策略支持所有规则
        return true;
    }
    
    /**
     * 获取策略名称
     *
     * @return 策略名称
     */
    @Override
    public String getStrategyName() {
        return "默认库存计算策略";
    }
}
