package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.strategy;

import com.differ.wdgj.api.user.biz.domain.stock.data.result.GoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.rule.StockCalculationRule;

import java.math.BigDecimal;

/**
 * 库存计算策略接口
 * 定义不同的库存计算策略，可通过外部子类注入
 *
 * <AUTHOR>
 * @date 2024-12-26 10:00
 */
public interface IStockCalculationStrategy {
    
    /**
     * 计算同步数量
     *
     * @param rule        计算规则
     * @param actualStock 实际库存数量
     * @return 计算后的同步数量
     */
    GoodsStockCalculationResult calculateSyncQuantity(StockCalculationRule rule, BigDecimal actualStock);
    
    /**
     * 获取策略名称
     *
     * @return 策略名称
     */
    String getStrategyName();
}
