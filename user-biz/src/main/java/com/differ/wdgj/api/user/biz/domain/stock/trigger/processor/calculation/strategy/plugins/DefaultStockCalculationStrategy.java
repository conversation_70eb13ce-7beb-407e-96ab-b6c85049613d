package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.strategy.plugins;

import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockGoodsExtRuleTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.GoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.rule.StockCalculationRule;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.strategy.AbstractStockCalculationStrategy;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 默认库存计算策略
 * 基于同步数量规则进行计算
 *
 * <AUTHOR>
 * @date 2024-12-26 10:00
 */
public class DefaultStockCalculationStrategy extends AbstractStockCalculationStrategy {
    //region 构造
    public DefaultStockCalculationStrategy(StockSyncContext context) {
        super(context);
    }
    //endregion

    //region 实现基类方法
    /**
     * 执行具体的计算逻辑
     *
     * @param rule        计算规则
     * @param actualStock 实际库存数量
     * @return 计算后的同步数量
     */
    @Override
    protected GoodsStockCalculationResult doCalculateSyncQuantity(StockCalculationRule rule, BigDecimal actualStock) {
        // 固定数量计算
        if (rule.getExtRuleType() == StockGoodsExtRuleTypeEnum.FIX) {
            return StockContentResult.success(rule.getFixedQuantity());
        }
        // 库存数量处于生效范围内：同步固定数量
        if(rule.getExtRuleType() == StockGoodsExtRuleTypeEnum.CONDITION_FIX){
            if (actualStock.compareTo(rule.getMinStockQuantity()) >= 0 && rule.getMaxStockQuantity().compareTo(actualStock) >= 0) {
                return StockContentResult.success(rule.getFixedQuantity());
            }
        }

        BigDecimal stockQuantityResult = actualStock;
        // 同步百分比计算
        if (rule.getPercent().compareTo(BigDecimal.ZERO) >= 0) {
            stockQuantityResult = percentCalculation(actualStock, rule);
        }
        // 条件固定增加数量
        if (rule.getExtRuleType() == StockGoodsExtRuleTypeEnum.CONDITION_FIX_INCREASE) {
            // 库存数量处于生效范围内：累加增量数量
            if (actualStock.compareTo(rule.getMinStockQuantity()) >= 0 && rule.getMaxStockQuantity().compareTo(actualStock) >= 0) {
                stockQuantityResult = stockQuantityResult.add(rule.getIncrementalQuantity());
            }
        }

        return StockContentResult.success(stockQuantityResult);
    }

    /**
     * 获取策略名称
     *
     * @return 策略名称
     */
    @Override
    public String getStrategyName() {
        return "默认库存计算策略";
    }
    //endregion

    //region 私有方法
    /**
     * 基于规则的百分比计算
     *
     * @param stockQuantity 当前库存数量
     * @return 计算后结果
     */
    private BigDecimal percentCalculation(BigDecimal stockQuantity, StockCalculationRule rule) {
        // 库存数量乘以百分比
        BigDecimal syncQuantity = stockQuantity.multiply(rule.getPercent().divide(BigDecimal.valueOf(100), 3, RoundingMode.HALF_DOWN));
        // 向上取整
        if (rule.isbSyncPctStock()) {
            syncQuantity = syncQuantity.setScale(0, RoundingMode.UP);
        }
        // 向下取整
        else {
            syncQuantity = syncQuantity.setScale(0, RoundingMode.DOWN);
        }
        return syncQuantity;
    }
    //endregion
}
