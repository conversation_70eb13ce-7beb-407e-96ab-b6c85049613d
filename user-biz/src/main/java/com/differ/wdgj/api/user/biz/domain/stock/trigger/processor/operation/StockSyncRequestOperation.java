package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.operation;

import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.batchSyncStock.BusinessBatchSyncStockRequestGoodInfo;
import com.differ.wdgj.api.user.biz.domain.stock.data.GoodsMatchEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.GoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncGoodsRequestPair;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockShopConfig;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * 库存同步请求操作
 *
 * <AUTHOR>
 * @date 2025/6/20 15:43
 */
public class StockSyncRequestOperation {
    // region 常量

    /**
     * 上下文
     */
    protected final StockSyncContext context;

    // endregion

    // region 构造方法

    /**
     * 构造方法
     *
     * @param context 上下文
     */
    public StockSyncRequestOperation(StockSyncContext context) {
        this.context = context;
    }

    // endregion

    /**
     * 封装商品级请求对
     *
     * @param matchEnhance   匹配数据
     * @param goodsStockInfo 商品库存量信息
     * @return 结果
     */
    public StockSyncGoodsRequestPair formatGoodsRequestPair(GoodsMatchEnhance matchEnhance, GoodsStockCalculationResult goodsStockInfo){
        BusinessBatchSyncStockRequestGoodInfo requestGoodInfo = formatGoodsRequest(matchEnhance, goodsStockInfo);
        return new StockSyncGoodsRequestPair(matchEnhance, goodsStockInfo, requestGoodInfo);
    }

    /**
     * 封装商品级请求
     *
     * @param matchEnhance   匹配数据
     * @param goodsStockInfo 商品库存量信息
     * @return 结果
     */
    public BusinessBatchSyncStockRequestGoodInfo formatGoodsRequest(GoodsMatchEnhance matchEnhance, GoodsStockCalculationResult goodsStockInfo) {
        ApiSysMatchDO goodsMatch = matchEnhance.getSysMatch();

        // 封装请求
        BusinessBatchSyncStockRequestGoodInfo goodsRequest = new BusinessBatchSyncStockRequestGoodInfo();
        goodsRequest.setOuterId(goodsMatch.gettBOuterID());
        goodsRequest.setOutSkuId(goodsMatch.getSkuOuterID());
        goodsRequest.setPlatProductId(goodsMatch.getNumiid());
        goodsRequest.setSkuId(goodsMatch.getSkuID());
        goodsRequest.setSyncStockType(SyncStockTypeEnum.Whole.getCode());
        // 库存量赋值
        goodsRequest.setQuantity(goodsStockInfo.getStockCount().intValue());
        goodsRequest.setRealQuantity(goodsStockInfo.getRealStockCount().intValue());
        // 默认多仓信息
        goodsRequest.setWhseCode(matchEnhance.getMultiSign());
        // 设置上下架状态
        goodsRequest.setStatus(this.getUpOrDownStatus(goodsStockInfo.getStockCount()));

        return goodsRequest;
    }

    /**
     * 获取上下架状态
     *
     * @param quantity 计算出的库存数量
     * @return 上下架状态
     */
    public String getUpOrDownStatus(BigDecimal quantity) {
        if(context == null || context.getSyncStockConfig() == null){
            return StringUtils.EMPTY;
        }

        // 店铺配置控制，当库存大于0时是否自动上架
        SyncStockShopConfig syncStockConfig = context.getSyncStockConfig();
        if(syncStockConfig.getIsSyncMoreZeroAutoShelves()){
            return quantity.compareTo(BigDecimal.ZERO) > 0 ? "up" : "down";
        }

        return StringUtils.EMPTY;
    }
}
