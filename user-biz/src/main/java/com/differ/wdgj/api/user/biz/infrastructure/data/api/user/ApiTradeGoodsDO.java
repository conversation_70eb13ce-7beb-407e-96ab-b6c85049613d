package com.differ.wdgj.api.user.biz.infrastructure.data.api.user;

import com.differ.wdgj.api.user.biz.domain.order.common.data.enums.OrderGoodsSendStatusEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 原始单货品(g_api_tradegoods)
 *
 * <AUTHOR>
 * @date 2024/7/17 下午6:58
 */
public class ApiTradeGoodsDO {
    /**
     * 自增主键
     */
    private int recId;

    /**
     * 序号
     */
    private Integer recNo;

    /**
     * 和原始单表关联
     */
    private Integer billId;

    /**
     * 平台编码
     */
    private String tradeGoodsNO;

    /**
     * 平台货品名称
     */
    private String tradeGoodsName;

    /**
     * 平台规格名称
     */
    private String tradeGoodsSpec;

    /**
     * 数量
     */
    private BigDecimal goodsCount;

    /**
     * 退款数量
     */
    private BigDecimal refundCount;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 优惠金额
     */
    private BigDecimal discountMoney;

    /**
     * 套餐，一般为空
     */
    private String otherGoods;

    /**
     * 货品备注
     */
    private String remark;

    /**
     * 应该不用，使用g_api_tradelist表的，0待过滤；1等扫描；2待递交；3放弃；4已递交；11退款；13部份退款
     */
    private Integer curStatus;

    /**
     * 管家商品Id
     */
    private Integer goodsId;

    /**
     * 管家规格Id
     */
    private Integer specId;

    /**
     * 管家规格名称
     */
    private String goodsSpec;

    /**
     * 是否是组合装
     */
    private int bFit;

    /**
     * 是否超卖
     */
    private int isOversold;

    /**
     * 某些平台特殊数据，苏宁的包裹号和支付号
     */
    private String cStatus;

    /**
     * 快递单打印的价格
     */
    private BigDecimal printPrice;

    /**
     * 平台子交易编号
     */
    private String oid;

    /**
     * 快递单号：用于拆单发货，淘宝不支持按照数量拆分，所以只有一个快递单号，如果是其它平台，则以： postid1:sellcount1;postid2:sellcount2;postid3:sellcount3;支持拆单发货的平台有： '菠萝派','独立商城','淘宝网','天猫商城','有赞','口袋通','唯品会'
     */
    private String posNo;

    /**
     * 发货方式
     */
    private String sndStyle;

    /**
     * 发货状态 0=待发货，1=已发货，2=部分发货，3=发货失败
     * {@link OrderGoodsSendStatusEnum}
     */
    private int bSend;

    /**
     * 货品状态：'退款成功','付款以前，卖家或买家主动关闭交易' ,'卖家已经同意退款，等待买家退货' ,'买家已经退货，等待卖家确认收货' 买家已经申请退款，等待卖家同意
     */
    private String goodsTypeCh;

    /**
     * 拆单发货时的同步状态
     */
    private String synCause;

    /**
     * 货品税额
     */
    private BigDecimal tax;

    /**
     * trade id
     */
    private Integer tradeID;

    /**
     * 货品分摊后实付金额（严选仓储接口相关）
     */
    private BigDecimal shareMoney;

    /**
     * 架海金梁仓库类型 CN：菜鸟仓  SC:商家仓
     */
    private String consignType;

    /**
     * 平台商品ID
     */
    private String platGoodsID;

    /**
     * 平台SKU ID
     */
    private String platSkuID;

    /**
     * 预售发货时间，具体日期，格式2018-03-29 19:00:00
     */
    private LocalDateTime estimateSendTime;

    /**
     * 淘宝商品图片地址
     */
    private String tradeGoodsPic;

    /**
     * 达人(主播)ID
     */
    private String authorId;

    /**
     * 达人(主播)昵称
     */
    private String authorName;

    /**
     * 原始商品编码
     */
    private String origTradeGoodsNO;

    /**
     * 拓展信息，xml格式
     */
    private String extendedField;

    //region get/set

    public int getRecId() {
        return recId;
    }

    public void setRecId(int recId) {
        this.recId = recId;
    }

    public Integer getRecNo() {
        return recNo;
    }

    public void setRecNo(Integer recNo) {
        this.recNo = recNo;
    }

    public Integer getBillId() {
        return billId;
    }

    public void setBillId(Integer billId) {
        this.billId = billId;
    }

    public String getTradeGoodsNO() {
        return tradeGoodsNO;
    }

    public void setTradeGoodsNO(String tradeGoodsNO) {
        this.tradeGoodsNO = tradeGoodsNO;
    }

    public String getTradeGoodsName() {
        return tradeGoodsName;
    }

    public void setTradeGoodsName(String tradeGoodsName) {
        this.tradeGoodsName = tradeGoodsName;
    }

    public String getTradeGoodsSpec() {
        return tradeGoodsSpec;
    }

    public void setTradeGoodsSpec(String tradeGoodsSpec) {
        this.tradeGoodsSpec = tradeGoodsSpec;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public BigDecimal getRefundCount() {
        return refundCount;
    }

    public void setRefundCount(BigDecimal refundCount) {
        this.refundCount = refundCount;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getDiscountMoney() {
        return discountMoney;
    }

    public void setDiscountMoney(BigDecimal discountMoney) {
        this.discountMoney = discountMoney;
    }

    public String getOtherGoods() {
        return otherGoods;
    }

    public void setOtherGoods(String otherGoods) {
        this.otherGoods = otherGoods;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getCurStatus() {
        return curStatus;
    }

    public void setCurStatus(Integer curStatus) {
        this.curStatus = curStatus;
    }

    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    public Integer getSpecId() {
        return specId;
    }

    public void setSpecId(Integer specId) {
        this.specId = specId;
    }

    public String getGoodsSpec() {
        return goodsSpec;
    }

    public void setGoodsSpec(String goodsSpec) {
        this.goodsSpec = goodsSpec;
    }

    public int getbFit() {
        return bFit;
    }

    public void setbFit(int bFit) {
        this.bFit = bFit;
    }

    public int getIsOversold() {
        return isOversold;
    }

    public void setIsOversold(int isOversold) {
        this.isOversold = isOversold;
    }

    public String getcStatus() {
        return cStatus;
    }

    public void setcStatus(String cStatus) {
        this.cStatus = cStatus;
    }

    public BigDecimal getPrintPrice() {
        return printPrice;
    }

    public void setPrintPrice(BigDecimal printPrice) {
        this.printPrice = printPrice;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getPosNo() {
        return posNo;
    }

    public void setPosNo(String posNo) {
        this.posNo = posNo;
    }

    public String getSndStyle() {
        return sndStyle;
    }

    public void setSndStyle(String sndStyle) {
        this.sndStyle = sndStyle;
    }

    public int getbSend() {
        return bSend;
    }

    public void setbSend(int bSend) {
        this.bSend = bSend;
    }

    public String getGoodsTypeCh() {
        return goodsTypeCh;
    }

    public void setGoodsTypeCh(String goodsTypeCh) {
        this.goodsTypeCh = goodsTypeCh;
    }

    public String getSynCause() {
        return synCause;
    }

    public void setSynCause(String synCause) {
        this.synCause = synCause;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public Integer getTradeID() {
        return tradeID;
    }

    public void setTradeID(Integer tradeID) {
        this.tradeID = tradeID;
    }

    public BigDecimal getShareMoney() {
        return shareMoney;
    }

    public void setShareMoney(BigDecimal shareMoney) {
        this.shareMoney = shareMoney;
    }

    public String getConsignType() {
        return consignType;
    }

    public void setConsignType(String consignType) {
        this.consignType = consignType;
    }

    public String getPlatGoodsID() {
        return platGoodsID;
    }

    public void setPlatGoodsID(String platGoodsID) {
        this.platGoodsID = platGoodsID;
    }

    public String getPlatSkuID() {
        return platSkuID;
    }

    public void setPlatSkuID(String platSkuID) {
        this.platSkuID = platSkuID;
    }

    public LocalDateTime getEstimateSendTime() {
        return estimateSendTime;
    }

    public void setEstimateSendTime(LocalDateTime estimateSendTime) {
        this.estimateSendTime = estimateSendTime;
    }

    public String getTradeGoodsPic() {
        return tradeGoodsPic;
    }

    public void setTradeGoodsPic(String tradeGoodsPic) {
        this.tradeGoodsPic = tradeGoodsPic;
    }

    public String getAuthorId() {
        return authorId;
    }

    public void setAuthorId(String authorId) {
        this.authorId = authorId;
    }

    public String getAuthorName() {
        return authorName;
    }

    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }

    public String getOrigTradeGoodsNO() {
        return origTradeGoodsNO;
    }

    public void setOrigTradeGoodsNO(String origTradeGoodsNO) {
        this.origTradeGoodsNO = origTradeGoodsNO;
    }

    public String getExtendedField() {
        return extendedField;
    }

    public void setExtendedField(String extendedField) {
        this.extendedField = extendedField;
    }

    //endregion
}
