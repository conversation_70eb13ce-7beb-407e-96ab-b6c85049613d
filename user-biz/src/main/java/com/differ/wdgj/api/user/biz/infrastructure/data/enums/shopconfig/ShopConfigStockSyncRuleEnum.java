package com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig;

import com.differ.wdgj.api.component.util.enums.ValueEnum;

/**
 * 店铺配置库存同步规则
 *
 * <AUTHOR>
 * @date 2025/6/17 10:24
 */
public enum ShopConfigStockSyncRuleEnum implements ValueEnum {
    RULE_ONE(1, "实际库存-未付款数量-订购量-待发货量"),
    RULE_TWO(2, "实际库存-订购量-待发货量"),
    RULE_THREE(3, "实际库存"),
    RULE_FOUR(4, "实际库存-未付款数量+采购在途数量-订购量-待发货量"),
    RULE_FIVE(5, "实际库存+采购在途数量-订购量-待发货量"),
    RULE_SIX(6, "实际库存-未付款数量"),
    RULE_SEVEN(7, "实际库存+采购在途数量"),
    RULE_EIGHT(8, "实际库存-订购量"),
    RULE_NINE(9, "实际库存-待发货量"),
    RULE_TEN(10, "实际库存+采购在途数量-订购量"),
    RULE_ELEVEN(11, "实际库存-未付款数量-订购量"),
    RULE_TWELVE(12, "实际库存+采购在途数量-待发货量"),
    RULE_THIRTEEN(13, "实际库存-未付款数量-待发货量"),
    RULE_FOURTEEN(14, "实际库存-未付款数量+采购在途数量"),
    RULE_FIFTEEN(15, "实际库存-未付款数量+采购在途数量-待发货量"),
    RULE_SIXTEEN(16, "实际库存-未付款数量+采购在途数量-订购量"),
    RULE_SEVENTEEN(17, "实际库存+调入在途数量"),
    RULE_EIGHTEEN(18, "实际库存-未付款数量+调入在途数量"),
    RULE_NINETEEN(19, "实际库存+采购在途数量+调入在途数量"),
    RULE_TWENTY(20, "实际库存+调入在途数量-订购量"),
    RULE_TWENTYONE(21, "实际库存+调入在途数量-待发货量"),
    RULE_TWENTYTWO(22, "实际库存+调入在途数量-订购量-待发货量"),
    RULE_TWENTYTHREE(23, "实际库存+采购在途数量+调入在途数量-订购量"),
    RULE_TWENTYFOUR(24, "实际库存-未付款数量+调入在途数量-订购量"),
    RULE_TWENTYFIVE(25, "实际库存+采购在途数量+调入在途数量-待发货量"),
    RULE_TWENTYSIX(26, "实际库存-未付款数量+调入在途数量-待发货量"),
    RULE_TWENTYSEVEN(27, "实际库存-未付款数量+采购在途数量+调入在途数量"),
    RULE_TWENTYEIGHT(28, "实际库存+采购在途数量+调入在途数量-订购量-待发货量"),
    RULE_TWENTYNINE(29, "实际库存-未付款数量+调入在途数量-订购量-待发货量"),
    RULE_THIRTY(30, "实际库存-未付款数量+采购在途数量+调入在途数量-待发货量"),
    RULE_THIRTYONE(31, "实际库存-未付款数量+采购在途数量+调入在途数量-订购量"),
    RULE_THIRTYTWO(32, "实际库存-未付款数量+采购在途数量+调入在途数量-订购量-待发货量"),
    ;

    /**
     * 枚举值<p/>
     * 注意定义需要保持和中心库dev_shopConfig一致
     */
    private final int value;

    /**
     * 枚举描述
     */
    private final String name;


    //region 构造
    ShopConfigStockSyncRuleEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }
    //endregion

    /**
     * 获取枚举值
     *
     * @return 枚举值
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取枚举描述
     *
     * @return 枚举描述
     */
    public String getName() {
        return name;
    }
}
