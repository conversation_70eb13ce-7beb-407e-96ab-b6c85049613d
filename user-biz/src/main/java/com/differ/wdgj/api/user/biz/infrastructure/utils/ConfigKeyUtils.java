package com.differ.wdgj.api.user.biz.infrastructure.utils;

import com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.DbConfigLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.cryptography.APICryptography;
import org.apache.commons.lang3.StringUtils;

/**
 * 配置键工具类
 *
 * <AUTHOR>
 * @date 2024-03-11 19:44
 */
public class ConfigKeyUtils {

    private ConfigKeyUtils() {
    }

    //region 常量

    /**
     * 加密固定的头部信息
     */
    private static final String DB_CONFIG_VALUE_ENCRYPT_HEAD = "☯☼$#**#$☼☯";

    /**
     * 配置键值分割符合 - 正则竖线
     */
    public static final String REGEX_VERTICAL_LINE = "\\|";

    /**
     * 配置键值分割符合 - 竖线
     */
    public static final String VERTICAL_LINE = "|";

    /**
     * 配置键值分割符合 - 警号
     */
    public static final String WARNING_SIGNAL = "#";

    /**
     * 配置键值ALL
     */
    public static final String ALL = "ALL";

    /**
     * 默认分隔符
     */
    private static final String DEFAULT_SEPARATOR_CHAR = "=";

    /**
     * 配置键值分割符合 - $
     */
    public static final String REGEX_DOLLAR_SIGNAL = "\\$";

    //endregion

    //region 管家相关配置键操作 - 新逻辑禁用

    /**
     * 获取管家配置键值
     * dotNet逻辑兼容，后续建议使用API配置键
     *
     * @param configKey 配置键
     * @return 配置键值
     */
    @Deprecated
    public static String getWdgjConfigValue(ConfigKeyEnum configKey) {
        return getConfigValue(configKey, ConfigKeyTypeEnum.WDGJ);
    }

    /**
     * 管家配置键 - 多维度配置键匹配
     * dotNet逻辑兼容，后续建议使用API配置键
     *
     * @param configKey   配置键
     * @param splitChars  分割符号
     * @param matchValues 匹配值
     * @return 是否匹配
     */
    @Deprecated
    public static boolean isActionWdgjMultipleMatchValue(ConfigKeyEnum configKey, String[] splitChars, String... matchValues) {
        String configValue = getWdgjConfigValue(configKey);
        return isActionMultipleMatchValue(configValue, splitChars, matchValues);
    }

    /**
     * 单维度配置键匹配
     * dotNet逻辑兼容，后续建议使用API配置键
     *
     * @param configKey  配置键
     * @param matchValue 匹配值
     * @return 是否匹配
     */
    @Deprecated
    public static boolean isActionWdgj(ConfigKeyEnum configKey, String matchValue) {
        String configValue = getWdgjConfigValue(configKey);
        return isMatch(configValue, matchValue, VERTICAL_LINE);
    }

    /**
     * 异步单维度配置键匹配
     * 存在循环调用逻辑时使用，例如log
     * dotNet逻辑兼容，后续建议使用API配置键
     *
     * @param configKey  配置键
     * @param matchValue 匹配值
     * @return 是否匹配
     */
    @Deprecated
    public static boolean isActionWdgjAsync(ConfigKeyEnum configKey, String matchValue) {
        String configValue = asyncGetConfigValue(configKey, ConfigKeyTypeEnum.WDGJ);
        return isMatch(configValue, matchValue, VERTICAL_LINE);
    }

    //endregion

    //region API相关配置键操作

    /**
     * 获取API配置键值
     *
     * @param configKey 配置键
     * @return 配置键值
     */
    public static String getApiConfigValue(ConfigKeyEnum configKey) {
        return getConfigValue(configKey, ConfigKeyTypeEnum.API);
    }

    /**
     * Api配置键-配置键是否为ture
     *
     * @param configKey 配置键
     * @return 结果
     */
    public static boolean isActionApiBoolean(ConfigKeyEnum configKey){
        String configValue = getApiConfigValue(configKey);
        if(StringUtils.isEmpty(configValue)){
            return false;
        }
        return StringUtils.equals("1", configValue);
    }

    /**
     * Api配置键 - 多维度配置键匹配
     *
     * @param configKey   配置键
     * @param splitChars  分割符号
     * @param matchValues 匹配值
     * @return 是否匹配
     */
    public static boolean isActionApiMultipleMatchValue(ConfigKeyEnum configKey, String[] splitChars, String... matchValues) {
        String configValue = getApiConfigValue(configKey);
        return isActionMultipleMatchValue(configValue, splitChars, matchValues);
    }

    /**
     * 单维度配置键匹配
     *
     * @param configKey  配置键
     * @param matchValue 匹配值
     * @return 是否匹配
     */
    public static boolean isActionApi(ConfigKeyEnum configKey, String matchValue) {
        String configValue = getApiConfigValue(configKey);
        return isMatch(configValue, matchValue, VERTICAL_LINE);
    }

    /**
     * 获取配置键内容（|A=B|）根据A找B(可配置默认值：例如 |default=60|)
     *
     * @param configKey    配置键
     * @param prefix       匹配项
     * @param defaultValue 默认值
     * @return 配置键内容
     */
    public static String getConfigBySeparatorDefault(ConfigKeyEnum configKey, String prefix, String defaultValue) {
        return getConfigBySeparatorDefault(configKey, ConfigKeyTypeEnum.API, prefix, DEFAULT_SEPARATOR_CHAR, defaultValue);
    }

    /**
     * 获取配置键内容（|A=B|）根据A找B(可配置默认值：例如 |A=60|)
     *
     * @param configKey     配置键
     * @param prefix        匹配项
     * @param separatorChar 分割字符串
     * @param defaultValue  默认值
     * @return 配置键内容
     */
    public static String getConfigBySeparatorDefault(ConfigKeyEnum configKey, ConfigKeyTypeEnum configKeyType, String prefix, String separatorChar, String defaultValue) {
        String config = getConfigByPrefix(configKey, configKeyType, prefix, separatorChar, "");
        if (StringUtils.isBlank(config)) {
            config = getConfigByPrefix(configKey, configKeyType, "default", separatorChar, defaultValue);
        }
        return config;
    }

    /**
     * 获取配置键内容（|A-B|）根据A找B
     *
     * @param configKey    配置键
     * @param prefix       匹配项
     * @param defaultValue 默认值
     * @return 查找结果
     */
    public static String getConfigByPrefix(ConfigKeyEnum configKey, ConfigKeyTypeEnum configKeyType, String prefix, String separatorChar, String defaultValue) {
        String configValue = getConfigValue(configKey, configKeyType, true);
        if (StringUtils.isBlank(configValue)) {
            return defaultValue;
        }

        String s = String.format("|%s%s", prefix, separatorChar);
        int index1 = configValue.indexOf(s);
        if (index1 < 0) {
            return defaultValue;
        }

        int index2 = configValue.indexOf("|", index1 + s.length());
        if (index2 < 0) {
            return defaultValue;
        }

        return configValue.substring(index1 + s.length(), index2);
    }

    //endregion

    //region 基础逻辑

    /**
     * 获取配置键值
     *
     * @param configKey     配置键
     * @param configKeyType 配置键配置键类别
     * @return 配置键值
     */
    public static String getConfigValue(ConfigKeyEnum configKey, ConfigKeyTypeEnum configKeyType) {
        return getConfigValue(configKey, configKeyType, true);
    }

    /**
     * 异步获取配置键值
     *
     * @param configKey     配置键
     * @param configKeyType 配置键配置键类别
     * @return 配置键值
     */
    public static String asyncGetConfigValue(ConfigKeyEnum configKey, ConfigKeyTypeEnum configKeyType) {
        return getConfigValue(configKey, configKeyType, false);
    }

    /**
     * 多维度配置键匹配
     *
     * @param configValue 配置键值
     * @param splitChars  分割符号
     * @param matchValues 匹配值
     * @return 是否匹配
     */
    public static boolean isActionMultipleMatchValue(String configValue, String[] splitChars, String... matchValues) {
        if (StringUtils.isEmpty(configValue)) {
            return false;
        }

        String[] configArr = StringUtils.split(configValue, REGEX_VERTICAL_LINE);
        for (String config : configArr) {
            String separatorChar = String.format("%s%s%s", "[", StringUtils.join(splitChars, ""), "]");
            String[] subConfigArr = StringUtils.split(config, separatorChar);
            boolean match = true;
            for (int i = 0; i < subConfigArr.length; i++) {
                match = isMatch(subConfigArr[i], matchValues[i], WARNING_SIGNAL);
                if (!match) {
                    break;
                }
            }
            if (match) {
                return match;
            }
        }

        return false;
    }

    /**
     * 验证两个值是否匹配
     *
     * @param configValue  配置键值
     * @param currentValue 当前值
     * @param splitChar    分割符
     * @return 是否匹配
     */
    public static boolean isMatch(String configValue, String currentValue, String splitChar) {
        if (StringUtils.isBlank(configValue)) {
            return false;
        }

        if (StringUtils.equalsIgnoreCase(configValue, ALL)) {
            return true;
        }

        String fullConfigValue = String.format("%s%s%s", splitChar, configValue, splitChar);
        String currentConfigValue = String.format("%s%s%s", splitChar, currentValue, splitChar);
        return fullConfigValue.contains(currentConfigValue);
    }

    //endregion

    //region 私有方法

    /**
     * 获取配置键值
     *
     * @param configKey 配置键
     * @return 配置键值
     */
    public static String getConfigValue(ConfigKeyEnum configKey) {
        return getConfigValue(configKey, ConfigKeyTypeEnum.API, true);
    }

    /**
     * 获取配置键值
     *
     * @param configKey     配置键
     * @param configKeyType 配置键配置键类别
     * @param bSync         是否同步
     * @return 配置键值
     */
    private static String getConfigValue(ConfigKeyEnum configKey, ConfigKeyTypeEnum configKeyType, boolean bSync) {
        // 获取配置键
        String configValue = bSync
                ? DbConfigLocalCache.singleton().getConfigValue(configKey, configKeyType)
                : DbConfigLocalCache.singleton().asyncGetConfigValue(configKey, configKeyType);

        // 默认值逻辑
        if (StringUtils.isEmpty(configValue)) {
            return configKey.getDefaultValue();
        }

        // 解密
        if (configValue.startsWith(DB_CONFIG_VALUE_ENCRYPT_HEAD)) {
            return APICryptography.singleton().decrypt(configValue.replace(DB_CONFIG_VALUE_ENCRYPT_HEAD, ""));
        }

        return configValue;
    }

    //endregion
}
