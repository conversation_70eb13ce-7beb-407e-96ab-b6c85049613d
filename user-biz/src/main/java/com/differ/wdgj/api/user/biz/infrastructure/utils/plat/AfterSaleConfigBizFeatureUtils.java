package com.differ.wdgj.api.user.biz.infrastructure.utils.plat;

import com.differ.wdgj.api.user.biz.infrastructure.data.dto.bizfeature.plugins.AfterSalesConfigContent;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.bizfeature.PlatBizFeatureTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.ShopInfoUtils;

/**
 * 平台业务特性工具类 - 售后配置
 *
 * <AUTHOR>
 * @date 2024/7/16 上午10:15
 */
public class AfterSaleConfigBizFeatureUtils {
    //region 常量
    /**
     * 业务类型
     */
    private static final PlatBizFeatureTypeEnum TYPE = PlatBizFeatureTypeEnum.AFTER_SALES_CONFIG;
    //endregion

    //region 构造
    private AfterSaleConfigBizFeatureUtils() {
    }
    //endregion

    /**
     * 获取业务特性
     *
     * @param memberName   会员名
     * @param outShopId    外部店铺Id
     * @return 结果
     */
    public static AfterSalesConfigContent getFeature(String memberName, int outShopId) {
        ApiShopBaseDto apiShopBaseDto = ShopInfoUtils.singleByOutShopId(memberName, outShopId);
        if (apiShopBaseDto == null) {
            return null;
        }

        return getFeature(apiShopBaseDto);
    }

    /**
     * 获取业务特性
     *
     * @param shopBase     店铺基础信息
     * @return 结果
     */
    public static AfterSalesConfigContent getFeature(ApiShopBaseDto shopBase) {
        return getFeature(shopBase.getPlat(), shopBase.getOutAccount(), shopBase.getToken());
    }

    /**
     * 获取业务特性
     *
     * @param plat         平台
     * @param memberName   会员名
     * @param shopToken    店铺Token
     * @return 结果
     */
    public static AfterSalesConfigContent getFeature(PolyPlatEnum plat, String memberName, String shopToken) {
        // 业务约定售后配置暂时不适用特殊键约束
        return (AfterSalesConfigContent) PlatBizFeatureUtils.getFeature(TYPE, plat, memberName, shopToken, null);
    }
}
