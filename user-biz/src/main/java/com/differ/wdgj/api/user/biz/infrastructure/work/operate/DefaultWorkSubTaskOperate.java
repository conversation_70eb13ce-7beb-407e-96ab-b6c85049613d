package com.differ.wdgj.api.user.biz.infrastructure.work.operate;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.component.util.tools.Md5Utils;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.WorkRunningCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiWorkTaskDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.SwitchDbContext;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiWorkTaskMapper;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.*;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.TriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkErrorCodeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.event.handler.WorkEventHandler;
import com.differ.wdgj.api.user.biz.infrastructure.work.operate.cache.LocalProgressCache;
import org.apache.commons.lang3.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 工作任务数据操作
 *
 * <AUTHOR>
 * @date 2024/7/2 15:13
 */
public class DefaultWorkSubTaskOperate<S extends SubPageTask> implements WorkSubTaskOperate<S> {
    //region 常量
    /**
     * 工作任务类型
     */
    private final WorkEnum workEnum;

    /**
     * 工作任务数据库操作
     */
    private ApiWorkTaskMapper mapper;

    /**
     * 事件处理器
     */
    private WorkEventHandler eventHandler;

    private static final Logger LOG = LoggerFactory.getLogger(DefaultWorkSubTaskOperate.class);
    //endregion

    //region 构造
    public DefaultWorkSubTaskOperate(WorkEnum workEnum) {
        this.workEnum = workEnum;
    }
    //endregion

    //region 实现基类方法
    /**
     * 获取工作任务更新操作器
     *
     * @param context 数据操作上下文
     * @return 工作任务更新操作器
     */
    @Override
    public WorkDetailOperate<S> getDetailOperate(DataOperateContext context) {
        return new DefaultWorkDetailOperate<>(context);
    }

    @Override
    public CreateResult create(WorkData<?> workData) {
        // 生成任务ID
        long taskId = generateTaskId(workData);
        // 赋值入库对象
        ApiWorkTaskDO apiWorkTask = new ApiWorkTaskDO();
        apiWorkTask.setTaskId(String.valueOf(taskId));
        apiWorkTask.setTriggerType(workData.getTriggerType().getByteValue());
        apiWorkTask.setShopId(workData.getShopId());
        apiWorkTask.setTaskType(workData.getWorkType().getValue());
        apiWorkTask.setTaskStatus(WorkStatus.WAIT_RUN.getByteValue());
        apiWorkTask.setCreator(workData.getCreator());
        apiWorkTask.setTaskInfo(JsonUtils.toJson(workData.getData()));

        CreateResult createResult = null;
        try {
            // 添加任务入库
            int ret = DBSwitchUtil.doDBWithContext(SwitchDbContext.buildWDGJ(workData.getMemberName()), () -> getApiWorkTaskMapper().addWait(apiWorkTask));
            if (ret > 0) {
                // 设置结果，任务ID
                createResult = CreateResult.successResult(String.valueOf(taskId));
            } else {
                createResult = CreateResult.failResult(WorkErrorCodeEnum.WORK_ADD_FAIL.getCode());
            }
        } catch (Exception ex) {
            LOG.error(String.format("添加任务到数据库异常:%s", JsonUtils.toJson(workData)), ex);
            createResult = CreateResult.failResult(WorkErrorCodeEnum.ERROR.getCode());
        }
        if (createResult.success()) {
            // 成功后发布任务变化事件
            publishEvent(WorkStatus.WAIT_RUN, DataOperateContext.of(workData.getMemberName(), workData.getWorkType(), createResult.getTaskId()));
        }
        // 返回结果
        return createResult;
    }

    @Override
    public WorkData<?> getWork(String memberName, String taskId) {
        ApiWorkTaskDO apiWorkTask = DBSwitchUtil.doDBWithContext(SwitchDbContext.buildWDGJ(memberName), () -> getApiWorkTaskMapper().singleByTaskId(taskId));
        if (null == apiWorkTask) {
            return null;
        }

        WorkContext workContext = new WorkContext();
        workContext.setWorkType(WorkEnum.create(String.valueOf(apiWorkTask.getTaskType())));
        workContext.setCreator(apiWorkTask.getCreator());
        workContext.setMember(memberName);
        workContext.setShopId(apiWorkTask.getShopId());
        workContext.setTriggerType(TriggerTypeEnum.create(String.valueOf(apiWorkTask.getTriggerType())));

        // 获取WorkData泛型的数据类型
        Class<WorkArgs> workArgsClass = (Class<WorkArgs>) workEnum.getWorkFactory().getWorkDataArgClass();
        if (null == workArgsClass) {
            return null;
        }

        WorkArgs workArgs = JsonUtils.deJson(apiWorkTask.getTaskInfo(), workArgsClass);
        return WorkData.of(workContext, workArgs);
    }

    @Override
    public void cleanComplete() {

    }

    @Override
    public void query() {

    }

    @Override
    public void totalExec() {

    }

    /**
     * 是否存在未完成的工作任务
     *
     * @param workData 工作数据
     * @return true:存在
     */
    @Override
    public boolean existsUnComplete(WorkData<?> workData) {
        Integer lineCount = DBSwitchUtil.doDBWithContext(SwitchDbContext.buildWDGJ(workData.getMemberName()),
                () -> getApiWorkTaskMapper().existsUnComplete(workData.getWorkType().getValue(), workData.getShopId()));
        return lineCount != null && lineCount > 0;
    }

    /**
     * 查询待检测的未完成任务
     *
     * @param member   会员名
     * @param workEnum 任务类型
     * @return 未完成任务Id列表
     */
    @Override
    public List<String> listToCheckWork(String member, WorkEnum workEnum) {
        return DBSwitchUtil.doDBWithContext(SwitchDbContext.buildWDGJ(member), () -> getApiWorkTaskMapper().listToCheckWork(workEnum.getValue()));
    }

    /**
     * 将工作任务重置为失败
     *
     * @param taskId 任务ID
     */
    @Override
    public void workComplete(String member, String taskId, WorkResult workResult) {
        DataOperateContext context = DataOperateContext.of(member, this.workEnum, taskId);
        this.getDetailOperate(context).complete(member, taskId, workResult);
    }
    //endregion

    //region 私有方法
    /**
     * 工作任务数据库操作
     *
     * @return 工作任务数据库操作
     */
    private ApiWorkTaskMapper getApiWorkTaskMapper() {
        if (mapper == null) {
            mapper = BeanContextUtil.getBean(ApiWorkTaskMapper.class);
        }
        return mapper;
    }

    /**
     * 生成任务ID
     *
     * @param workData 工作数据
     * @return 任务ID
     */
    protected long generateTaskId(WorkData workData) {
        return Md5Utils.encryptHash(String.format("%s_%s_%s_%s_%d_%d", workData.getMemberName(), workData.getWorkType(), workData.getShopId(), workData.getTriggerType(), System.currentTimeMillis(), RandomUtils.nextInt(0, 99999)));
    }

    /**
     * 发布工作任务变化事件
     *
     * @param status 状态
     * @param context 上下文
     */
    protected void publishEvent(WorkStatus status, DataOperateContext context) {
        this.getEventHandler().handle(new WorkChangeEvent(context, status));
    }

    /**
     * 获取事件处理器
     *
     * @return 事件处理器
     */
    private WorkEventHandler getEventHandler() {
        if (this.eventHandler == null) {
            this.eventHandler = this.workEnum.getWorkFactory().createEventHandler();
        }
        return this.eventHandler;
    }
    //endregion

    /**
     * 工作任务数据操作
     *
     * @param <S>
     */
    static class DefaultWorkDetailOperate<S extends SubPageTask> implements WorkDetailOperate<S> {
        //region 变量
        /**
         * 工作任务类型
         */
        private final WorkEnum workEnum;

        /**
         * 上下文
         */
        private final DataOperateContext context;

        /**
         * 工作任务操作缓存
         */
        private WorkRunningCache workRunningCache;

        /**
         * 工作任务数据库操作
         */
        private ApiWorkTaskMapper mapper;

        /**
         * 事件处理器
         */
        private WorkEventHandler eventHandler;

        /**
         * 完成时的最后进度值
         */
        private static final int COMPLETE_PROGRESS = 100;
        //endregion

        //region 构造
        public DefaultWorkDetailOperate(DataOperateContext context) {
            this.workEnum = context.getWorkEnum();
            this.context = context;
        }
        //endregion

        //region 实现基类方法
        @Override
        public void setProgress(int value) {
            // 设置redis进度缓存值
            this.getWorkRunningCache().setProgressValue(value, false);
            // 设置内存进度缓存
            LocalProgressCache.singleton().setProgress(this.context, value);
            // 任务进度变化,发布事件
            publishEvent(WorkStatus.RUNNING, this.context);
        }

        @Override
        public void increProgress(int value) {
            // 增量redis进度缓存值
            this.getWorkRunningCache().incrementProgress(value);
            // 增量内存进度缓存
            LocalProgressCache.singleton().increProgress(this.context, value);
            // 任务进度变化,发布事件
            publishEvent(WorkStatus.RUNNING, this.context);
        }

        @Override
        public void initToExecute(String member, String taskId) {
            // 初始化数据库任务状态执行中
            DBSwitchUtil.doDBWithContext(SwitchDbContext.buildWDGJ(member), () -> this.getApiWorkTaskMapper().updateToExec(taskId, WorkStatus.RUNNING.getByteValue()));
            // 初始化内存进度缓存
            LocalProgressCache.singleton().initProgress(this.context);
        }

        @Override
        public void complete(String member, String taskId, WorkResult workResult) {
            // 更新任务状态
            WorkStatus status = workResult.success() ? WorkStatus.COMPLETED : WorkStatus.FAILED;
            DBSwitchUtil.doDBWithContext(SwitchDbContext.buildWDGJ(member), () -> this.getApiWorkTaskMapper().updateToFinish(taskId, status.getByteValue(), JsonUtils.toJson(workResult)));

            // 更新进度缓存,最后必须得移除redis进度缓存
            this.getWorkRunningCache().setProgressValue(COMPLETE_PROGRESS,true);
            LocalProgressCache.singleton().remove(this.context);

            // 任务结束，发布事件
            publishEvent(status, this.context);
        }

        /**
         * 更新子任务和动态状态数据
         *
         * @param subTask 子任务
         */
        @Override
        public void updateSubTask(S subTask) {
            this.getWorkRunningCache().updateSubTask(subTask);
        }

        @Override
        public void updateSubTask(S subTask, int increProgressValue) {
            // 更新任务状态信息
            this.getWorkRunningCache().updateSubTask(subTask, increProgressValue);
            // 任务进度变化,发布事件
            PageDynamicStatus dynamicStatus = (PageDynamicStatus) subTask.getDynamicStatus();
            publishEvent(dynamicStatus.getRunStatus().getWorkStatus(), this.context);
        }

        /**
         * 获取子任务的动态状态数据
         *
         * @param subTypeKey 子任务唯一键
         * @return 动态状态数据
         */
        @Override
        public S getSubTaskBreakData(String subTypeKey, Class<? extends S> clazz) {
            return (S) this.getWorkRunningCache().getSubTaskDynamicStatus(subTypeKey, clazz);
        }
        //endregion

        //region 私有方法
        /**
         * 工作任务数据库操作
         *
         * @return 工作任务数据库
         */
        private ApiWorkTaskMapper getApiWorkTaskMapper() {
            if (mapper == null) {
                mapper = BeanContextUtil.getBean(ApiWorkTaskMapper.class);
            }
            return mapper;
        }

        /**
         * 工作任务缓存操作
         *
         * @return 工作任务缓存
         */
        private WorkRunningCache getWorkRunningCache() {
            if (this.workRunningCache == null) {
                if (context == null) {
                    throw new RuntimeException("未初始化上下文");
                }
                this.workRunningCache = WorkRunningCache.build(context.getMember(), context.getWorkEnum(), context.getTaskId());
            }
            return this.workRunningCache;
        }

        /**
         * 发布工作任务变化事件
         *
         * @param status 状态
         * @param context 上下文
         */
        protected void publishEvent(WorkStatus status, DataOperateContext context) {
            this.getEventHandler().handle(new WorkChangeEvent(context, status));
        }

        /**
         * 获取事件处理器
         *
         * @return 事件处理器
         */
        private WorkEventHandler getEventHandler() {
            if (this.eventHandler == null) {
                this.eventHandler = this.workEnum.getWorkFactory().createEventHandler();
            }
            return this.eventHandler;
        }
        //endregion
    }
}
