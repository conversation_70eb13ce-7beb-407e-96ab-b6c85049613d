package com.differ.wdgj.api.user.biz.infrastructure.work.operate;


import com.differ.wdgj.api.user.biz.infrastructure.work.data.DataOperateContext;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.SubTask;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;

/**
 * 工作任务的操作
 *
 * <AUTHOR>
 * @date 2024/6/24 10:41
 */
public interface WorkSubTaskOperate<S extends SubTask> extends WorkDataOperate {
    /**
     * 获取工作任务的更新操作器
     *
     * @param context 上下文
     * @return 工作任务的更新操作器
     */
    WorkDetailOperate<S> getDetailOperate(DataOperateContext context);

    /**
     * 查询工作任务
     *
     * @param memberName 会员名
     * @param taskId     任务Id
     * @return 工作任务
     */
    WorkData<?> getWork(String memberName, String taskId);
}
