<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.WdgjStoredProcedureMapper">

    <!-- 商品匹配 -->
    <resultMap id="matchOrderGoodsResultMap"
               type="com.differ.wdgj.api.user.biz.infrastructure.data.dto.wdgj.MatchOrderGoodsResultDto">
        <result column="goodsID" property="goodsId" jdbcType="TINYINT"/>
        <result column="specID" property="specId" jdbcType="TINYINT"/>
        <result column="bfit" property="bFit" jdbcType="TINYINT"/>
        <result column="bCycle" property="bCycle" jdbcType="BIT"/>
    </resultMap>
    <!-- 商品匹配 -->
    <select id="matchOrderGoods" statementType="CALLABLE" resultMap="matchOrderGoodsResultMap">
        { CALL g_AutoGetGoodsIDspecID(
        #{tradeGoodsNo, mode=IN, jdbcType=VARCHAR},
        #{goodsName, mode=IN, jdbcType=VARCHAR},
        #{goodsSpec, mode=IN, jdbcType=VARCHAR},
        #{shopId, mode=IN, jdbcType=INTEGER}
        ) }
    </select>

    <!-- 售后单变更通知 -->
    <select id="afterSaleChangeNotice" statementType="CALLABLE" resultMap="matchOrderGoodsResultMap">
        { CALL G_GetReturnTradeInfo_new(
        #{billId, mode=IN, jdbcType=VARCHAR},
        #{noticeTypes, mode=IN, jdbcType=VARCHAR}
        ) }
    </select>

    <!-- 计算普通商品库存 -->
    <select id="calculationNormalStock" statementType="CALLABLE" resultMap="matchOrderGoodsResultMap">
        { CALL G_GetReturnTradeInfo_new(
        #{billId, mode=IN, jdbcType=VARCHAR},
        #{noticeTypes, mode=IN, jdbcType=VARCHAR}
        ) }
    </select>

    <!-- 组合商品库存计算 -->
    <select id="calculationFitStock" statementType="CALLABLE" resultMap="matchOrderGoodsResultMap">
        { CALL G_GetReturnTradeInfo_new(
        #{billId, mode=IN, jdbcType=VARCHAR},
        #{noticeTypes, mode=IN, jdbcType=VARCHAR}
        ) }
    </select>
</mapper>