package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.mode;

import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockDetailCountTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.GoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.GoodsStockCalculationDto;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.multi.adapter.core.IMultiWarehouseAdapter;
import com.differ.wdgj.api.user.biz.domain.stock.subdomain.calculation.IErpGoodsStockCalculationService;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.mode.plugins.FitStockCalculationMode;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.mode.plugins.NormalStockCalculationMode;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ShopConfigStockSyncRuleEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 库存计算模式集成测试
 * 测试不同计算模式的集成功能
 *
 * <AUTHOR>
 * @date 2025/6/17 11:20
 */
@SpringBootTest
@ActiveProfiles("test")
public class StockCalculationModeIntegrationTest {

    private StockSyncContext context;
    private GoodsStockCalculationDto calculationDto;

    @BeforeEach
    public void setUp() {
        // 创建测试上下文
        context = createTestContext();
        
        // 创建计算参数
        calculationDto = createCalculationDto();
    }

    @Test
    public void testNormalStockCalculationMode_Integration() {
        // 测试普通库存计算模式集成
        NormalStockCalculationMode mode = new NormalStockCalculationMode(context);
        
        // 验证模式名称
        assertEquals("普通库存计算模式", mode.getModeName());
        
        // 注意：这里需要Mock ERP服务，因为集成测试不应该依赖真实的外部服务
        // 在实际项目中，应该使用TestContainers或者Mock服务
        
        // 验证基本功能
        assertNotNull(mode);
        assertTrue(mode instanceof IStockCalculationMode);
        assertTrue(mode instanceof AbstractStockCalculationMode);
    }

    @Test
    public void testFitStockCalculationMode_Integration() {
        // 测试组合商品库存计算模式集成
        FitStockCalculationMode mode = new FitStockCalculationMode(context);
        
        // 验证模式名称
        assertEquals("组合商品库存计算模式", mode.getModeName());
        
        // 验证基本功能
        assertNotNull(mode);
        assertTrue(mode instanceof IStockCalculationMode);
        assertTrue(mode instanceof AbstractStockCalculationMode);
    }

    @Test
    public void testModeFactory_Integration() {
        // 测试模式工厂集成
        
        // 测试创建普通模式
        IStockCalculationMode normalMode = createStockCalculationMode("NORMAL", context);
        assertNotNull(normalMode);
        assertEquals("普通库存计算模式", normalMode.getModeName());
        
        // 测试创建组合模式
        IStockCalculationMode fitMode = createStockCalculationMode("FIT", context);
        assertNotNull(fitMode);
        assertEquals("组合商品库存计算模式", fitMode.getModeName());
    }

    @Test
    public void testModeChain_Integration() {
        // 测试模式链集成 - 模拟不同商品类型使用不同模式
        
        // 普通商品使用普通模式
        calculationDto.setGoodsType(1); // 普通商品
        IStockCalculationMode normalMode = selectModeByGoodsType(calculationDto.getGoodsType(), context);
        assertTrue(normalMode instanceof NormalStockCalculationMode);
        
        // 组合商品使用组合模式
        calculationDto.setGoodsType(2); // 组合商品
        IStockCalculationMode fitMode = selectModeByGoodsType(calculationDto.getGoodsType(), context);
        assertTrue(fitMode instanceof FitStockCalculationMode);
    }

    @Test
    public void testModeWithDifferentPlatforms_Integration() {
        // 测试不同平台的模式集成
        
        // 淘宝平台
        context.setPlat(PolyPlatEnum.BUSINESS_Taobao);
        IStockCalculationMode taobaoMode = new NormalStockCalculationMode(context);
        assertEquals("普通库存计算模式", taobaoMode.getModeName());
        
        // 京东平台
        context.setPlat(PolyPlatEnum.BUSINESS_JD);
        IStockCalculationMode jdMode = new NormalStockCalculationMode(context);
        assertEquals("普通库存计算模式", jdMode.getModeName());
        
        // 验证不同平台使用相同模式但上下文不同
        assertNotEquals(taobaoMode.hashCode(), jdMode.hashCode());
    }

    @Test
    public void testModeErrorHandling_Integration() {
        // 测试模式错误处理集成
        
        // 创建异常上下文
        StockSyncContext errorContext = new StockSyncContext();
        errorContext.setVipUser("errorUser");
        errorContext.setPlat(PolyPlatEnum.BUSINESS_Taobao);
        
        NormalStockCalculationMode mode = new NormalStockCalculationMode(errorContext);
        
        // 测试空参数处理
        GoodsStockCalculationResult result = mode.getActualStock(null);
        assertTrue(result.isFailed());
        assertTrue(result.getMessage().contains("errorUser"));
    }

    @Test
    public void testModePerformance_Integration() {
        // 测试模式性能集成
        
        NormalStockCalculationMode mode = new NormalStockCalculationMode(context);
        
        // 测试多次调用性能
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < 100; i++) {
            calculationDto.setErpGoodsId(1000 + i);
            // 注意：这里应该Mock服务调用以避免真实网络请求
            assertNotNull(mode.getModeName());
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 验证性能在合理范围内（这里只是示例，实际阈值需要根据业务需求设定）
        assertTrue(duration < 5000, "模式调用性能超出预期：" + duration + "ms");
    }

    /**
     * 创建测试上下文
     */
    private StockSyncContext createTestContext() {
        StockSyncContext context = new StockSyncContext();
        context.setVipUser("testUser");
        context.setShopId(12345);
        context.setPlat(PolyPlatEnum.BUSINESS_Taobao);
        
        // 设置店铺基础信息
        ApiShopBaseDto shopBase = new ApiShopBaseDto();
        shopBase.setShopId(12345);
        shopBase.setShopName("测试店铺");
        shopBase.setPlat(PolyPlatEnum.BUSINESS_Taobao);
        context.setShopBase(shopBase);
        
        // 设置库存同步配置
        SyncStockShopConfig syncConfig = new SyncStockShopConfig();
        context.setSyncStockConfig(syncConfig);
        
        return context;
    }

    /**
     * 创建计算参数
     */
    private GoodsStockCalculationDto createCalculationDto() {
        GoodsStockCalculationDto dto = new GoodsStockCalculationDto();
        dto.setShopId(12345);
        dto.setErpGoodsId(1001);
        dto.setErpSpecId(2001);
        dto.setGoodsType(1); // 普通商品
        dto.setErpWarehouseIds(Arrays.asList(101, 102, 103));
        dto.setShopConfigStockSyncRule(ShopConfigStockSyncRuleEnum.RULE_THREE);
        return dto;
    }

    /**
     * 模拟模式工厂方法
     */
    private IStockCalculationMode createStockCalculationMode(String modeType, StockSyncContext context) {
        switch (modeType) {
            case "NORMAL":
                return new NormalStockCalculationMode(context);
            case "FIT":
                return new FitStockCalculationMode(context);
            default:
                return new NormalStockCalculationMode(context);
        }
    }

    /**
     * 根据商品类型选择模式
     */
    private IStockCalculationMode selectModeByGoodsType(Integer goodsType, StockSyncContext context) {
        if (goodsType != null && goodsType == 2) {
            return new FitStockCalculationMode(context);
        }
        return new NormalStockCalculationMode(context);
    }
}
