package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.strategy;

import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockGoodsExtRuleTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.GoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.rule.StockCalculationRule;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.strategy.plugins.DefaultStockCalculationStrategy;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.PolyPlatEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 默认库存计算策略单元测试
 *
 * <AUTHOR>
 * @date 2025/6/17 11:00
 */
public class DefaultStockCalculationStrategyTest {

    @Mock
    private StockSyncContext mockContext;

    @Mock
    private StockCalculationRule mockRule;

    private DefaultStockCalculationStrategy strategy;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 设置上下文基本信息
        when(mockContext.getVipUser()).thenReturn("testUser");
        when(mockContext.getPlat()).thenReturn(PolyPlatEnum.BUSINESS_Taobao);
        when(mockContext.getShopId()).thenReturn(12345);
        
        strategy = new DefaultStockCalculationStrategy(mockContext);
    }

    @Test
    public void testGetStrategyName() {
        // 测试策略名称
        String strategyName = strategy.getStrategyName();
        assertEquals("默认库存计算策略", strategyName);
    }

    @Test
    public void testCalculateSyncQuantity_FixedQuantity() {
        // 测试固定数量计算
        when(mockRule.getExtRuleType()).thenReturn(StockGoodsExtRuleTypeEnum.FIX);
        when(mockRule.getFixedQuantity()).thenReturn(BigDecimal.valueOf(100));
        
        BigDecimal actualStock = BigDecimal.valueOf(200);
        GoodsStockCalculationResult result = strategy.calculateSyncQuantity(mockRule, actualStock);
        
        assertTrue(result.isSuccess());
        assertEquals(BigDecimal.valueOf(100), result.getStockCount());
    }

    @Test
    public void testCalculateSyncQuantity_ConditionalFixedQuantity_InRange() {
        // 测试条件固定数量计算 - 在范围内
        when(mockRule.getExtRuleType()).thenReturn(StockGoodsExtRuleTypeEnum.CONDITION_FIX);
        when(mockRule.getFixedQuantity()).thenReturn(BigDecimal.valueOf(50));
        when(mockRule.getMinStockQuantity()).thenReturn(BigDecimal.valueOf(10));
        when(mockRule.getMaxStockQuantity()).thenReturn(BigDecimal.valueOf(100));
        
        BigDecimal actualStock = BigDecimal.valueOf(50);
        GoodsStockCalculationResult result = strategy.calculateSyncQuantity(mockRule, actualStock);
        
        assertTrue(result.isSuccess());
        assertEquals(BigDecimal.valueOf(50), result.getStockCount());
    }

    @Test
    public void testCalculateSyncQuantity_ConditionalFixedQuantity_OutOfRange() {
        // 测试条件固定数量计算 - 超出范围
        when(mockRule.getExtRuleType()).thenReturn(StockGoodsExtRuleTypeEnum.CONDITION_FIX);
        when(mockRule.getFixedQuantity()).thenReturn(BigDecimal.valueOf(50));
        when(mockRule.getMinStockQuantity()).thenReturn(BigDecimal.valueOf(10));
        when(mockRule.getMaxStockQuantity()).thenReturn(BigDecimal.valueOf(100));
        when(mockRule.getPercent()).thenReturn(BigDecimal.valueOf(-1)); // 不使用百分比
        
        BigDecimal actualStock = BigDecimal.valueOf(150); // 超出范围
        GoodsStockCalculationResult result = strategy.calculateSyncQuantity(mockRule, actualStock);
        
        assertTrue(result.isSuccess());
        assertEquals(actualStock, result.getStockCount()); // 应该返回原始库存
    }

    @Test
    public void testCalculateSyncQuantity_PercentageCalculation_RoundUp() {
        // 测试百分比计算 - 向上取整
        when(mockRule.getExtRuleType()).thenReturn(null);
        when(mockRule.getPercent()).thenReturn(BigDecimal.valueOf(80)); // 80%
        when(mockRule.isbSyncPctStock()).thenReturn(true); // 向上取整
        
        BigDecimal actualStock = BigDecimal.valueOf(125); // 125 * 80% = 100
        GoodsStockCalculationResult result = strategy.calculateSyncQuantity(mockRule, actualStock);
        
        assertTrue(result.isSuccess());
        assertEquals(BigDecimal.valueOf(100), result.getStockCount());
    }

    @Test
    public void testCalculateSyncQuantity_PercentageCalculation_RoundDown() {
        // 测试百分比计算 - 向下取整
        when(mockRule.getExtRuleType()).thenReturn(null);
        when(mockRule.getPercent()).thenReturn(BigDecimal.valueOf(75)); // 75%
        when(mockRule.isbSyncPctStock()).thenReturn(false); // 向下取整
        
        BigDecimal actualStock = BigDecimal.valueOf(133); // 133 * 75% = 99.75 -> 99
        GoodsStockCalculationResult result = strategy.calculateSyncQuantity(mockRule, actualStock);
        
        assertTrue(result.isSuccess());
        assertEquals(BigDecimal.valueOf(99), result.getStockCount());
    }

    @Test
    public void testCalculateSyncQuantity_ConditionalFixedIncrease_InRange() {
        // 测试条件固定增加数量 - 在范围内
        when(mockRule.getExtRuleType()).thenReturn(StockGoodsExtRuleTypeEnum.CONDITION_FIX_INCREASE);
        when(mockRule.getPercent()).thenReturn(BigDecimal.valueOf(100)); // 100%
        when(mockRule.getIncrementalQuantity()).thenReturn(BigDecimal.valueOf(20));
        when(mockRule.getMinStockQuantity()).thenReturn(BigDecimal.valueOf(10));
        when(mockRule.getMaxStockQuantity()).thenReturn(BigDecimal.valueOf(100));
        when(mockRule.isbSyncPctStock()).thenReturn(false);
        
        BigDecimal actualStock = BigDecimal.valueOf(50);
        GoodsStockCalculationResult result = strategy.calculateSyncQuantity(mockRule, actualStock);
        
        assertTrue(result.isSuccess());
        assertEquals(BigDecimal.valueOf(70), result.getStockCount()); // 50 + 20
    }

    @Test
    public void testCalculateSyncQuantity_ConditionalFixedIncrease_OutOfRange() {
        // 测试条件固定增加数量 - 超出范围
        when(mockRule.getExtRuleType()).thenReturn(StockGoodsExtRuleTypeEnum.CONDITION_FIX_INCREASE);
        when(mockRule.getPercent()).thenReturn(BigDecimal.valueOf(100)); // 100%
        when(mockRule.getIncrementalQuantity()).thenReturn(BigDecimal.valueOf(20));
        when(mockRule.getMinStockQuantity()).thenReturn(BigDecimal.valueOf(10));
        when(mockRule.getMaxStockQuantity()).thenReturn(BigDecimal.valueOf(100));
        when(mockRule.isbSyncPctStock()).thenReturn(false);
        
        BigDecimal actualStock = BigDecimal.valueOf(150); // 超出范围
        GoodsStockCalculationResult result = strategy.calculateSyncQuantity(mockRule, actualStock);
        
        assertTrue(result.isSuccess());
        assertEquals(BigDecimal.valueOf(150), result.getStockCount()); // 不增加，保持原值
    }

    @Test
    public void testCalculateSyncQuantity_NoSpecialRule() {
        // 测试无特殊规则，只使用原始库存
        when(mockRule.getExtRuleType()).thenReturn(null);
        when(mockRule.getPercent()).thenReturn(BigDecimal.valueOf(-1)); // 不使用百分比
        
        BigDecimal actualStock = BigDecimal.valueOf(200);
        GoodsStockCalculationResult result = strategy.calculateSyncQuantity(mockRule, actualStock);
        
        assertTrue(result.isSuccess());
        assertEquals(actualStock, result.getStockCount());
    }

    @Test
    public void testCalculateSyncQuantity_Exception() {
        // 测试异常情况
        when(mockRule.getExtRuleType()).thenThrow(new RuntimeException("测试异常"));
        
        BigDecimal actualStock = BigDecimal.valueOf(100);
        GoodsStockCalculationResult result = strategy.calculateSyncQuantity(mockRule, actualStock);
        
        assertTrue(result.isFailed());
        assertTrue(result.getMessage().contains("测试异常"));
    }
}
