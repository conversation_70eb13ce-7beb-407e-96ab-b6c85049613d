package com.differ.wdgj.api.user.biz.infrastructure.utils.shop;

import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockNumRuleDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ShopConfigStockSyncRuleEnum;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 库存同步店铺配置工具类测试
 *
 * <AUTHOR>
 * @date 2025/6/17 10:30
 */
public class SyncStockShopConfigUtilsTest {

    @Test
    public void testGetShopConfigStockSyncRule_NullConfig() {
        // 测试空配置
        ShopConfigStockSyncRuleEnum result = SyncStockShopConfigUtils.getShopConfigStockSyncRule(null);
        assertNull(result);
    }

    @Test
    public void testGetShopConfigStockSyncRule_NullSyncNumRule() {
        // 测试空同步数量规则
        SyncStockShopConfig config = new SyncStockShopConfig();
        config.setSyncNumRule(null);
        
        ShopConfigStockSyncRuleEnum result = SyncStockShopConfigUtils.getShopConfigStockSyncRule(config);
        assertNull(result);
    }

    @Test
    public void testGetShopConfigStockSyncRule_RuleOne() {
        // 测试规则1：实际库存-未付款数量-订购量-待发货量
        SyncStockShopConfig config = createConfig(true, true, true, true, false, false);
        
        ShopConfigStockSyncRuleEnum result = SyncStockShopConfigUtils.getShopConfigStockSyncRule(config);
        assertEquals(ShopConfigStockSyncRuleEnum.RULE_ONE, result);
    }

    @Test
    public void testGetShopConfigStockSyncRule_RuleTwo() {
        // 测试规则2：实际库存-订购量-待发货量
        SyncStockShopConfig config = createConfig(true, false, true, true, false, false);
        
        ShopConfigStockSyncRuleEnum result = SyncStockShopConfigUtils.getShopConfigStockSyncRule(config);
        assertEquals(ShopConfigStockSyncRuleEnum.RULE_TWO, result);
    }

    @Test
    public void testGetShopConfigStockSyncRule_RuleThree() {
        // 测试规则3：实际库存
        SyncStockShopConfig config = createConfig(true, false, false, false, false, false);
        
        ShopConfigStockSyncRuleEnum result = SyncStockShopConfigUtils.getShopConfigStockSyncRule(config);
        assertEquals(ShopConfigStockSyncRuleEnum.RULE_THREE, result);
    }

    @Test
    public void testGetShopConfigStockSyncRule_RuleThirtyTwo() {
        // 测试规则32：实际库存-未付款数量+采购在途数量+调入在途数量-订购量-待发货量
        SyncStockShopConfig config = createConfig(true, true, true, true, true, true);
        
        ShopConfigStockSyncRuleEnum result = SyncStockShopConfigUtils.getShopConfigStockSyncRule(config);
        assertEquals(ShopConfigStockSyncRuleEnum.RULE_THIRTYTWO, result);
    }

    @Test
    public void testGetShopConfigStockSyncRule_NoMatch() {
        // 测试没有匹配的规则（实际库存为false）
        SyncStockShopConfig config = createConfig(false, true, true, true, true, true);
        
        ShopConfigStockSyncRuleEnum result = SyncStockShopConfigUtils.getShopConfigStockSyncRule(config);
        assertNull(result);
    }

    /**
     * 创建测试配置
     */
    private SyncStockShopConfig createConfig(boolean isActualStock, boolean isUnPayNum, 
                                           boolean isOrderQuantity, boolean isWaitSend,
                                           boolean isPurchaseOnWayNum, boolean isTransferOnWayNum) {
        SyncStockShopConfig config = new SyncStockShopConfig();
        SyncStockNumRuleDto syncNumRule = new SyncStockNumRuleDto();
        
        syncNumRule.setIsActualStock(isActualStock);
        syncNumRule.setIsUnPayNum(isUnPayNum);
        syncNumRule.setIsOrderQuantity(isOrderQuantity);
        syncNumRule.setIsWaitSend(isWaitSend);
        syncNumRule.setIsPurchaseOnWayNum(isPurchaseOnWayNum);
        syncNumRule.setIsTransferOnWayNum(isTransferOnWayNum);
        
        config.setSyncNumRule(syncNumRule);
        return config;
    }
}
