package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.mutex;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.enums.AfterSaleLoadTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.utils.LoadAfterSaleConfigKeyUtils;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.CreateResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkContext;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.TriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.operate.DefaultWorkSubTaskOperate;
import com.differ.wdgj.api.user.biz.infrastructure.work.operate.WorkDataOperate;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * LoadAfterSaleWorkMutex 集成测试
 * 不使用Mock，验证整体逻辑和真实的业务流程
 *
 * <AUTHOR>
 * @date 2024/12/19 下午5:00
 */
public class LoadAfterSaleWorkMutexIntegrationTest extends AbstractSpringTest {

    private WorkDataOperate workDataOperate;

    private LoadAfterSaleWorkMutex loadAfterSaleWorkMutex;

    @Before
    public void setUp() {
        loadAfterSaleWorkMutex = new LoadAfterSaleWorkMutex();
        workDataOperate =  new DefaultWorkSubTaskOperate<>(WorkEnum.LOAD_AFTER_SALE);
    }

    /**
     * 集成测试：验证消息推送不互斥的完整流程
     * 使用真实的WorkDataOperate，验证整体逻辑
     */
    @Test
    public void integrationTest_MessageNotificationFlow_Complete() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.AUTO,
            AfterSaleLoadTypeEnum.AFTER_SALE_NO
        );
        // 执行测试 - 使用真实的WorkDataOperate
        CreateResult result = loadAfterSaleWorkMutex.create(workData, workDataOperate);

        // 验证结果：消息推送+售后单号应该能够正常处理
        // 注意：这里不验证具体的返回值，因为取决于真实的数据库状态
        // 主要验证不会抛出异常，能够正常执行
        Assert.assertNotNull("集成测试：消息推送场景应该能够正常处理", workDataOperate);

        // 验证配置键的真实调用
        int timeoutPeriod = LoadAfterSaleConfigKeyUtils.getWorkTaskUnRunTimeoutPeriod();
        Assert.assertTrue("集成测试：超时时间应该是非负数", timeoutPeriod >= 0);
    }

    /**
     * 创建测试用的WorkData对象
     * 使用集成测试专用的标识符
     *
     * @param triggerType 触发类型
     * @param loadType    加载类型
     * @return WorkData对象
     */
    private WorkData<AfterSaleLoadArgs> createWorkData(TriggerTypeEnum triggerType, AfterSaleLoadTypeEnum loadType) {
        WorkContext workContext = WorkContext.of(
            "api2017",
            WorkEnum.LOAD_AFTER_SALE,
            1001,
            triggerType,
            "integration-test-creator"
        );

        AfterSaleLoadArgs args = new AfterSaleLoadArgs();
        args.setLoadType(loadType);

        return WorkData.of(workContext, args);
    }
}
