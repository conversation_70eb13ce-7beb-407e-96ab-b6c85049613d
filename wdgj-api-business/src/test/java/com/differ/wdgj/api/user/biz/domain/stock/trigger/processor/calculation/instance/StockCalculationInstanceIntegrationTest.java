package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.instance;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockDetailCountTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.GoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.mode.IStockCalculationMode;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.rule.StockCalculationRule;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.strategy.IStockCalculationStrategy;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockShopConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 库存计算实例集成测试
 * 测试计算实例与计算模式、计算策略的集成功能
 *
 * <AUTHOR>
 * @date 2025/6/17 11:30
 */
public class StockCalculationInstanceIntegrationTest extends AbstractSpringTest {

    @Mock
    private IStockCalculationMode mockCalculationMode;

    @Mock
    private IStockCalculationStrategy mockCalculationStrategy;

    @Mock
    private StockCalculationRule mockRule;

    private StockSyncContext context;
    private StockCalculationInstance calculationInstance;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        // 创建测试上下文
        context = createTestContext();
        
        // 创建计算实例
        calculationInstance = new StockCalculationInstance(context, mockCalculationMode, mockCalculationStrategy);
    }

    /**
     * 测试构造函数 - 有效参数
     */
    @Test
    public void testConstructor_ValidParameters() {
        assertNotNull(calculationInstance);
        assertEquals(mockCalculationMode, calculationInstance.getCalculationMode());
        assertEquals(mockCalculationStrategy, calculationInstance.getCalculationStrategy());
    }

    /**
     * 测试构造函数 - 空上下文
     */
    @Test
    public void testConstructor_NullContext() {
        assertThrows(IllegalArgumentException.class, () -> {
            new StockCalculationInstance(null, mockCalculationMode, mockCalculationStrategy);
        });
    }

    @Test
    public void testConstructor_NullCalculationMode() {
        // 测试构造函数 - 空计算模式
        assertThrows(IllegalArgumentException.class, () -> {
            new StockCalculationInstance(context, null, mockCalculationStrategy);
        });
    }

    @Test
    public void testConstructor_NullCalculationStrategy() {
        // 测试构造函数 - 空计算策略
        assertThrows(IllegalArgumentException.class, () -> {
            new StockCalculationInstance(context, mockCalculationMode, null);
        });
    }

    @Test
    public void testCalculateSyncQuantity_Success() {
        // 测试成功计算同步数量
        
        // 设置模式返回结果
        BigDecimal actualStock = BigDecimal.valueOf(100);
        Map<StockDetailCountTypeEnum, BigDecimal> actualStockDetailMap = new HashMap<>();
        actualStockDetailMap.put(StockDetailCountTypeEnum.ACTUAL_STOCK, actualStock);
        GoodsStockCalculationResult actualStockResult = GoodsStockCalculationResult.success(actualStock, actualStockDetailMap);
        when(mockCalculationMode.getActualStock(any())).thenReturn(actualStockResult);
        
        // 设置策略返回结果
        BigDecimal syncQuantity = BigDecimal.valueOf(80);
        Map<StockDetailCountTypeEnum, BigDecimal> syncDetailMap = new HashMap<>();
        syncDetailMap.put(StockDetailCountTypeEnum.SYNC_QUANTITY, syncQuantity);
        GoodsStockCalculationResult syncResult = GoodsStockCalculationResult.success(syncQuantity, syncDetailMap);
        when(mockCalculationStrategy.calculateSyncQuantity(any(), eq(actualStock))).thenReturn(syncResult);
        
        // 执行计算
        GoodsStockCalculationResult result = calculationInstance.calculateSyncQuantity(mockRule);
        
        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(syncQuantity, result.getStockCount());
        assertNotNull(result.getStockDetailMap());
        assertEquals(2, result.getStockDetailMap().size());
        assertTrue(result.getStockDetailMap().containsKey(StockDetailCountTypeEnum.ACTUAL_STOCK));
        assertTrue(result.getStockDetailMap().containsKey(StockDetailCountTypeEnum.SYNC_QUANTITY));
        
        // 验证调用
        verify(mockCalculationMode, times(1)).getActualStock(any());
        verify(mockCalculationStrategy, times(1)).calculateSyncQuantity(any(), eq(actualStock));
    }

    @Test
    public void testCalculateSyncQuantity_ActualStockFailed() {
        // 测试获取实际库存失败
        
        String errorMessage = "获取实际库存失败";
        GoodsStockCalculationResult failedResult = GoodsStockCalculationResult.failed(errorMessage);
        when(mockCalculationMode.getActualStock(any())).thenReturn(failedResult);
        
        // 执行计算
        GoodsStockCalculationResult result = calculationInstance.calculateSyncQuantity(mockRule);
        
        // 验证结果
        assertTrue(result.isFailed());
        assertTrue(result.getMessage().contains("获取实际库存失败"));
        assertTrue(result.getMessage().contains(errorMessage));
        
        // 验证调用
        verify(mockCalculationMode, times(1)).getActualStock(any());
        verify(mockCalculationStrategy, never()).calculateSyncQuantity(any(), any());
    }

    @Test
    public void testCalculateSyncQuantity_StrategyCalculationFailed() {
        // 测试策略计算失败
        
        // 设置模式返回成功结果
        BigDecimal actualStock = BigDecimal.valueOf(100);
        GoodsStockCalculationResult actualStockResult = GoodsStockCalculationResult.success(actualStock);
        when(mockCalculationMode.getActualStock(any())).thenReturn(actualStockResult);
        
        // 设置策略返回失败结果
        String errorMessage = "策略计算失败";
        GoodsStockCalculationResult failedResult = GoodsStockCalculationResult.failed(errorMessage);
        when(mockCalculationStrategy.calculateSyncQuantity(any(), eq(actualStock))).thenReturn(failedResult);
        
        // 执行计算
        GoodsStockCalculationResult result = calculationInstance.calculateSyncQuantity(mockRule);
        
        // 验证结果
        assertTrue(result.isFailed());
        assertTrue(result.getMessage().contains("计算同步数量失败"));
        assertTrue(result.getMessage().contains(errorMessage));
        
        // 验证调用
        verify(mockCalculationMode, times(1)).getActualStock(any());
        verify(mockCalculationStrategy, times(1)).calculateSyncQuantity(any(), eq(actualStock));
    }

    @Test
    public void testCalculateSyncQuantity_Exception() {
        // 测试异常处理
        
        when(mockCalculationMode.getActualStock(any())).thenThrow(new RuntimeException("模式异常"));
        
        // 执行计算
        GoodsStockCalculationResult result = calculationInstance.calculateSyncQuantity(mockRule);
        
        // 验证结果
        assertTrue(result.isFailed());
        assertTrue(result.getMessage().contains("testUser"));
        assertTrue(result.getMessage().contains("计算同步数量异常"));
        assertTrue(result.getMessage().contains("模式异常"));
        
        // 验证调用
        verify(mockCalculationMode, times(1)).getActualStock(any());
    }

    @Test
    public void testCalculateSyncQuantity_ComplexScenario() {
        // 测试复杂场景集成
        
        // 设置复杂的实际库存结果
        BigDecimal actualStock = BigDecimal.valueOf(200);
        Map<StockDetailCountTypeEnum, BigDecimal> actualDetailMap = new HashMap<>();
        actualDetailMap.put(StockDetailCountTypeEnum.ACTUAL_STOCK, actualStock);
        actualDetailMap.put(StockDetailCountTypeEnum.UNPAID_QUANTITY, BigDecimal.valueOf(10));
        actualDetailMap.put(StockDetailCountTypeEnum.ORDER_QUANTITY, BigDecimal.valueOf(5));
        GoodsStockCalculationResult actualStockResult = GoodsStockCalculationResult.success(actualStock, actualDetailMap);
        when(mockCalculationMode.getActualStock(any())).thenReturn(actualStockResult);
        
        // 设置复杂的策略计算结果
        BigDecimal syncQuantity = BigDecimal.valueOf(150);
        Map<StockDetailCountTypeEnum, BigDecimal> syncDetailMap = new HashMap<>();
        syncDetailMap.put(StockDetailCountTypeEnum.SYNC_QUANTITY, syncQuantity);
        syncDetailMap.put(StockDetailCountTypeEnum.PERCENTAGE_APPLIED, BigDecimal.valueOf(75)); // 75%
        GoodsStockCalculationResult syncResult = GoodsStockCalculationResult.success(syncQuantity, syncDetailMap);
        when(mockCalculationStrategy.calculateSyncQuantity(any(), eq(actualStock))).thenReturn(syncResult);
        
        // 执行计算
        GoodsStockCalculationResult result = calculationInstance.calculateSyncQuantity(mockRule);
        
        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(syncQuantity, result.getStockCount());
        
        // 验证详情合并
        Map<StockDetailCountTypeEnum, BigDecimal> resultDetailMap = result.getStockDetailMap();
        assertEquals(5, resultDetailMap.size()); // 3个实际库存详情 + 2个同步详情
        assertEquals(actualStock, resultDetailMap.get(StockDetailCountTypeEnum.ACTUAL_STOCK));
        assertEquals(BigDecimal.valueOf(10), resultDetailMap.get(StockDetailCountTypeEnum.UNPAID_QUANTITY));
        assertEquals(BigDecimal.valueOf(5), resultDetailMap.get(StockDetailCountTypeEnum.ORDER_QUANTITY));
        assertEquals(syncQuantity, resultDetailMap.get(StockDetailCountTypeEnum.SYNC_QUANTITY));
        assertEquals(BigDecimal.valueOf(75), resultDetailMap.get(StockDetailCountTypeEnum.PERCENTAGE_APPLIED));
    }

    @Test
    public void testCalculateSyncQuantity_ZeroStock() {
        // 测试零库存场景
        
        BigDecimal zeroStock = BigDecimal.ZERO;
        GoodsStockCalculationResult actualStockResult = GoodsStockCalculationResult.success(zeroStock);
        when(mockCalculationMode.getActualStock(any())).thenReturn(actualStockResult);
        
        GoodsStockCalculationResult syncResult = GoodsStockCalculationResult.success(zeroStock);
        when(mockCalculationStrategy.calculateSyncQuantity(any(), eq(zeroStock))).thenReturn(syncResult);
        
        // 执行计算
        GoodsStockCalculationResult result = calculationInstance.calculateSyncQuantity(mockRule);
        
        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(zeroStock, result.getStockCount());
    }

    @Test
    public void testCalculateSyncQuantity_LargeStock() {
        // 测试大库存场景
        
        BigDecimal largeStock = BigDecimal.valueOf(999999);
        GoodsStockCalculationResult actualStockResult = GoodsStockCalculationResult.success(largeStock);
        when(mockCalculationMode.getActualStock(any())).thenReturn(actualStockResult);
        
        BigDecimal largeSyncQuantity = BigDecimal.valueOf(799999); // 80%
        GoodsStockCalculationResult syncResult = GoodsStockCalculationResult.success(largeSyncQuantity);
        when(mockCalculationStrategy.calculateSyncQuantity(any(), eq(largeStock))).thenReturn(syncResult);
        
        // 执行计算
        GoodsStockCalculationResult result = calculationInstance.calculateSyncQuantity(mockRule);
        
        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(largeSyncQuantity, result.getStockCount());
    }

    /**
     * 创建测试上下文
     */
    private StockSyncContext createTestContext() {
        StockSyncContext context = new StockSyncContext();
        context.setVipUser("testUser");
        context.setShopId(12345);
        context.setPlat(PolyPlatEnum.BUSINESS_Taobao);
        
        // 设置店铺基础信息
        ApiShopBaseDto shopBase = new ApiShopBaseDto();
        shopBase.setShopId(12345);
        shopBase.setShopName("测试店铺");
        shopBase.setPlat(PolyPlatEnum.BUSINESS_Taobao);
        context.setShopBase(shopBase);
        
        // 设置库存同步配置
        SyncStockShopConfig syncConfig = new SyncStockShopConfig();
        context.setSyncStockConfig(syncConfig);
        
        return context;
    }
}
