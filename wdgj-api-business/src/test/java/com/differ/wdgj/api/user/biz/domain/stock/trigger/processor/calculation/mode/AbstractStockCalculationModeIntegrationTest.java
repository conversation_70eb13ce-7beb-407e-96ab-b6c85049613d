package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.mode;

import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.GoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.GoodsStockCalculationDto;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.subdomain.calculation.IErpGoodsStockCalculationService;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.mode.plugins.NormalStockCalculationMode;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ShopConfigStockSyncRuleEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 抽象库存计算模式集成测试
 * 测试抽象基类的通用功能
 *
 * <AUTHOR>
 * @date 2025/6/17 11:50
 */
public class AbstractStockCalculationModeIntegrationTest {
    private StockSyncContext context;
    private GoodsStockCalculationDto calculationDto;

    @BeforeEach
    public void setUp() {
        context = createTestContext();
        calculationDto = createCalculationDto();
    }

    /**
     * 测试抽象模式错误处理
     */
    @Test
    public void testAbstractMode_ErrorHandling() {
        TestableAbstractStockCalculationMode mode = new TestableAbstractStockCalculationMode(context);
        mode.setThrowException(true);
        
        // 执行计算
        GoodsStockCalculationResult result = mode.getActualStock(calculationDto);
        
        // 验证错误处理
        assertTrue(result.isFailed());
        assertTrue(result.getMessage().contains("testUser"));
        assertTrue(result.getMessage().contains("测试模式"));
        assertTrue(result.getMessage().contains("测试异常"));
    }

    /**
     * 测试抽象模式成功流程
     */
    @Test
    public void testAbstractMode_SuccessFlow() {
        TestableAbstractStockCalculationMode mode = new TestableAbstractStockCalculationMode(context);
        mode.setMockResult(GoodsStockCalculationResult.success(java.math.BigDecimal.valueOf(100), new HashMap<>()));
        
        // 执行计算
        GoodsStockCalculationResult result = mode.getActualStock(calculationDto);
        
        // 验证成功流程
        assertTrue(result.isSuccess());
        assertEquals(java.math.BigDecimal.valueOf(100), result.getStockCount());
        assertTrue(mode.isDoGetActualStockCalled());
    }

    /**
     * 测试上下文验证
     */
    @Test
    public void testAbstractMode_ContextValidation() {
        // 测试空上下文
        assertThrows(IllegalArgumentException.class, () -> {
            new TestableAbstractStockCalculationMode(null);
        });

        // 测试有效上下文
        TestableAbstractStockCalculationMode mode = new TestableAbstractStockCalculationMode(context);
        assertNotNull(mode);
        assertEquals("测试模式", mode.getModeName());
    }

    /**
     * 测试多次调用一致性
     */
    @Test
    public void testAbstractMode_MultipleCallsConsistency() {
        TestableAbstractStockCalculationMode mode = new TestableAbstractStockCalculationMode(context);
        mode.setMockResult(GoodsStockCalculationResult.success(java.math.BigDecimal.valueOf(50), new HashMap<>()));
        
        // 多次调用
        GoodsStockCalculationResult result1 = mode.getActualStock(calculationDto);
        GoodsStockCalculationResult result2 = mode.getActualStock(calculationDto);
        GoodsStockCalculationResult result3 = mode.getActualStock(calculationDto);
        
        // 验证一致性
        assertTrue(result1.isSuccess());
        assertTrue(result2.isSuccess());
        assertTrue(result3.isSuccess());
        assertEquals(result1.getStockCount(), result2.getStockCount());
        assertEquals(result2.getStockCount(), result3.getStockCount());
        assertEquals(3, mode.getCallCount());
    }

    /**
     * 测试性能基线
     */
    @Test
    public void testAbstractMode_PerformanceBaseline() {
        TestableAbstractStockCalculationMode mode = new TestableAbstractStockCalculationMode(context);
        mode.setMockResult(GoodsStockCalculationResult.success(java.math.BigDecimal.valueOf(100), new HashMap<>()));
        
        long startTime = System.currentTimeMillis();
        
        // 执行多次计算
        for (int i = 0; i < 1000; i++) {
            GoodsStockCalculationResult result = mode.getActualStock(calculationDto);
            assertTrue(result.isSuccess());
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 验证性能在合理范围内
        assertTrue(duration < 10000, "抽象模式性能超出预期：" + duration + "ms");
        assertEquals(1000, mode.getCallCount());
    }

    //region 私有方法
    /**
     * 创建测试上下文
     */
    private StockSyncContext createTestContext() {
        StockSyncContext context = new StockSyncContext();
        context.setVipUser("testUser");
        context.setShopId(12345);
        context.setPlat(PolyPlatEnum.BUSINESS_Taobao);
        
        ApiShopBaseDto shopBase = new ApiShopBaseDto();
        shopBase.setShopId(12345);
        shopBase.setShopName("测试店铺");
        shopBase.setPlat(PolyPlatEnum.BUSINESS_Taobao);
        context.setShopBase(shopBase);
        
        SyncStockShopConfig syncConfig = new SyncStockShopConfig();
        context.setSyncStockConfig(syncConfig);
        
        return context;
    }

    /**
     * 创建计算参数
     */
    private GoodsStockCalculationDto createCalculationDto() {
        GoodsStockCalculationDto dto = new GoodsStockCalculationDto();
        dto.setShopId(12345);
        dto.setErpGoodsId(1001);
        dto.setErpSpecId(2001);
        dto.setGoodsType(1);
        dto.setErpWarehouseIds(Arrays.asList(101, 102, 103));
        dto.setShopConfigStockSyncRule(ShopConfigStockSyncRuleEnum.RULE_THREE);
        return dto;
    }

    /**
     * 可测试的抽象模式实现
     */
    private static class TestableAbstractStockCalculationMode extends AbstractStockCalculationMode {
        private GoodsStockCalculationResult mockResult;
        private boolean throwException = false;
        private boolean doGetActualStockCalled = false;
        private int callCount = 0;

        public TestableAbstractStockCalculationMode(StockSyncContext context) {
            super(context);
        }

        @Override
        protected GoodsStockCalculationResult doGetActualStock(GoodsStockCalculationDto calculateParam) {
            doGetActualStockCalled = true;
            callCount++;
            
            if (throwException) {
                throw new RuntimeException("测试异常");
            }
            
            return mockResult != null ? mockResult : GoodsStockCalculationResult.success(java.math.BigDecimal.valueOf(100), new HashMap<>());
        }

        @Override
        public String getModeName() {
            return "测试模式";
        }

        public void setMockResult(GoodsStockCalculationResult result) {
            this.mockResult = result;
        }

        public void setThrowException(boolean throwException) {
            this.throwException = throwException;
        }

        public boolean isDoGetActualStockCalled() {
            return doGetActualStockCalled;
        }

        public int getCallCount() {
            return callCount;
        }
    }

    /**
     * 注入Mock服务（使用反射）
     */
    private void injectMockService(NormalStockCalculationMode mode, IErpGoodsStockCalculationService mockService) {
        try {
            java.lang.reflect.Field field = mode.getClass().getSuperclass().getDeclaredField("erpGoodsStockCalculationService");
            field.setAccessible(true);
            field.set(mode, mockService);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject mock service", e);
        }
    }
    //endregion
}
