package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.strategy;

import com.differ.wdgj.api.user.biz.domain.stock.data.result.GoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.rule.StockCalculationRule;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 库存计算策略接口测试
 * 测试策略接口的基本契约
 *
 * <AUTHOR>
 * @date 2025/6/17 11:40
 */
public class IStockCalculationStrategyTest {

    @Test
    public void testStrategyInterface_Contract() {
        // 测试策略接口契约
        
        // 创建测试实现
        TestStockCalculationStrategy strategy = new TestStockCalculationStrategy();
        
        // 验证接口方法存在
        assertNotNull(strategy.getStrategyName());
        assertFalse(strategy.getStrategyName().isEmpty());
        
        // 验证计算方法存在
        StockCalculationRule mockRule = null; // 在实际测试中应该创建mock对象
        BigDecimal actualStock = BigDecimal.valueOf(100);
        
        // 这里只验证方法签名，具体实现在具体策略类中测试
        assertDoesNotThrow(() -> {
            strategy.calculateSyncQuantity(mockRule, actualStock);
        });
    }

    @Test
    public void testStrategyInterface_PolymorphismSupport() {
        // 测试策略接口多态支持
        
        IStockCalculationStrategy strategy1 = new TestStockCalculationStrategy();
        IStockCalculationStrategy strategy2 = new AnotherTestStockCalculationStrategy();
        
        // 验证多态调用
        assertNotEquals(strategy1.getStrategyName(), strategy2.getStrategyName());
        
        // 验证接口类型
        assertTrue(strategy1 instanceof IStockCalculationStrategy);
        assertTrue(strategy2 instanceof IStockCalculationStrategy);
    }

    /**
     * 测试策略实现1
     */
    private static class TestStockCalculationStrategy implements IStockCalculationStrategy {
        @Override
        public GoodsStockCalculationResult calculateSyncQuantity(StockCalculationRule rule, BigDecimal actualStock) {
            return GoodsStockCalculationResult.success(actualStock);
        }

        @Override
        public String getStrategyName() {
            return "测试策略1";
        }
    }

    /**
     * 测试策略实现2
     */
    private static class AnotherTestStockCalculationStrategy implements IStockCalculationStrategy {
        @Override
        public GoodsStockCalculationResult calculateSyncQuantity(StockCalculationRule rule, BigDecimal actualStock) {
            return GoodsStockCalculationResult.success(actualStock.multiply(BigDecimal.valueOf(0.8)));
        }

        @Override
        public String getStrategyName() {
            return "测试策略2";
        }
    }
}
