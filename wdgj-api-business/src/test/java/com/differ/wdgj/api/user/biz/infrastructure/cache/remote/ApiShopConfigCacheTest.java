package com.differ.wdgj.api.user.biz.infrastructure.cache.remote;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.shop.ApiShopConfigCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.ApiShopConfigDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiShopConfigBizTypes;
import org.junit.Test;

/**
 * 会员店铺配置缓存
 *
 * <AUTHOR>
 * @date 2024-06-25 18:50
 */
//@Ignore
public class ApiShopConfigCacheTest extends AbstractSpringTest {

    /**
     * 查询
     */
    @Test
    public void getDataTest(){
        ApiShopConfigDto api2017 = ApiShopConfigCache.create("api2017").getData(1292, ApiShopConfigBizTypes.AFTER_SALES);
    }

    /**
     * 清除
     */
    @Test
    public void clearTest(){
        ApiShopConfigCache.create("api2017").clearCache(1292, ApiShopConfigBizTypes.AFTER_SALES);
    }

}
